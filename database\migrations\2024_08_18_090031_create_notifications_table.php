<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('icon')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('for')->nullable();
            $table->string('type')->nullable();
            $table->string('title')->nullable();
            $table->text('notice')->nullable();
            $table->string('action_url')->nullable();
            $table->boolean('read')->default(false)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
