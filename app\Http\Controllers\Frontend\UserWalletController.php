<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\CoinStatus;
use App\Http\Controllers\Controller;
use App\Models\Coin;
use App\Models\DepositMethod;
use App\Models\UserWallet;
use App\Models\WithdrawAccount;
use App\Models\WithdrawMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class UserWalletController extends Controller
{
    public function index()
    {

        $wallets = UserWallet::query()
            ->with('coin')
            ->where('user_id', Auth::user()->id)
            ->oldest();

        $currencies = Coin::query()
            ->where('status', CoinStatus::Active)
            ->whereNotIn('id', $wallets->pluck('coin_id'))
            ->get();

        $userWallets = $wallets->get();

        return view('frontend::user.wallet.index', ['userWallets' => $userWallets, 'currencies' => $currencies]);
    }

    public function create()
    {
        $wallets = UserWallet::query()
            ->with('coin')
            ->where('user_id', Auth::user()->id)
            ->oldest();

        $currencies = Coin::query()
            ->where('status', CoinStatus::Active)
            ->whereNotIn('id', $wallets->pluck('coin_id'))
            ->get();

        return view('frontend::user.wallet.create', ['currencies' => $currencies]);
    }

    public function store(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'currency_id' => 'required|exists:coins,id',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back();
        }

        $existUserWallet = UserWallet::query()
            ->where('user_id', $user->id)
            ->where('coin_id', $request->currency_id)
            ->get();

        if ($existUserWallet->count() > 0) {
            notify()->error(__('Already user wallet created with this currency'));

            return back();
        }

        try {
            DB::beginTransaction();

            $userWallet = new UserWallet;
            $userWallet->user_id = $user->id;
            $userWallet->coin_id = $request->currency_id;
            $userWallet->balance = 0;
            $userWallet->save();

            DB::commit();

            notify()->success(__('User wallet successfully created'));

            return back();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            notify()->error(__('An error occurred while creating the wallet. Please try again.'));

            return back();
        }
    }

    public function delete($id)
    {
        $user = Auth::user();

        $userWallet = UserWallet::query()
            ->where('user_id', $user->id)
            ->findOrFail($id);

        if (! $userWallet) {
            notify()->error(__('User wallet does not exist'));

            return redirect()->back();
        }

        if ($userWallet->balance > 0) {
            notify()->error(__('You have a balance in your wallet. You cannot delete your wallet.'));

            return redirect()->back();
        }

        try {
            DB::beginTransaction();

            $userWallet->delete();

            DB::commit();

            notify()->success(__('User wallet successfully deleted'));

            return redirect()->back();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            notify()->error(__('An error occurred while deleting the wallet. Please try again.'));

            return redirect()->back();
        }
    }

    public function gatewayMethod(Request $request)
    {
        try {
            $currencyCode = $this->getCurrencyCode($request->wallet_currency);

            if (! $currencyCode) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid wallet currency',
                ]);
            }

            $user = Auth::user();

            if ($request->gateway_type === 'add_money') {
                $gateways = DepositMethod::query()
                    ->where('currency', $currencyCode)
                    ->where('status', 1)
                    ->get();

                $options = $this->generateOptions($gateways, 'add_money');
            } else {
                $withdrawMethod = WithdrawMethod::query()
                    ->where('currency', $currencyCode)
                    ->where('status', 1)
                    ->first();

                if (! $withdrawMethod) {

                    return response()->json([
                        'status' => false,
                        'message' => __('No withdrawal method available for the selected currency'),
                    ]);
                }

                $withdrawAccounts = WithdrawAccount::query()
                    ->where('user_id', $user->id)
                    ->where('withdraw_method_id', $withdrawMethod->id)
                    ->with('method')
                    ->get();

                $options = $this->generateOptions($withdrawAccounts, 'withdraw_money');
            }

            return response()->json([
                'options' => $options,
            ]);
        } catch (\Throwable $throwable) {
            return response()->json([
                'status' => false,
                'message' => __('User Wallet not found'),
                'error' => $throwable->getMessage(),
            ]);
        }
    }

    private function getCurrencyCode($walletCurrency)
    {
        if ($walletCurrency === 'default') {
            return setting('site_currency');
        }

        $userWallet = UserWallet::query()
            ->with('coin')
            ->find($walletCurrency);

        return $userWallet?->coin?->code;
    }

    private function generateOptions($items, $type)
    {
        $options = '<option selected disabled>'.__('Select Gateway').'</option>';

        foreach ($items as $item) {
            if ($type === 'add_money') {
                $options .= sprintf(
                    '<option value="%s" data-minimum-amount="%s" data-maximum-amount="%s" data-routeurl="%s" data-gateway-type="%s" data-gateway-charge="%s" data-gateway-charge-type="%s" data-gateway-rate="%s" data-gateway-currency="%s" data-payment-method="%s" data-gateway-decimals="%s">%s</option>',
                    $item->id,
                    formatAmount($item->minimum_deposit, $item->currency),
                    formatAmount($item->maximum_deposit, $item->currency),
                    route('user.paymentGateway.info', $item->id),
                    $item->type,
                    formatAmount($item->charge, $item->currency),
                    $item->charge_type,
                    $item->rate,
                    $item->currency,
                    $item->name,
                    $item->currency_decimals,
                    $item->name.($item->type === 'manual' ? ' ('.__('Manual').')' : '')
                );
            } elseif ($type === 'withdraw_money') {
                $options .= sprintf(
                    '<option value="%s" data-minimum_amount="%s" data-maximum_amount="%s" data-withdraw_charge="%s" data-withdraw_charge_type="%s" data-withdraw_rate="%s" data-withdraw_currency="%s">%s</option>',
                    $item->id,
                    formatAmount($item?->method?->min_withdraw, $item?->method?->currency),
                    formatAmount($item?->method?->max_withdraw, $item?->method?->currency),
                    formatAmount($item?->method?->charge, $item?->method?->currency),
                    $item?->method?->charge_type,
                    $item?->method?->rate,
                    $item?->method?->currency,
                    $item?->method_name
                );
            }
        }

        return $options;
    }
}
