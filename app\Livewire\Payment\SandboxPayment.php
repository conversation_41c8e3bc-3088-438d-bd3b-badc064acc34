<?php

namespace App\Livewire\Payment;

use App\Enums\TxnStatus;
use App\Models\SandboxTransaction;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;

class SandboxPayment extends BasePaymentComponent
{
    public function mount($transaction_id)
    {
        $this->transaction = SandboxTransaction::with('user.merchant')->where('tnx', $transaction_id)->firstOrFail();
        $this->checkPaymentStatus();
        $this->payAmount = $this->transaction->final_amount;
    }

    public function nextStep()
    {
        // Get default credentials
        $credentials = $this->defaultCredentails();

        // Validate account number
        if ($this->account_number !== $credentials['account_number']) {
            $this->addError('account_number', __('UID is invalid!'));

            return;
        }

        $this->clearValidation('account_number');

        $this->step += 1;
    }

    public function payNow()
    {
        // Get default credentials
        $credentials = $this->defaultCredentails();

        // Check password is invalid return error
        if ($this->account_password !== $credentials['password']) {
            $this->addError('account_password', __('Password is invalid!'));
            $this->reset('account_password');

            return;
        }

        // Process payment
        try {

            DB::beginTransaction();

            // Process payment
            $this->processPayment();

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            $this->addError('wallet', 'Payment failed, please try again!');
            $this->resetPayment();
        }
    }

    private function processPayment(): void
    {
        // Update transaction status
        $this->transaction->update([
            'status' => TxnStatus::Success,
        ]);

        // Send request to IPN
        $this->sendRequestToIpn($this->transaction, data_get($this->transaction->manual_field_data, 'ipn_url'));

        $this->isSuccess = true;
        $this->isRedirection = $this->transaction->callback_url !== null;
    }

    private function defaultCredentails(): array
    {
        return [
            'account_number' => '***********',
            'password' => '********',
        ];
    }
}
