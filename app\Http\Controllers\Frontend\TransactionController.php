<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TransactionController extends Controller
{
    public function transactions(Request $request)
    {
        $user = Auth::user();
        $transactions = Transaction::with('wallet.coin')
            ->where('user_id', $user->id)
            ->when($request->filled('txn'), function ($query) use ($request) {
                $query->where('tnx', 'like', '%'.$request->input('txn').'%');
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->input('status'));
            })
            ->when($request->filled('wallet'), function ($query) use ($request) {
                $query->whereRelation('wallet', 'coin_id', $request->input('wallet'));
            })
            ->when($request->filled('type'), function ($query) use ($request) {
                $query->where('type', $request->input('type'));
            })
            ->when($request->filled('date'), function ($query) use ($request) {
                if (str($request->input('date'))->contains('to')) {
                    $dates = explode(' to ', $request->input('date'));
                    $dates = array_map(function ($date) {
                        return date('Y-m-d', strtotime($date));
                    }, $dates);
                    $query->whereBetween(DB::raw('DATE(created_at)'), $dates);
                } else {
                    $query->whereDate('created_at', $request->input('date'));
                }
            })
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('frontend::user.transaction.index', ['transactions' => $transactions]);
    }
}
