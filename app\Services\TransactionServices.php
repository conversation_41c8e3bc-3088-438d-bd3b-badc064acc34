<?php

namespace App\Services;

use App\Enums\TxnType;
use App\Enums\UserMiningStatus;
use App\Models\Transaction;
use App\Traits\NotifyTrait;
use Illuminate\Support\Facades\DB;

class TransactionServices
{
    use NotifyTrait;

    public function paid(Transaction $transaction)
    {
        if (in_array($transaction->type, [TxnType::PlanPurchase->value, TxnType::PlanPurchaseManual->value])) {
            $this->planPurchase($transaction);
        }
    }

    public function planPurchase(Transaction $transaction)
    {
        DB::beginTransaction();
        try {

            if ($transaction->wallet_type != null and $transaction->type != TxnType::PlanPurchaseManual->value) {
                if ($transaction->wallet_type == 'default') {
                    $transaction->user->decrement('balance', $transaction->amount);
                } else {
                    $transaction->user->wallets()->where('id', $transaction->wallet_type)->decrement('balance', $transaction->amount);
                }
            }

            $userMining = $transaction->user->minings()->create([
                'scheme_id' => $transaction->scheme_id,
                'transaction_id' => $transaction->id,
                'plan_price' => $transaction->amount,
                'total_mined_amount' => 0,
                'next_mining_time' => now()->addHours($transaction->scheme->return_period_hours),
                'status' => UserMiningStatus::Ongoing,
                'mining_count' => 0,
            ]);
            $transaction->update(['user_mining_id' => $userMining->id]);
            if ($transaction->user->wallets()->whereCoinId($transaction->scheme->miner->coin_id)->doesntExist()) {
                $transaction->user->wallets()->create(['coin_id' => $transaction->scheme->miner->coin_id, 'balance' => 0]);
            }
            defer(function () use ($transaction) {
                $trxCurr = $transaction->wallet_type == 'default' ? setting('site_currency', 'global') : $transaction->wallet->coin->code;
                $shortcodes = [
                    '[[admin_name]]' => 'Admin',
                    '[[conversion_rate]]' => '1 '.setting('site_currency', 'global').' = '.($transaction->wallet_type == 'default' ? 1 : cryptoFormat($transaction->scheme->miner->coin->conversion_rate)).' '.$trxCurr,
                    '[[charge]]' => $transaction->charge.' '.$trxCurr,
                    '[[final_amount]]' => $transaction->final_amount.' '.setting('site_currency', 'global'),
                    '[[payable_amount]]' => $transaction->amount.' '.$trxCurr,
                    '[[purchase_link]]' => route('admin.user.minings.show', $transaction->userMining->id),
                    '[[site_title]]' => setting('site_title', 'global'),
                    '[[scheme_name]]' => $transaction->scheme->name,
                ];

                $this->sendNotify(setting('site_email', 'global'), 'plan_purchase_admin', 'Admin', $shortcodes, null, null, route('admin.user.minings.show', $transaction->userMining->id));
            });
        } catch (\Throwable $th) {
            DB::rollBack();
        }

        DB::commit();
    }
}
