 @use '../../utils' as *;

 /*----------------------------------------*/
 /* Dashboard Sidebar styles
/*----------------------------------------*/
 .app-sidebar-wrapper {
   position: fixed;
   height: 100%;
   top: 0;
   z-index: 900;
   line-height: inherit;
   text-align: end;

   &.close_icon {
     @media #{$xs,$sm,$md,$lg} {
       margin-inline-start: -300px;
     }
   }

   .sidebar-inner {
     -webkit-box-shadow: 0 0 21px 0 rgba(89, 102, 122, 0.1);
     box-shadow: 0 0 21px 0 rgba(89, 102, 122, 0.1);
     border-inline-end: 1px solid rgba(255, 255, 255, 0.1);
     background: var(--td-heading);
   }
 }

 .app-sidebar {
   width: 290px;
   height: 100%;
   inset-block-start: 0;
   inset-inline-start: 0;
   background: var(--td-white);
   background: var(--td-white);
   border-inline-end: 1px solid rgba(8, 8, 8, 0.16);
   backdrop-filter: blur(3px);

   @include dark-theme {
     background-color: transparent;
     border-color: rgba($white, $alpha: 0.1);
     background-color: var(--td-void);
   }

   @media #{$xl,$xxl} {
     width: 260px;
   }

   @media #{$lg,$md,$sm,$xs} {
     inset-inline-start: -300px;
   }

   .main-sidebar-header {
     height: 70px;
     @include flexbox();
     align-items: center;
     justify-content: space-between;
     padding: 10px 20px;
     transition: all 0.03s ease;
     border-bottom: 1px solid rgba(8, 8, 8, 0.16);

     @include dark-theme {
       border-color: rgba($white, $alpha: 0.1);
     }

     img {
       transition: all 0.02s ease;
     }

     .sidebar-logo {
       .main-logo {
         img {
           height: 45px;
         }
       }

       .main-logo.logo-white-mode {
         display: none;
       }

       .small-logo {
         display: none;
       }
     }
   }

   .nav {
     >ul {
       padding-inline-start: 0px;
     }

     ul {
       li {
         list-style: none;
         margin: 0 14px;
         margin-bottom: 4px;
       }
     }
   }

   .sidebar-menu {
     display: none;
     padding: 0;
   }

   .sidebar-left {
     display: none;
   }

   .sidebar-right {
     display: none;
   }

   .main-menu {
     >.slide {
       .clip-path {
         position: relative;
         display: block;
         padding: 1px;
         background: transparent;

         .clip-path-inner {
           display: flex;
           position: relative;
           z-index: 3;
           background-color: var(--td-white);
           clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
           gap: 8px;
           height: 43px;
           align-items: center;
           justify-content: start;
           color: var(--td-white);

           @include dark-theme {
             background: var(--td-void);
           }

           &::before {
             position: absolute;
             top: 0;
             inset-inline-start: 0;
             content: "";
             z-index: -1;
             width: 100%;
             height: 100%;
           }

           .btn-icon {
             width: 20px;
             display: inline-flex;
           }
         }

         &::before {
           position: absolute;
           inset-inline-start: 0;
           top: 0;
           width: 100%;
           height: 100%;
           content: "";
           clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
           border-radius: 2px;
         }

         &::after {
           position: absolute;
           inset-inline-start: 0;
           top: 0;
           width: 100%;
           height: 100%;
           content: "";
           clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
           border-radius: 2px;
           background: linear-gradient(to right, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
           visibility: hidden;
         }

         &:hover {
           &::after {
             opacity: 1;
             visibility: visible;
           }
         }
       }

       .clip-path.active {
         .sidebar-menu-item {
           &:hover {
             background: transparent;
           }
         }

         .inner {
           &:before {
             background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
           }
         }

         &:before {
           background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
         }

         &:after {
           display: none;
         }
       }

       &:hover {
         .sidebar-menu {
           .sidebar-menu-item {
             &:hover {
               .side-menu-angle {
                 color: var(--td-primary) !important;
               }
             }
           }
         }
       }
     }

     >.slide.active {
       .sidebar-menu {
         .sidebar-menu-item {
           &:hover {
             .side-menu-angle {
               color: var(--td-primary) !important;
             }
           }
         }
       }
     }

     >.slide.has-logout {
       .sidebar-menu-item {
         background-color: rgba(251, 64, 90, 0.1);

         .sidebar-menu-label {
           color: var(--td-danger);
         }

         .side-menu-icon {
           color: var(--td-danger);
         }

         &:hover {
           background: rgba(251, 64, 90, 0.1);
         }
       }

       .clip-path {
         &::after {
           background: linear-gradient(to right, var(--td-danger) 0%, var(--td-danger) 100%);
           opacity: 1;
         }
       }
     }
   }

   .slide.has-sub {
     .sidebar-menu {
       transform: translate(0, 0) !important;
       visibility: visible !important;
     }

     display: -ms-grid;
     display: -moz-grid;
     display: grid;
     display: -ms-grid;
     display: -moz-grid;
     display: grid;
   }

   .slide.has-sub.open {
     >.sidebar-menu-item {
       .side-menu-angle {
         transform: rotate(180deg);
         transform: rotate(180deg);
       }
     }
   }

   .slide.active {
     .clip-path {
       position: relative;
       display: block;
       padding: 1px;
       background: transparent;

       .clip-path-inner {
         &::before {
           background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) 11.16%, rgba(142, 84, 233, 0.3) 100%);
         }
       }

       &::before {
         background: linear-gradient(to right, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
       }

       &::after {
         background: linear-gradient(to right, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
         background: linear-gradient(to left, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
         opacity: 0;
         visibility: hidden;
       }

       &:hover {
         &::after {
           opacity: 1;
           visibility: visible;
         }
       }
     }

     .clip-path.active {
       .inner {
         &:before {
           background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
         }
       }

       &:before {
         background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
       }

       &:after {
         display: none;
       }
     }

     &:hover {
       .sidebar-menu {
         .sidebar-menu-item {
           &:hover {
             .side-menu-angle {
               color: var(--td-primary) !important;
             }
           }
         }
       }
     }

     .sidebar-menu-item {
       .side-menu-icon {
         color: var(--td-heading);
       }

       .sidebar-menu-label {
         color: var(--td-heading);
       }
     }
   }

   .slide.active.active {
     .sidebar-menu {
       .sidebar-menu-item {
         &:hover {
           .side-menu-angle {
             color: var(--td-primary) !important;
           }
         }
       }
     }
   }

   .sidebar-menu.child1 {
     .sidebar-menu-item {
       &:hover {
         color: var(--td-primary);
       }

       padding: 6px 6px;
       background-color: transparent !important;

       &:before {
         position: absolute;
         content: "\e404";
         font-family: "Font Awesome 6 Pro";
         font-size: 12px;
         inset-inline-start: -10px;
         opacity: 0.8;
       }
     }

     .sidebar-menu-item.active {
       background-color: transparent !important;
     }

     li {
       padding: 0;
       position: relative;
       padding-inline-start: 56px;
     }
   }

   .sidebar-menu.child2 {
     .sidebar-menu-item {
       &:hover {
         color: var(--td-primary);
       }

       padding: 6px 6px;
       background-color: transparent !important;

       &:before {
         position: absolute;
         content: "\e404";
         font-family: "Font Awesome 6 Pro";
         font-size: 12px;
         inset-inline-start: -10px;
         opacity: 0.8;
       }
     }

     .sidebar-menu-item.active {
       background-color: transparent !important;
     }

     li {
       padding: 0;
       position: relative;
       padding-inline-start: 12px;
     }
   }

   .sidebar-menu.child3 {
     .sidebar-menu-item {
       &:hover {
         color: var(--td-primary);
       }

       background-color: transparent !important;

       &:before {
         position: absolute;
         content: "\e404";
         font-family: "Font Awesome 6 Pro";
         font-size: 12px;
         inset-inline-start: -10px;
         opacity: 0.8;
       }
     }

     .sidebar-menu-item.active {
       background-color: transparent !important;
     }

     li {
       padding: 0;
       position: relative;
       padding-inline-start: 16px;
     }
   }

   .sidebar-menu-category {
     .category-name {
       color: var(--td-secondary);
       font-size: 12px;
       font-weight: 500;
       text-transform: uppercase;
       padding: 4px 10px;
       white-space: nowrap;
       position: relative;
       margin-top: 15px;
       display: block;
     }
   }

   .sidebar-menu-item {
     padding: 12px 16px;
     position: relative;
     @include flexbox();
     align-items: center;
     gap: 6px;
     text-decoration: none;
     border: 1px solid transparent;
     width: 100%;

     &:hover {
       background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) 11.16%, rgba(142, 84, 233, 0.3) 100%);

       .sidebar-menu-label {
         color: var(--td-heading);
       }

       .side-menu-icon {
         color: var(--td-heading);
       }
     }
   }

   .sidebar-menu-item.active {
     color: var(--td-primary);

     .sidebar-menu-label {
       color: var(--td-primary);
     }

     .side-menu-angle {
       color: var(--td-primary);
     }

     .side-menu-icon {
       color: var(--td-primary);
     }
   }

   .sidebar-menu-label {
     white-space: nowrap;
     position: relative;
     font-size: 16px;
     font-weight: 700;
     line-height: 1;
     @include flexbox();
     align-items: center;
     text-transform: capitalize;
     transition: 0.3s;
     color: #646465;
   }

   .side-menu-icon {
     line-height: 0;
     font-size: 20px;
     text-align: center;
     color: #646465;

     svg {
       width: 16px;
       height: 16px;

       * {
         transition: 0.3s;
         width: 16px;
         height: 16px;
         stroke: var(--td-text-secondary);
       }
     }
   }

   .side-menu-angle {
     transform-origin: center;
     position: absolute;
     inset-inline-end: 20px;
     line-height: 1;
     font-size: 14px;
     transition: all 0.03s ease;
     opacity: 0.8;
   }

   .app-sidebar.nav-folded.side-nav-hover {
     .sidebar-menu-category {
       .category-name {
         display: block;
       }
     }
   }
 }

 .dark-theme {
   .app-sidebar {
     background-color: transparent;
     border-color: rgba(255, 255, 255, 0.1);
     background-color: var(--td-void);

     .main-sidebar-header {
       border-color: rgba(255, 255, 255, 0.1);
     }

     .main-menu {
       >.slide {
         .clip-path {
           .clip-path-inner {
             background: var(--td-void);
           }
         }
       }
     }

     .slide.active {
       .sidebar-menu-item {
         .side-menu-icon {
           color: var(--td-white);
         }

         .sidebar-menu-label {
           color: var(--td-white);
         }
       }
     }

     .sidebar-menu-label {
       color: var(--td-text-secondary);
     }

     .side-menu-icon {
       color: var(--td-text-secondary);
     }
   }
 }

 [dir=rtl] {
   .app-sidebar {
     .sidebar-menu-category {
       .category-name {
         text-align: right;
       }
     }
   }
 }

 .app-sidebar.collapsed {
   inset-inline-start: -300px;
 }

 .app-sidebar.nav-folded {
   width: 80px;
   transition: 0.2s;

   .nav {
     ul {
       li {
         margin: 0 10px;
       }
     }
   }

   .category-name {
     display: none;
   }

   .sidebar-menu-item {
     padding: 10px 16px;
     display: -webkit-inline-box;
     display: -moz-inline-box;
     display: -ms-inline-flexbox;
     display: -webkit-inline-flex;
     display: inline-flexbox;
     display: inline-flex;

     .sidebar-menu-label {
       display: none;
     }

     .category-name {
       display: none;
     }
   }

   .sidebar-logo {
     .main-logo {
       display: none;
     }

     .small-logo {
       display: block;
     }
   }
 }

 .app-sidebar.side-nav-hover {
   width: 290px;
   transition: all 0.3s ease;

   .sidebar-menu-item {
     .sidebar-menu-label {
       display: none;
       display: block;
     }

     @include flexbox();
   }

   .sidebar-menu-category {
     .category-name {
       display: block !important;
     }
   }

   .sidebar-logo {
     .main-logo {
       display: block;
     }

     .small-logo {
       display: none;
     }
   }
 }

 .toggle-sidebar {
   position: absolute;
   top: 60px;
   inset-inline-end: -10px;
   z-index: 5;

   @media #{$xs,$sm,$md,$lg} {
     position: relative;
     top: inherit;
     inset-inline-end: inherit;
     z-index: 5;
   }

   &.active {
     .bar-icon {
       transform: rotate(-180deg);
     }
   }
 }

 .close_sidebar {
   &.app-sidebar {
     @media #{$lg,$md,$sm,$xs} {
       inset-inline-start: 0px;
     }
   }
 }