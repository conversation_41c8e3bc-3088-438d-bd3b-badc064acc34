<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Portfolio;
use App\Traits\ImageUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PortfolioController extends Controller
{
    use ImageUpload;

    /**
     * Display a listing of the portfolios.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $portfolios = Portfolio::orderBy('level')->get();
        $currency = setting('site_currency', 'global');

        return view('backend.portfolio.index', compact('portfolios', 'currency'));
    }

    /**
     * Show the form for creating a new portfolio.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('backend.portfolio.create');
    }

    /**
     * Store a newly created portfolio in storage.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'level' => 'required|string|max:100',
            'portfolio_name' => 'required|string|max:255',
            'minimum_transactions' => 'required|numeric|min:0',
            'bonus' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'icon' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'status' => 'required|boolean',
            'discount_maintenance_type' => 'required|in:percentage,fixed',
            'discount_amount_maintenance' => 'nullable|numeric|min:0',
            'discount_plan_purchase_type' => 'required|in:percentage,fixed',
            'discount_amount_plan_purchase' => 'nullable|numeric|min:0',
            'boost_return_amount_type' => 'required|in:percentage,fixed',
            'boost_amount_return_amount' => 'nullable|numeric|min:0',
            'boost_referral_bonus_type' => 'required|in:percentage,fixed',
            'boost_amount_referral_bonus' => 'nullable|numeric|min:0',

        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first(), 'Error');

            return redirect()->back()->withErrors($validator)->withInput();
        }
        DB::beginTransaction();
        try {

            $portfolio = Portfolio::create([
                'level' => $request->level,
                'portfolio_name' => $request->portfolio_name,
                'minimum_transactions' => $request->minimum_transactions,
                'bonus' => $request->bonus,
                'description' => $request->description,
                'icon' => $this->imageUploadTrait($request->icon, folderPath: 'portfolios'),
                'status' => $request->status,
            ]);
            $featuresData = [
                'discount_maintenance_type' => $request->discount_maintenance_type,
                'discount_amount_maintenance' => $request->float('discount_amount_maintenance', 0),
                'discount_plan_purchase_type' => $request->discount_plan_purchase_type,
                'discount_amount_plan_purchase' => $request->float('discount_amount_plan_purchase', 0),
                'boost_return_amount_type' => $request->boost_return_amount_type,
                'boost_amount_return_amount' => $request->float('boost_amount_return_amount', 0),
                'boost_referral_bonus_type' => $request->boost_referral_bonus_type,
                'boost_amount_referral_bonus' => $request->float('boost_amount_referral_bonus', 0),
            ];

            $portfolio->features()->create($featuresData);
            DB::commit();
            notify()->success(__('Portfolio created successfully'), 'Success');

            return redirect()->route('admin.portfolio.index');
        } catch (\Exception $e) {
            DB::rollBack();
            notify()->error($e->getMessage(), 'Error');

            return redirect()->back()->withInput();
        }
    }

    /**
     * Show the form for editing the specified portfolio.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $portfolio = Portfolio::findOrFail($id);

        return view('backend.portfolio.edit', compact('portfolio'));
    }

    /**
     * Update the specified portfolio in storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'level' => 'required|string|max:100',
            'portfolio_name' => 'required|string|max:255',
            'minimum_transactions' => 'required|numeric|min:0',
            'bonus' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'status' => 'required|boolean',
            'discount_maintenance_type' => 'required|in:percentage,fixed',
            'discount_amount_maintenance' => 'nullable|numeric|min:0',
            'discount_plan_purchase_type' => 'required|in:percentage,fixed',
            'discount_amount_plan_purchase' => 'nullable|numeric|min:0',
            'boost_return_amount_type' => 'required|in:percentage,fixed',
            'boost_amount_return_amount' => 'nullable|numeric|min:0',
            'boost_referral_bonus_type' => 'required|in:percentage,fixed',
            'boost_amount_referral_bonus' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first(), 'Error');

            return redirect()->back()->withErrors($validator)->withInput();
        }
        DB::beginTransaction();
        try {
            $portfolio = Portfolio::findOrFail($id);

            $iconPath = $portfolio->icon;

            if ($request->hasFile('icon')) {
                $iconPath = $this->imageUploadTrait($request->icon, $portfolio->icon, 'portfolios');
            }

            $portfolio->update([
                'level' => $request->level,
                'portfolio_name' => $request->portfolio_name,
                'minimum_transactions' => $request->minimum_transactions,
                'bonus' => $request->bonus,
                'description' => $request->description,
                'icon' => $iconPath,
                'status' => $request->status,
            ]);
            $featuresData = [
                'discount_maintenance_type' => $request->discount_maintenance_type,
                'discount_amount_maintenance' => $request->float('discount_amount_maintenance', 0),
                'discount_plan_purchase_type' => $request->discount_plan_purchase_type,
                'discount_amount_plan_purchase' => $request->float('discount_amount_plan_purchase', 0),
                'boost_return_amount_type' => $request->boost_return_amount_type,
                'boost_amount_return_amount' => $request->float('boost_amount_return_amount', 0),
                'boost_referral_bonus_type' => $request->boost_referral_bonus_type,
                'boost_amount_referral_bonus' => $request->float('boost_amount_referral_bonus', 0),
            ];
            $portfolio->features()->updateOrCreate(['portfolio_id' => $portfolio->id], $featuresData);
            DB::commit();
            notify()->success(__('Portfolio updated successfully'), 'Success');

            return redirect()->route('admin.portfolio.index');
        } catch (\Exception $e) {
            DB::rollBack();
            notify()->error($e->getMessage(), 'Error');

            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified portfolio from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $portfolio = Portfolio::findOrFail($id);

            $this->fileDelete($portfolio->icon);

            $portfolio->delete();

            notify()->success(__('Portfolio deleted successfully'), 'Success');

            return redirect()->route('admin.portfolio.index');
        } catch (\Exception $e) {
            notify()->error($e->getMessage(), 'Error');

            return redirect()->back();
        }
    }
}
