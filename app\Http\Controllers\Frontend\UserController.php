<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Uri;

class UserController extends Controller
{
    public function notifyUser()
    {
        $notify = Session::get('user_notify');
        $isStepOne = 'current';
        $isStepTwo = 'current';
        $viewName = $notify['view_name'];

        return view('frontend::'.$viewName.'.success', ['isStepOne' => $isStepOne, 'isStepTwo' => $isStepTwo, 'notify' => $notify]);
    }

    public function allNotification()
    {
        $user = Auth::user();
        $notifications = Notification::where('for', 'user')->where('user_id', $user->id)->latest()->paginate(10);

        return view('frontend::user.notification.index', ['notifications' => $notifications]);
    }

    public function readNotification($id)
    {
        $user = Auth::user();

        if ($id == 0) {
            Notification::where('for', 'user')->where('user_id', $user->id)->update(['read' => 1]);

            return redirect()->back();
        }

        $notification = Notification::query()
            ->where('user_id', $user->id)
            ->find($id);

        if ($notification->read == 0) {
            $notification->read = 1;
            $notification->save();
        }
        $notification->action_url = Uri::of($notification->action_url)->withScheme(request()->getScheme())->value();

        return $notification->action_url == null ? back() : redirect($notification->action_url);
    }
}
