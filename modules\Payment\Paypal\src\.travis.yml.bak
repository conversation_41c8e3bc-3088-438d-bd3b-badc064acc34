language: php

sudo: false

php:
- 7.0
- 7.1
- 7.2
- 7.3
- 7.4

env:
  global:
  - setup=stable
  - coverage=no

before_script:
- composer update --prefer-dist --no-interaction

script:
- if [[ $coverage = 'yes' ]]; then ./vendor/bin/phpunit  $(if [ "$TRAVIS_PHP_VERSION" == "7.1" ] || [ "$TRAVIS_PHP_VERSION" == "7.2" ]; then echo "-c phpunit.xml.legacy"; fi;) --coverage-clover build/logs/clover.xml; fi
- if [[ $coverage = 'no' ]]; then ./vendor/bin/phpunit $(if [ "$TRAVIS_PHP_VERSION" == "7.1" ] || [ "$TRAVIS_PHP_VERSION" == "7.2" ]; then echo "-c phpunit.xml.legacy"; fi;); fi

after_script:
- if [[ $setup = 'coveralls' ]]; then php vendor/bin/php-coveralls -v; fi

matrix:
  include:
  - php: 7.0
    env: setup=coveralls coverage=yes
  - php: 7.1
    env: setup=coveralls coverage=yes
  - php: 7.2
    env: setup=coveralls coverage=yes
  - php: 7.3
    env: setup=coveralls coverage=yes
  - php: 7.4
    env: setup=coveralls coverage=yes
  fast_finish: true
