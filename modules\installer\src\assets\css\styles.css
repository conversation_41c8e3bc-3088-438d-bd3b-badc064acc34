/*-----------------------------------------------------------------------------------

  Project Name: <PERSON>rade HTML5 Template
  Author: Tdevs
  Support:
  Description: MITrade related HTML5 Template
  Version: 1.0

-----------------------------------------------------------------------------------

/*----------------------------------------*/
/*   Globals Default
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");
.product-filter-list ul li a::before, .td-main-menu .mega-menu li a, .single-notifications-item, input[type=radio] ~ label::before {
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

.breadcrumb-content .td-breadcrumb-menu nav ul li:not(:nth-child(1))::before, .faq-style-one .accordion .accordion-button::after {
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}

:root {
  --td-ff-body: "Inter", sans-serif;
  --td-ff-fontawesome: "Font Awesome 6 Pro";
  --td-ff-icomoon: "icomoon";
  --td-white: hsl(0, 0%, 100%);
  --td-black: hsl(0, 0%, 0%);
  --td-placeholder: hsla(0, 0%, 0%, 0.5);
  --td-selection: hsl(0, 0%, 0%);
  --td-heading: #010813;
  --td-primary: #0065ff;
  --td-secondary: #FDD819;
  --td-tertiary: #F81717;
  --td-text-primary: #676b71;
  --td-alice-blue: #F0F6FF;
  --td-yellow: #F79E1C;
  --td-warning: #FFA336;
  --td-success: #85FFC4;
  --td-danger: #ed4030;
  --td-green: #80ED99;
  --td-sunsetStrip: #febd00;
  --td-fw-normal: normal;
  --td-fw-thin: 100;
  --td-fw-elight: 200;
  --td-fw-light: 300;
  --td-fw-regular: 400;
  --td-fw-medium: 500;
  --td-fw-sbold: 600;
  --td-fw-bold: 700;
  --td-fw-ebold: 800;
  --td-fw-black: 900;
  --td-fs-body: 16px;
  --td-fs-p: 16px;
  --td-fs-h1: 52px;
  --td-fs-h2: 42px;
  --td-fs-h3: 32px;
  --td-fs-h4: 24px;
  --td-fs-h5: 20px;
  --td-fs-h6: 16px;
}

/*---------------------------------
/*  Default Spacing
---------------------------------*/
.td-section-space {
  padding-top: 100px;
  padding-bottom: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-section-space {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-section-space {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-section-space {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .td-section-space {
    padding-top: 70px;
    padding-bottom: 70px;
  }
}

.td-section-space-top {
  padding-top: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-section-space-top {
    padding-top: 100px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-section-space-top {
    padding-top: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-section-space-top {
    padding-top: 80px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .td-section-space-top {
    padding-top: 70px;
  }
}

.td-section-space-bottom {
  padding-bottom: 100px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-section-space-bottom {
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-section-space-bottom {
    padding-bottom: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-section-space-bottom {
    padding-bottom: 80px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .td-section-space-bottom {
    padding-bottom: 70px;
  }
}

.td-section-title-space {
  margin-bottom: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-section-title-space {
    margin-bottom: 50px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-section-title-space {
    margin-bottom: 45px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-section-title-space {
    margin-bottom: 40px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .td-section-title-space {
    margin-bottom: 35px;
  }
}

/*----------------------------------------*/
/*   Typography scss
/*----------------------------------------*/
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  font-family: var(--td-ff-body);
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5;
  color: var(--td-text-primary);
  background-color: var(--td-alice-blue);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--td-heading);
  margin-top: 0px;
  line-height: 1.3;
  margin-bottom: 0;
  word-break: break-word;
}

h1,
.h1 {
  line-height: 1.3;
  font-weight: var(--td-fw-bold);
  font-size: clamp(1.75rem, 1rem + 2vw, 3.25rem);
}

h2,
.h2 {
  font-size: clamp(1.375rem, 0.875rem + 1.5vw, 2.625rem);
  line-height: 1.3;
  font-weight: var(--td-fw-bold);
}

h3,
.h3 {
  font-size: clamp(1.375rem, 0.875rem + 1.5vw, 2rem);
  font-weight: var(--td-fw-sbold);
  line-height: 1.33;
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  h3,
  .h3 {
    font-size: 28px;
  }
}
@media (max-width: 575px) {
  h3,
  .h3 {
    font-size: 24px;
  }
}

h4,
.h4 {
  font-size: clamp(1.375rem, 0.875rem + 1.5vw, 1.5rem);
  font-weight: var(--td-fw-sbold);
  line-height: 1.4;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  h4,
  .h4 {
    font-size: 22px;
  }
}

h5,
.h5 {
  font-size: clamp(1.375rem, 0.875rem + 1.5vw, 1.25rem);
  font-weight: var(--td-fw-sbold);
  line-height: 1.4;
}
@media (max-width: 575px) {
  h5,
  .h5 {
    font-size: 20px;
  }
}

h6,
.h6 {
  font-size: clamp(1.375rem, 0.875rem + 1.5vw, 1rem);
  font-weight: var(--td-fw-sbold);
  line-height: 1.4;
}
@media (max-width: 575px) {
  h6,
  .h6 {
    font-size: 18px;
  }
}

ul {
  margin: 0px;
  padding: 0px;
}

p {
  color: var(--td-text-primary);
  font-size: 1rem;
}
p.b1 {
  font-size: 12px;
  line-height: 1.4;
}
p.b2 {
  font-size: 14px;
  line-height: 1.5;
}
p.b3 {
  font-size: 20px;
  line-height: 1.625;
}
p.b4 {
  font-size: 22px;
  line-height: 1.754;
}
p:last-child {
  margin-bottom: 0px;
}

a {
  text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
}

a,
button {
  color: inherit;
  outline: none;
  border: none;
  background: transparent;
}

.o-x-clip {
  overflow-x: clip;
}

img {
  max-width: 100%;
  object-fit: cover;
}

button:hover {
  cursor: pointer;
}

button:focus {
  outline: 0;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

hr:not([size]) {
  border-color: rgba(0, 0, 0, 0.1);
  opacity: 1;
  border-width: 1px;
}

input[type=text],
input[type=search],
input[type=email],
input[type=tel],
input[type=number],
input[type=password],
textarea {
  outline: none;
  background-color: transparent;
  height: 50px;
  width: 100%;
  font-size: 14px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  padding: 0 20px;
  color: var(--td-text-primary);
  border-radius: 12px;
}
input[type=text]:focus,
input[type=search]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
textarea:focus {
  border-color: var(--td-primary);
}

textarea {
  padding: 14px 24px;
}
textarea:focus {
  border-color: var(--td-heading);
}

input[type=checkbox] {
  display: none;
}
input[type=checkbox]:checked ~ label::before {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}
input[type=checkbox]:checked ~ label::after {
  background-color: var(--td-primary);
  border-color: var(--td-primary);
}
input[type=checkbox]:checked ~ label::before {
  visibility: visible;
  opacity: 1;
}
input[type=checkbox] ~ label {
  position: relative;
  padding-inline-start: 26px;
  z-index: 1;
  font-size: 14px;
}
input[type=checkbox] ~ label::after {
  position: absolute;
  content: "";
  top: 2px;
  inset-inline-start: 0;
  width: 18px;
  height: 18px;
  line-height: 16px;
  text-align: center;
  border: 1px solid #C3C7C9;
  z-index: -1;
  transition: all 0.2s linear;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}
input[type=checkbox] ~ label::before {
  position: absolute;
  content: "\f00c";
  top: 3px;
  inset-inline-start: 0px;
  width: 18px;
  height: 18px;
  line-height: 16px;
  text-align: center;
  visibility: hidden;
  opacity: 0;
  color: var(--td-white);
  transition: all 0.2s linear;
  font-family: var(--td-ff-fontawesome);
  font-size: 12px;
}
input[type=checkbox] ~ label:hover {
  cursor: pointer;
}

input[type=radio] {
  opacity: 0;
  position: absolute;
}
input[type=radio] ~ label {
  position: relative;
  font-size: 14px;
  line-height: 21px;
  font-weight: 400;
  padding-inline-start: 25px;
  cursor: pointer;
  margin-bottom: 0;
}
input[type=radio] ~ label::before {
  content: "";
  position: absolute;
  top: 3px;
  inset-inline-start: 0;
  width: 16px;
  height: 16px;
  background-color: transparent;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 2px;
  border-radius: 50%;
}
input[type=radio] ~ label::after {
  content: " ";
  position: absolute;
  transform: rotate(-45deg);
  opacity: 0;
  transition: all 0.3s;
  width: 8px;
  height: 8px;
  inset-inline-start: 4px;
  background: var(--td-white);
  border-radius: 50%;
  top: 7px;
}
input[type=radio]:checked ~ label::before {
  border-color: var(--td-primary);
}
input[type=radio]:checked ~ label::after {
  opacity: 1;
  background-color: var(--td-primary);
}

/*---------------------------------
/*  Custom Check Box
---------------------------------*/
.animate-custom .cbx {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}
.animate-custom .cbx span {
  display: inline-block;
  vertical-align: middle;
}
.animate-custom .cbx span a {
  color: var(--td-primary);
}
.animate-custom .cbx span a:hover {
  color: #000000;
}
.animate-custom .cbx span:first-child {
  position: relative;
  width: 18px;
  height: 18px;
  border-radius: 4px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid #b9b8c3;
  transition: all 0.2s ease;
}
.animate-custom .cbx span:first-child svg {
  position: absolute;
  z-index: 1;
  top: 4px;
  inset-inline-start: 2px;
  fill: none;
  stroke: #ffffff;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}
.animate-custom .cbx span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: var(--td-primary);
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
  transition-delay: 0.2s;
}
.animate-custom .cbx span:last-child {
  margin-inset-inline-start: 6px;
  color: var(--td-text-primary);
  font-weight: 500;
  font-size: 14px;
}
.animate-custom .cbx span:last-child:after {
  content: "";
  position: absolute;
  top: 8px;
  inset-inline-start: 0;
  height: 1px;
  width: 100%;
  background: #b9b8c3;
  transform-origin: 0 0;
  transform: scaleX(0);
}
.animate-custom .cbx:hover span:first-child {
  border-color: var(--td-primary);
}
.animate-custom .inp-cbx:checked + .cbx span:first-child {
  border-color: var(--td-primary);
  background: var(--td-primary);
  animation: check-15 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(2.2);
  opacity: 0;
  transition: all 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:last-child {
  transition: all 0.3s ease;
}
.animate-custom input[type=checkbox] ~ label::after {
  display: none;
}
.animate-custom input[type=checkbox] ~ label {
  padding-inline-start: 0;
}

@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}
*::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

*::-moz-placeholder {
  opacity: 1;
  font-size: 14px;
}

*::placeholder {
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
}

/*---------------------------------
  1.2 Common Classes
---------------------------------*/
.w-img img {
  width: 100%;
}

.m-img img {
  max-width: 100%;
}

.fix {
  overflow: hidden;
}

.clear {
  clear: both;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.z-index-1 {
  z-index: 1;
}

.z-index-11 {
  z-index: 11;
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.position-absolute {
  position: absolute;
}

.include-bg {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.hr-1 {
  border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
  overflow-x: clip;
}

.o-visible {
  overflow: visible;
}

.valign {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
}

/*----------------------------------------
  Bootstrap customize
-----------------------------------------*/
.container, .container-fluid, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
  --bs-gutter-x: 30px;
}

@media (min-width: 1601px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1600px;
  }
}
.row {
  --bs-gutter-x: 30px;
}

.g-0, .gx-0 {
  --bs-gutter-x: ;
}

.g-0, .gy-0 {
  --bs-gutter-y: 0 ;
}

.g-10,
.gx-10 {
  --bs-gutter-x: 10px;
}

.gy-12 {
  --bs-gutter-y: 12px;
}

.gy-15 {
  --bs-gutter-y: 15px;
}

.gx-15 {
  --bs-gutter-x: 15px;
}

.g-20 {
  --bs-gutter-x: 20px;
  --bs-gutter-y: 20px;
}

.gy-20 {
  --bs-gutter-y: 20px;
}

.gx-20 {
  --bs-gutter-x: 20px;
}

.gy-20 {
  --bs-gutter-y: 20px;
}

.gx-24 {
  --bs-gutter-x: 24px;
}

.gy-24 {
  --bs-gutter-y: 24px;
}

.g-30,
.gx-30 {
  --bs-gutter-x: 30px;
}

.g-30,
.gy-30 {
  --bs-gutter-y: 30px;
}

.g-40,
.gx-40 {
  --bs-gutter-x: 40px;
}

.g-40,
.gy-40 {
  --bs-gutter-y: 40px;
}

.g-50,
.gx-50 {
  --bs-gutter-x: 50px;
}

.g-50,
.gy-50 {
  --bs-gutter-y: 50px;
}

.g-60,
.gy-60 {
  --bs-gutter-y: 60px;
}

/*----------------------------------------
  Body Overlay
-----------------------------------------*/
.body-overlay {
  background-color: #000000;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9999;
  inset-inline-start: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

.body-overlay {
  background-color: #000000;
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9999;
  inset-inline-start: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

.body-overlay.opened {
  opacity: 0.7;
  visibility: visible;
}

/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 1280px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1600px) and (max-width: 1800px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 1000px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 850px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 820px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-iframe-holder .mfp-content {
    max-width: 750px;
  }
}

.mfp-close {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}
.mfp-close:hover {
  color: var(--td-white);
}
.mfp-close::after {
  position: absolute;
  content: "\f00d";
  height: 100%;
  width: 100%;
  font-family: var(--td-ff-fontawesome);
  font-size: 31px;
  font-weight: 200;
  inset-inline-end: -20px;
  margin-top: -25px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .mfp-close::after {
    inset-inline-end: 15px;
    margin-top: -30px;
  }
}

/*----------------------------------------*/
/*  terms-conditions css
/*----------------------------------------*/
.terms-conditions-content .wrapper-title {
  line-height: 1.3;
  margin-bottom: 30px;
}
.terms-conditions-content p {
  font-size: 17px;
  line-height: 27px;
  margin-bottom: 30px;
}
.terms-conditions-content .info-title {
  font-size: 24px;
  margin-bottom: 20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .terms-conditions-content .info-title {
    font-size: 18px;
  }
}
.terms-conditions-content .icon_list {
  margin-bottom: 40px;
}
.terms-conditions-content .icon_list.unordered-list-block > li {
  list-style: none;
}
.terms-conditions-content .icon_list.unordered-list-block > li:not(:last-child) {
  margin-bottom: 14px;
}
.terms-conditions-content .icon_list .list-item-text {
  color: var(--td-text-primary);
}
.terms-conditions-content .icon_list .list-item-icon {
  flex: none;
  width: auto;
  height: auto;
  font-size: 5px;
  border-radius: 0;
  margin-inline-end: 10px;
  color: var(--td-text-primary);
  background-color: transparent;
}
.terms-conditions-content .link {
  font-size: 17px;
  font-weight: 600;
  line-height: 27px;
  color: var(--td-primary);
  text-decoration: underline;
}
.terms-conditions-content .author-mail {
  font-size: 21px;
  font-weight: 700;
  line-height: 29px;
  color: var(--td-primary);
}

/*----------------------------------------*/
/*  Order history css
/*----------------------------------------*/
.order-history-form {
  display: grid;
  grid-template-columns: repeat(3, 1fr) auto;
  width: 100%;
  gap: 15px;
  padding: 20px 20px;
  margin-bottom: 30px;
  background: rgba(0, 101, 255, 0.1);
  border: 1px solid rgba(0, 101, 255, 0.1);
  border-radius: 6px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .order-history-form {
    grid-template-columns: repeat(1, 1fr) auto;
  }
}
@media (max-width: 575px) {
  .order-history-form {
    grid-template-columns: 1fr;
  }
}

.td-table.table-history table {
  min-width: 900px;
}

/*----------------------------------------*/
/* Chats style
/*----------------------------------------*/
.chatbot-main-grid {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .chatbot-main-grid {
    grid-template-columns: 340px 1fr;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .chatbot-main-grid {
    grid-template-columns: 1fr;
  }
}

.chat-left-side,
.chatbot-wrapper {
  padding: 30px 30px;
  background: var(--td-alice-blue);
  border-radius: 15px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .chat-left-side,
  .chatbot-wrapper {
    padding: 20px 20px;
  }
}
@media (max-width: 480px) {
  .chat-left-side,
  .chatbot-wrapper {
    padding: 16px 16px;
  }
}

.chatbot-avatar {
  display: flex;
  align-items: center;
  gap: 10px;
}
.chatbot-avatar .thumb {
  max-width: 38px;
  position: relative;
}
.chatbot-avatar .thumb::before {
  position: absolute;
  content: "";
  width: 8px;
  height: 8px;
  background-color: #62CC7B;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  bottom: 3px;
  inset-inline-start: 3px;
  border: 1px solid var(--td-alice-blue);
}
.chatbot-avatar .contents .title {
  font-size: 16px;
}
@media (max-width: 480px) {
  .chatbot-avatar .contents .title {
    font-size: 14px;
  }
}
.chatbot-avatar .contents .status {
  font-size: 14px;
  color: #6F747C;
}
.chatbot-avatar.sm .thumb {
  max-width: 50px;
}
.chatbot-avatar.sm .contents .title {
  font-size: 20px;
}
@media (max-width: 480px) {
  .chatbot-avatar.sm .contents .title {
    font-size: 18px;
  }
}
.chatbot-avatar.sm .contents .status {
  font-size: 14px;
}

.chatbot-form {
  position: relative;
}
.chatbot-form input {
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  border: 0;
  padding-inline-start: 40px;
  background-color: var(--td-white);
}
.chatbot-form .icon {
  position: absolute;
  top: 15px;
  inset-inline-start: 16px;
}

.chatbot-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 10px;
  padding: 10px 14px;
}

.chatbot-chat-list.td-tab .nav-item {
  width: 100%;
}
.chatbot-chat-list.td-tab .nav-tabs {
  width: 100%;
  gap: 5px;
}
.chatbot-chat-list.td-tab .nav-tabs .nav-link {
  width: 100%;
  border-radius: 10px;
  text-align: left;
}
.chatbot-chat-list.td-tab .nav-tabs .nav-link:hover {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
}

.chatbot-status {
  text-align: end;
}
.chatbot-status .date {
  font-size: 14px;
  margin-bottom: 5px;
}
@media (max-width: 480px) {
  .chatbot-status .date {
    font-size: 12px;
  }
}
.chatbot-status .chat-count {
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #27AC1F;
  font-size: 8px;
  color: var(--td-white);
  border-radius: 50%;
}

.chatbot-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px 15px;
  margin-bottom: 25px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 25px;
}

.chatbot-share ul {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.chatbot-share ul li {
  list-style: none;
}
.chatbot-share ul li button {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  justify-content: center;
}

.chat-history-date {
  background: rgba(0, 101, 255, 0.1);
  border-radius: 6px;
  font-size: 12px;
  padding: 7px 16px;
  color: var(--td-primary);
}

.chat-text-item {
  display: flex;
  align-items: end;
  gap: 26px;
  width: 70%;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .chat-text-item {
    width: 100%;
  }
}
.chat-text-item > .thumb {
  max-width: 46px;
  position: relative;
  flex: 0 0 auto;
}
@media (max-width: 575px) {
  .chat-text-item > .thumb {
    max-width: 36px;
  }
}
.chat-text-item > .thumb::before {
  position: absolute;
  content: "";
  width: 8px;
  height: 8px;
  background-color: #62CC7B;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  bottom: 3px;
  inset-inline-start: 3px;
  border: 1px solid var(--td-alice-blue);
}
.chat-text-item .contents {
  background-color: var(--td-white);
  padding: 14px 20px 20px;
  border-radius: 8px 8px 8px 0px;
  position: relative;
}
.chat-text-item .contents::after {
  position: absolute;
  content: "";
  background-color: var(--td-white);
  height: 27px;
  width: 28px;
  clip-path: polygon(0% 100%, 0% 100%, 14.515% 94.613%, 26.828% 86.985%, 37.091% 77.572%, 45.455% 66.83%, 52.073% 55.214%, 57.097% 43.182%, 60.678% 31.188%, 62.969% 19.689%, 64.121% 9.141%, 64.286% 0%, 100% 0%, 100% 100%, 0% 100%);
  bottom: 0px;
  inset-inline-start: -16px;
}
.chat-text-item .contents .description {
  font-weight: 500;
  margin-bottom: 24px;
}
@media (max-width: 575px) {
  .chat-text-item .contents .description {
    font-size: 14px;
  }
}
.chat-text-item .contents .time {
  position: absolute;
  inset-inline-start: 12px;
  bottom: 12px;
  font-size: 13px;
  color: #6F747C;
}
.chat-text-item .contents .file {
  display: flex;
  gap: 15px;
}
@media (max-width: 575px) {
  .chat-text-item .contents .file {
    flex-wrap: wrap;
  }
}
.chat-text-item .contents .file .thumb {
  max-width: 46px;
  position: relative;
  flex: 0 0 auto;
}
.chat-text-item.other-text {
  flex-direction: row-reverse;
  text-align: start;
  margin-inline: auto;
}
.chat-text-item.other-text .thumb::before {
  inset-inline-start: 3px;
  inset-inline-start: auto;
}
.chat-text-item.other-text .contents {
  background-color: var(--td-primary);
}
.chat-text-item.other-text .contents::after {
  background-color: var(--td-primary);
  clip-path: polygon(100% 100%, 100% 100%, 85.535% 94.613%, 73.358% 86.985%, 63.295% 77.572%, 55.173% 66.83%, 48.819% 55.214%, 44.06% 43.182%, 40.722% 31.188%, 38.631% 19.689%, 37.615% 9.141%, 37.5% 0%, 0% 0%, 0% 100%, 100% 100%);
  inset-inline-start: auto;
  inset-inline-end: -18px;
}
.chat-text-item.other-text .contents .description {
  color: var(--td-white);
}
.chat-text-item.other-text .contents .time {
  color: rgba(255, 255, 255, 0.8);
}
.chat-text-item.is-file {
  padding: 20px 20px;
}
.chat-text-item.is-file .contents {
  padding: 14px 20px 40px;
}
.chat-text-item .upload-thumb {
  position: relative;
  max-width: 230px;
}
.chat-text-item .upload-thumb img {
  border-radius: 8px 8px 8px 20px;
}
.chat-text-item .upload-thumb .time {
  position: absolute;
  inset-inline-start: 10px;
  bottom: 20px;
  color: var(--td-white);
  font-size: 13px;
}

.chatbot-history {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.chatbot-submit-form {
  position: relative;
}
.chatbot-submit-form > input {
  background: var(--td-white);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  height: 60px;
  padding-inline-end: 225px;
}
.chatbot-submit-form .submit-inner {
  position: absolute;
  text-align: right;
  top: 7px;
  inset-inline-end: 8px;
  display: flex;
  align-items: center;
  gap: 15px;
}
.chatbot-submit-form .submit-inner .input_search_option {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 20px;
}
.chatbot-submit-form .submit-inner .input_search_option::after {
  position: absolute;
  content: "";
  width: 1px;
  height: 26px;
  background: var(--clr-bg-15);
  inset-inline-start: -15px;
  top: 50%;
  transform: translateY(-50%);
}
@media (max-width: 575px) {
  .chatbot-submit-form .submit-inner .input_search_option::after {
    display: none;
  }
}
.chatbot-submit-form .submit-inner .input_search_option div {
  position: relative;
  display: inline-block;
  margin: 0 1px;
  cursor: pointer;
}
.chatbot-submit-form .submit-inner .input_search_option div input {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  opacity: 0.01;
  cursor: pointer;
}
.chatbot-submit-form .submit-inner .input_search_option div span {
  position: absolute;
  display: block;
  text-align: center;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  opacity: 0;
  background: var(--td-alice-blue);
  font-size: 9px;
  letter-spacing: 1px;
  line-height: 1.2;
  text-transform: capitalize;
  padding: 5px 10px;
  border-radius: 12px;
  top: -18px;
  transition: all 0.2s ease-in-out;
  min-width: 85px;
  border: 1px solid #ddd;
}
.chatbot-submit-form .submit-inner .input_search_option div span::after {
  content: "";
  position: absolute;
  bottom: -3px;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  border-top: 4px solid var(--clr-bg-7);
  border-inline-start: 4px solid transparent;
  border-inline-end: 4px solid transparent;
  transition: all 0.2s ease-in-out;
}
.chatbot-submit-form .submit-inner .input_search_option div:hover span {
  opacity: 1;
  top: -22px;
}
.chatbot-submit-form .submit-inner .input_search_option div label {
  display: block;
  cursor: pointer;
  padding-inline-start: 0px;
}
.chatbot-submit-form .submit-inner .input_search_option div label::before, .chatbot-submit-form .submit-inner .input_search_option div label::after {
  display: none;
}
.chatbot-submit-form .submit-inner .input_search_option div svg {
  height: 20px;
  width: 20px;
  transition: all 0.2s ease-in-out;
  pointer-events: none;
}
.chatbot-submit-form .submit-inner .input_search_option div:hover svg {
  opacity: 1;
}

/*----------------------------------------*/
/* Forums style
/*----------------------------------------*/
.forums-top-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px 20px;
}

.forums-form {
  width: 574px;
  position: relative;
}
.forums-form input {
  background: var(--td-white);
  border: 0;
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 40px;
  height: 60px;
  padding-inset-inline-end: 160px;
  padding-inline-start: 25px;
  font-size: 16px;
  font-weight: 500;
}
.forums-form input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 16px;
  font-weight: 500;
}
.forums-form input::-moz-placeholder { /* Firefox 19+ */
  font-size: 16px;
  font-weight: 500;
}
.forums-form input:-moz-placeholder { /* Firefox 4-18 */
  font-size: 16px;
  font-weight: 500;
}
.forums-form input:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 16px;
  font-weight: 500;
}
.forums-form input::placeholder { /* MODERN BROWSER */
  font-size: 16px;
  font-weight: 500;
}
.forums-form .td-btn.primary-btn {
  position: absolute;
  inset-inline-end: 10px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 30px;
  padding: 0 24px;
}

.forums-list-wrapper ul li {
  list-style: none;
}
.forums-list-wrapper ul li:not(:last-child) {
  margin-bottom: 30px;
}

.forums-single-item {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.18);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px;
  gap: 30px 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .forums-single-item {
    flex-direction: column;
    align-items: start;
  }
}
.forums-single-item .forums-btn-inner {
  flex: 0 0 auto;
}
.forums-single-item .forums-btn-inner .td-btn {
  background: rgba(0, 101, 255, 0.15);
  border-radius: 10px;
  padding: 0px 20px;
  gap: 5px;
  color: var(--td-primary);
}

.forums-contents {
  display: flex;
  align-items: center;
  gap: 16px;
}
@media (max-width: 480px) {
  .forums-contents {
    flex-direction: column;
    align-items: start;
  }
}
.forums-contents .thumb {
  max-width: 43px;
}
.forums-contents .contents .title {
  font-size: 18px;
  margin-bottom: 5px;
}
.forums-contents .contents .title a:hover {
  color: var(--td-primary);
}
.forums-contents .contents .description {
  font-size: 12px;
}

.forums-reply-comment {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px 15px;
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.18);
  border-radius: 15px;
  padding: 20px 20px;
}
@media (max-width: 480px) {
  .forums-reply-comment {
    padding: 16px 16px;
  }
}

.forums-contents-large .inner {
  display: flex;
  align-items: center;
  gap: 12px 20px;
}
@media (max-width: 480px) {
  .forums-contents-large .inner {
    flex-direction: column;
    align-items: start;
  }
}
.forums-contents-large .inner .thumb {
  max-width: 60px;
}
.forums-contents-large .inner .contents .title {
  font-size: 18px;
  margin-bottom: 5px;
}
.forums-contents-large .inner .contents .title a:hover {
  color: var(--td-primary);
}
.forums-contents-large .inner .contents .description {
  font-size: 14px;
}
.forums-contents-large > .description {
  font-size: 14px;
  margin-top: 17px;
  margin-bottom: 0;
}

.forums-reply-inner ul.parent li .forums-reply-comment {
  margin-bottom: 24px;
}
.forums-reply-inner ul li {
  list-style: none;
}
.forums-reply-inner ul.children li {
  margin-inline-start: 47px;
  position: relative;
}
.forums-reply-inner ul.children li::before {
  position: absolute;
  content: "";
  height: 1%;
  width: 27px;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 100%;
  background-color: #AEAEAE;
}
.forums-reply-inner ul.children li:after {
  position: absolute;
  content: "";
  height: calc(100% + 24px);
  width: 1px;
  background-color: #AEAEAE;
  top: -24px;
  inset-inline-start: -28px;
}
.forums-reply-inner ul.children li:not(:last-child) {
  margin-bottom: 24px;
}
.forums-reply-inner ul.children li:last-child::after {
  height: calc(50% + 25px);
}
.forums-reply-inner ul.children li .forums-contents-large > .description:last-child {
  margin-top: 10px;
}

/*----------------------------------------*/
/*  Notifications css
/*----------------------------------------*/
.notifications-drop-btn::after,
.messages-drop-btn::after {
  display: none;
}

.dropdown-menu {
  background: var(--td-alice-blue);
  box-shadow: 0px 0px 22px rgba(0, 101, 255, 0.25);
  border-radius: 10px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  width: 520px;
  top: 60px !important;
  padding: 24px 24px 24px;
  border: 0;
  inset-inline-end: 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .dropdown-menu {
    transform: translateX(35%);
  }
}
@media (max-width: 480px) {
  .dropdown-menu {
    transform: translateX(45%);
  }
}
@media (max-width: 480px) {
  .dropdown-menu {
    width: 350px;
    padding: 15px 15px;
  }
}
@media (max-width: 360px) {
  .dropdown-menu {
    width: 300px;
    padding: 15px 15px;
    inset-inline-start: -15px;
  }
}

.notifications-top-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 25px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
@media (max-width: 480px) {
  .notifications-top-content {
    margin-bottom: 17px;
  }
}
.notifications-top-content .title {
  font-size: 24px;
}
@media (max-width: 480px) {
  .notifications-top-content .title {
    font-size: 14px;
  }
}

.notifications-item-wrapper {
  height: 370px;
  overflow-y: scroll;
  scrollbar-width: thin;
  padding-inline-end: 5px;
  margin-bottom: 25px;
}

.notifications-info-list ul li {
  list-style: none;
}
.notifications-info-list ul li .list-item {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: start;
  gap: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  padding: 10px 10px;
}
.notifications-info-list ul li .list-item .thumb {
  position: relative;
  max-width: 38px;
  flex: 0 0 auto;
}
.notifications-info-list ul li .list-item .content .title {
  font-size: 16px;
  font-weight: 400;
}
.notifications-info-list ul li .list-item .content .title span {
  font-weight: 600;
  color: var(--td-primary);
}
@media (max-width: 480px) {
  .notifications-info-list ul li .list-item .content .title {
    font-size: 12px;
  }
}
.notifications-info-list ul li .list-item .content .info {
  font-size: 11px;
}
.notifications-info-list ul li .list-item:hover {
  background: var(--td-white);
}
.notifications-info-list ul li:not(:last-child) {
  margin-bottom: 6px;
}

.all-notifications-list ul li {
  list-style: none;
}

.single-notifications-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px;
  border-radius: 8px;
  position: relative;
  gap: 30px 50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .single-notifications-item {
    padding: 12px 12px;
    gap: 30px 25px;
  }
}
.single-notifications-item.active::before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  background-color: var(--td-primary);
  top: 50%;
  inset-inline-end: 145px;
  transform: translateY(-50%);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .single-notifications-item.active::before {
    height: 8px;
    width: 8px;
    inset-inline-end: 88px;
  }
}
.single-notifications-item:hover {
  background-color: var(--td-alice-blue);
}

.notifications-btn {
  background: rgba(0, 101, 255, 0.1);
  border-radius: 28px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0px 35px;
  font-size: 14px;
  font-weight: 600;
  color: var(--td-primary);
}
.notifications-btn:hover {
  background-color: var(--td-primary);
  color: var(--td-white);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .notifications-btn {
    height: 32px;
    padding: 0px 16px;
    font-size: 12px;
  }
}

.notifications-list-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: start;
  gap: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}
.notifications-list-content .thumb {
  position: relative;
  max-width: 38px;
  flex: 0 0 auto;
}
.notifications-list-content .content .info {
  font-size: 11px;
}
.notifications-list-content .content .title {
  font-size: 16px;
  font-weight: 400;
}
.notifications-list-content .content .title span {
  font-weight: 600;
  color: var(--td-primary);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .notifications-list-content .content .title {
    font-size: 14px;
  }
}
@media (max-width: 480px) {
  .notifications-list-content .content .title {
    font-size: 12px;
  }
}

.react-badge {
  width: 18px;
  height: 18px;
  background: #107FCF;
  display: inline-flex;
  align-items: center;
  border-radius: 50%;
  color: var(--td-white);
  font-size: 12px;
  justify-content: center;
  position: absolute;
  bottom: 1px;
  inset-inline-end: 0px;
}
.react-badge.danger {
  background-color: #ED4030;
}

.chats-main-wrapper .chats-dropdown-heading {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.chats-profile-content {
  display: flex;
  align-items: center;
  gap: 12px;
}
.chats-profile-content .thumb {
  max-width: 38px;
}
.chats-profile-content .contents .title {
  font-size: 16px;
}
.chats-profile-content .contents .description {
  font-size: 14px;
}

.chats-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 10px;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}
.chats-list-item:hover {
  background-color: var(--td-white);
}
.chats-list-item .other-info .date {
  font-size: 14px;
  margin-bottom: 5px;
}
.chats-list-item .other-info .count-text {
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #27AC1F;
  font-size: 10px;
  color: var(--td-white);
  border-radius: 50%;
}

/*----------------------------------------*/
/* FAQ style
/*----------------------------------------*/
.all-followers-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px) {
  .all-followers-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .all-followers-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .all-followers-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .all-followers-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .all-followers-grid {
    grid-template-columns: 1fr;
  }
}

.single-followers-item {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 10px;
  padding: 16px 16px;
}
.single-followers-item .thumb {
  max-width: 40px;
  margin-bottom: 10px;
}
.single-followers-item .contents .title {
  font-size: 14px;
}
.single-followers-item .contents .info {
  font-size: 12px;
}
.single-followers-item .btn-inner {
  margin-top: 12px;
}
.single-followers-item .btn-inner .td-btn {
  height: 30px;
  padding: 0 15px;
  font-size: 12px;
}

/*----------------------------------------*/
/*  item style
/*----------------------------------------*/
.item-details-pages {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 51px;
  padding: 10px 10px;
  margin-bottom: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .item-details-pages {
    border-radius: 10px;
    padding: 20px;
  }
}
.item-details-pages ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px 8px;
  justify-content: space-between;
}
.item-details-pages ul li {
  list-style: none;
}
.item-details-pages ul li.active a {
  background-color: var(--td-primary);
  color: var(--td-white);
}
.item-details-pages ul li a {
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 26px;
  border-radius: 30px;
  font-size: 14px;
  font-weight: 600;
  color: var(--td-primary);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .item-details-pages ul li a {
    padding: 0 30px;
  }
}
@media only screen and (min-width: 1600px) and (max-width: 1800px) {
  .item-details-pages ul li a {
    padding: 0 30px;
  }
}

.product-preview-thumb img {
  border-radius: 12px;
  width: 100%;
}

.product-preview-slider {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 40px 40px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .product-preview-slider {
    padding: 25px 25px;
  }
}
@media (max-width: 480px) {
  .product-preview-slider {
    padding: 15px 15px;
  }
}

.product-overview-item:not(:last-child) {
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
}
.product-overview-item .item-single-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px 10px;
}
.product-overview-item .item-single-top ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px 35px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-overview-item .item-single-top ul {
    gap: 10px 10px;
  }
}
.product-overview-item .item-single-top ul li span {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
}
.product-overview-item .item-single-top ul li span i {
  position: relative;
  top: 1px;
  font-size: 18px;
  margin-inline-end: 5px;
}
.product-overview-item .meta-items {
  display: flex;
  align-items: center;
}
.product-overview-item .meta-items ul li {
  list-style: none;
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .product-overview-title .title {
    font-size: 24px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
  .product-overview-title .title {
    font-size: 20px;
  }
}
@media (max-width: 575px) {
  .product-overview-title .title {
    font-size: 18px;
  }
}

.features-info-box,
.requirements-info-box {
  padding: 30px 30px;
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
}
@media (max-width: 480px) {
  .features-info-box,
  .requirements-info-box {
    padding: 15px 15px;
  }
}

.features-info-list ul li {
  list-style: none;
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}
.features-info-list ul li .list-icon,
.features-info-list ul li .list-text {
  color: var(--td-heading);
}
.features-info-list ul li .list-text {
  font-weight: 500;
}
@media (max-width: 575px) {
  .features-info-list ul li .list-text {
    font-size: 14px;
  }
}

.requirements-info-list ul li {
  list-style: none;
}
.requirements-info-list ul li:not(:last-child) {
  margin-bottom: 8px;
}

.swiper.product-preview-active {
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}

.pagination-wrappper {
  margin-top: 25px;
}

.td-swiper-dot .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background-color: rgba(0, 101, 255, 0.2);
  opacity: 1;
  border-radius: 30px;
  position: relative;
  margin: 0 5px !important;
}
.td-swiper-dot .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--td-primary);
}

.pro-sidebar-box {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  padding: 30px 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .pro-sidebar-box {
    padding: 15px 20px 20px;
  }
}
@media (max-width: 480px) {
  .pro-sidebar-box {
    padding: 20px 20px;
  }
}

.product-purchase-panel-top {
  margin-bottom: 25px;
}
.product-purchase-panel-top .product-purchase-info {
  display: flex;
  align-items: self-start;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 15px 15px;
}
.product-purchase-panel-top .product-purchase-info .td-input-filter .nice-select {
  min-width: 167px;
  background-color: rgba(103, 107, 113, 0.1);
  height: 35px;
}
.product-purchase-panel-top .product-purchase-info .td-input-filter .nice-select .current {
  font-size: 12px;
}

.product-purchase-panel-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30px;
  flex-wrap: wrap;
  gap: 20px 10px;
}
.product-purchase-panel-bottom .btn-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.product-purchase-list-box {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 30px 30px;
}
@media (max-width: 480px) {
  .product-purchase-list-box {
    padding: 20px 20px;
  }
}

.product-purchase-title {
  font-size: 16px;
  margin-bottom: 22px;
}

.product-purchase-list ul li {
  list-style: none;
  font-size: 14px;
  position: relative;
  padding-inline-start: 20px;
}
.product-purchase-list ul li:not(:last-child) {
  margin-bottom: 14px;
}
.product-purchase-list ul li:before {
  position: absolute;
  content: "";
  height: 10px;
  width: 10px;
  background-color: var(--td-primary);
  top: 7px;
  inset-inline-start: 0;
  border-radius: 50%;
}

.product-purchase-currency .description {
  font-size: 14px;
  font-weight: 500;
  margin-top: 5px;
}
.product-purchase-currency .description i {
  font-size: 18px;
  position: relative;
  top: 2px;
}

.product-purchase-price {
  display: flex;
  gap: 5px;
  align-items: center;
}
.product-purchase-price .current-price {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-primary);
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .product-purchase-price .current-price {
    font-size: 20px;
  }
}
.product-purchase-price .old-price {
  font-weight: 600;
  font-size: 16px;
}

.user-author-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.user-author-profile {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}
.user-author-profile .thumb {
  max-width: 60px;
  flex: 0 0 auto;
}
.user-author-profile .contents .title {
  font-size: 20px;
}

.td-social ul {
  display: flex;
  align-items: center;
  gap: 10px 15px;
}
.td-social ul li {
  list-style: none;
}
.td-social ul li a {
  width: 40px;
  height: 40px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--td-primary);
  color: var(--td-primary);
}
.td-social ul li a:hover {
  transform: translateY(-5px);
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: transparent;
}

.pro-sidebar-title {
  font-size: 24px;
  margin-bottom: 25px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .pro-sidebar-title {
    font-size: 18px;
  }
}

.product-info-list {
  padding-top: 30px;
  margin-bottom: 30px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.product-info-list ul li {
  display: grid;
  grid-template-columns: 40% 58%;
  justify-content: space-between;
  gap: 10px;
  align-items: center;
}
.product-info-list ul li:not(:last-child) {
  margin-bottom: 20px;
}
.product-info-list ul li .title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(1, 8, 19, 0.8);
}
@media (max-width: 480px) {
  .product-info-list ul li .title {
    font-size: 14px;
  }
}
.product-info-list ul li .info {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 0 20px;
  height: 44px;
  display: inline-flex;
  align-items: center;
}
@media (max-width: 480px) {
  .product-info-list ul li .info {
    font-size: 14px;
  }
}

.product-comments-filter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 25px;
  margin-bottom: 30px;
  gap: 10px 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-comments-filter {
    flex-direction: column;
    align-items: start;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .product-comments-title-inner .title {
    font-size: 24px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-comments-title-inner .title {
    font-size: 24px;
  }
}

.comments-filter-warp {
  display: flex;
  align-items: center;
  gap: 10px;
}
.comments-filter-warp .td-input-filter .nice-select {
  background-color: var(--td-primary);
  color: var(--td-white);
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  min-width: 185px;
  height: 44px;
}
.comments-filter-warp .td-input-filter .nice-select .current {
  color: var(--td-white);
}
.comments-filter-warp .td-input-filter .nice-select::after {
  border-color: var(--td-white);
}
.comments-filter-warp .td-input-filter .nice-select:focus {
  background-color: var(--td-primary);
}

.comments-filter {
  position: relative;
}
.comments-filter input {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  height: 44px;
}
.comments-filter .filter-btn {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
}

.product-comments-form textarea {
  height: 80px;
  width: 100%;
  border: 0;
  outline: none;
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  margin-bottom: 20px;
  padding: 10px 20px;
  font-size: 14px;
}
.product-comments-form textarea::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
}
.product-comments-form textarea::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
}
.product-comments-form textarea:-moz-placeholder { /* Firefox 4-18 */
  font-size: 14px;
}
.product-comments-form textarea:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 14px;
}
.product-comments-form textarea::placeholder { /* MODERN BROWSER */
  font-size: 14px;
}

.highlight-box {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 30px 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .highlight-box {
    padding: 20px 20px;
  }
}
@media (max-width: 480px) {
  .highlight-box {
    padding: 15px 15px;
  }
}

.product-comments-box > .title {
  font-size: 20px;
  margin-bottom: 10px;
}

.product-comment-avatar-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 5px;
}
.product-comment-avatar-title a:hover {
  color: var(--td-primary);
}

.product-comment-avatar-meta {
  font-size: 14px;
  color: var(--td-primary);
  display: inline-block;
}

.product-comment-content .description {
  font-size: 14px;
  color: #494F5A;
}

.product-comment-top {
  margin-bottom: 10px;
}

.product-comment-inner ul.children li {
  background: #DBE9FD;
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 12px;
  padding: 24px 20px 20px;
  margin-inline-start: 75px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-comment-inner ul.children li {
    margin-inline-start: 0px;
  }
}
.product-comment-inner ul.children li:not(:last-child) {
  margin-bottom: 20px;
}
.product-comment-inner ul > li {
  list-style: none;
  margin-top: 20px;
}

.product-comment-inner > ul > li:first-child {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 30px 30px;
}
@media (max-width: 480px) {
  .product-comment-inner > ul > li:first-child {
    padding: 20px 20px;
  }
}
@media (max-width: 480px) {
  .product-comment-inner > ul > li:first-child .product-comment-box {
    padding: 0px 0px;
  }
}

.product-comment-box {
  display: flex;
  gap: 18px;
}
@media (max-width: 575px) {
  .product-comment-box {
    flex-direction: column;
  }
}
@media (max-width: 480px) {
  .product-comment-box {
    padding: 20px 20px;
  }
}

.product-comment-thumb {
  flex: 0 0 auto;
  max-width: 60px;
}
.product-comment-thumb img {
  border: 1px solid var(--td-primary);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

.product-avatar-meta {
  font-size: 14px;
}

.item-details-wrapper > .title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 22px;
  margin-bottom: 25px;
  gap: 10px 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.product-support-card {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  display: flex;
  gap: 15px 40px;
  align-items: center;
  padding: 25px 25px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-support-card {
    gap: 15px 20px;
  }
}
@media (max-width: 575px) {
  .product-support-card {
    flex-wrap: wrap;
  }
}
.product-support-card .thumb {
  max-width: 145px;
}
.product-support-card .thumb img {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
}
.product-support-card .contents .title {
  margin-bottom: 15px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .product-support-card .contents .title {
    font-size: 18px;
  }
}
.product-support-card .contents .title span {
  color: var(--td-primary);
  font-weight: 400;
}
.product-support-card .contents .td-btn {
  height: 33px;
  font-size: 14px;
  font-weight: 500;
}

.product-support-info-box .single-info-list-item {
  background: var(--td-white);
  border-radius: 10px;
  padding: 20px 30px;
}
@media (max-width: 480px) {
  .product-support-info-box .single-info-list-item {
    padding: 10px 15px;
  }
}
.product-support-info-box .single-info-list-item > .title {
  font-size: 18px;
  margin-bottom: 15px;
}
.product-support-info-box .single-info-list-item .link {
  color: var(--td-primary);
  text-decoration: underline;
}
.product-support-info-box .single-info-list-item .underline-list {
  margin-top: 30px;
}

.underline-list ul li {
  list-style: none;
  font-size: 14px;
  text-decoration: underline;
  color: var(--td-heading);
  font-weight: 600;
}
.underline-list ul li:not(:last-child) {
  margin-bottom: 10px;
}

.support-info-top {
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 25px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .support-info-top > .title {
    font-size: 20px;
  }
}

.support-info-content .title {
  margin-bottom: 10px;
  font-size: 18px;
}
.support-info-content .title span {
  color: #ED4030;
}

.support-info-list ul li {
  list-style: none;
  position: relative;
  padding-inline-start: 15px;
  margin-bottom: 5px;
}
.support-info-list ul li:before {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  background-color: var(--td-text-primary);
  top: 50%;
  inset-inline-start: 50%;
  transform: translateY(-50%);
  inset-inline-start: 0;
  border-radius: 50%;
}

.support-info-content:not(:last-child) {
  margin-bottom: 20px;
}

.product-review-admin-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px 10px;
}

.product-review-admin-info {
  display: flex;
  align-items: center;
  gap: 20px;
}
.product-review-admin-info .date span {
  font-weight: 500;
}

.product-review-admin {
  display: flex;
  align-items: center;
  gap: 15px;
}
.product-review-admin .thumb {
  max-width: 50px;
  flex: 0 0 auto;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  border: 1px solid var(--td-primary);
}
.product-review-admin .contents .title {
  font-size: 20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-review-admin .contents .title {
    font-size: 18px;
  }
}
.product-review-admin .contents .info {
  font-size: 14px;
}

.product-review-rating {
  display: flex;
  align-items: center;
  gap: 10px;
}
.product-review-rating .icon span {
  color: #FF9900;
  font-size: 14px;
}
.product-review-rating > .title {
  font-size: 14px;
}
.product-review-rating > .title strong {
  font-weight: 500;
}

.product-review-item-box {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 30px 30px;
}
@media (max-width: 480px) {
  .product-review-item-box {
    padding: 20px 20px;
  }
}

.product-review-item .description {
  font-size: 14px;
  margin-bottom: 0;
}
.product-review-item .title-inner {
  margin-top: 15px;
}
.product-review-item .title-inner .title {
  font-size: 16px;
  margin-bottom: 10px;
}

.product-review-reply-item {
  background: var(--td-white);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 12px;
  padding: 26px 30px;
  margin-top: 25px;
}

.product-comment-reply {
  display: flex;
  gap: 16px;
}
.product-comment-reply .thumb {
  max-width: 50px;
  flex: 0 0 auto;
  border: 1px sold var(--td-primary);
}

.comment-reply-form {
  flex-grow: 1;
}
.comment-reply-form input {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  height: 58px;
  border: 0;
  margin-bottom: 16px;
}
.comment-reply-form .td-btn {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 32px;
  color: var(--td-primary);
  padding: 0 25px;
  font-size: 14px;
  font-weight: 600;
}
.comment-reply-form .td-btn:hover {
  color: var(--td-white);
  background-color: var(--td-primary);
}

.author-single-edit-box {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 20px 20px;
}

.author-edit-description-box {
  background: var(--td-white);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 20px 16px;
}
.author-edit-description-box .description {
  line-height: 1.7;
}

.description-title {
  font-size: 14px;
  color: var(--td-text-primary);
  margin-bottom: 8px;
}

.manage-discount-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px 15px;
}
.manage-discount-heading .status {
  background: var(--td-white);
  border-radius: 9px;
  padding: 10px 14px;
  color: var(--td-primary);
  font-size: 14px;
}
.manage-discount-heading .contents .title {
  font-size: 16px;
  margin-bottom: 5px;
}
.manage-discount-heading .contents .description {
  font-size: 12px;
}

.manage-discount-price {
  display: flex;
  align-items: center;
  gap: 8px;
}
.manage-discount-price span {
  font-weight: 500;
}
.manage-discount-price strong {
  font-size: 24px;
  color: var(--td-heading);
}

.manage-discount-form .item.header {
  padding-bottom: 15px;
  margin-top: 30px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
}
.manage-discount-form .item.header .column {
  flex: 1;
  padding-inline-start: 10px;
}
.manage-discount-form .single-item {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: var(--td-white);
  padding: 14px 16px;
  margin-bottom: 20px;
  border-radius: 7px;
}
.manage-discount-form input[type=text] {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  text-align: center;
  background: rgba(103, 107, 113, 0.1);
  border: 1px solid rgba(41, 45, 50, 0.1);
  border-radius: 7px;
  height: 42px;
  color: var(--td-heading);
}
@media (max-width: 575px) {
  .manage-discount-form input[type=text] {
    font-size: 14px;
  }
  .manage-discount-form input[type=text]::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    font-size: 14px;
  }
  .manage-discount-form input[type=text]::-moz-placeholder { /* Firefox 19+ */
    font-size: 14px;
  }
  .manage-discount-form input[type=text]:-moz-placeholder { /* Firefox 4-18 */
    font-size: 14px;
  }
  .manage-discount-form input[type=text]:-ms-input-placeholder { /* IE 10+  Edge*/
    font-size: 14px;
  }
  .manage-discount-form input[type=text]::placeholder { /* MODERN BROWSER */
    font-size: 14px;
  }
}
.manage-discount-form input[type=radio] {
  opacity: 1;
  position: relative;
  border-color: rgba(103, 107, 113, 0.4);
}
.manage-discount-form input[placeholder] {
  background-color: var(--td-white);
}

.discount-terms-list ul {
  margin-bottom: 5px;
}
.discount-terms-list ul li {
  list-style: none;
  position: relative;
  padding-inline-start: 15px;
}
.discount-terms-list ul li:not(:last-child) {
  margin-bottom: 5px;
}
.discount-terms-list ul li::before {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  background-color: var(--td-text-primary);
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 0;
  border-radius: 50%;
}

.manage-discount-form form {
  min-width: 620px;
}

.chart-container {
  width: 100%;
  padding: 20px;
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px 10px;
}
.chart-header .title {
  font-size: 24px;
}
@media (max-width: 575px) {
  .chart-header .title {
    font-size: 20px;
  }
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}
.chart-controls .td-btn.primary-btn, .chart-controls .td-btn.primary-opacity-btn {
  height: 30px;
  padding: 0 16px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
}
.chart-controls .td-btn.primary-opacity-btn {
  color: rgba(0, 0, 0, 0.6);
}
.chart-controls .select-option select {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 6px;
  font-size: 12px;
  height: 30px;
  padding: 0 10px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 500;
}

/*----------------------------------------*/
/* Shop style
/*----------------------------------------*/
.td-input-filter .nice-select {
  height: 46px;
  width: 100%;
  padding: 0 30px 0px 15px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  float: none;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  background-color: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
}
.td-input-filter .nice-select .current {
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  color: var(--td-text-primary);
}
@media (max-width: 480px) {
  .td-input-filter .nice-select .current {
    font-size: 12px;
  }
}
.td-input-filter .nice-select .list {
  -webkit-transform: scale(1) translateY(0);
  -moz-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: max-content;
  padding: 10px 0;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  border-radius: 12px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.08);
  border-width: 1px;
  padding: 12px 12px 12px 12px;
  max-height: 300px;
  width: 100%;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
  background: var(--td-white);
  -webkit-box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.1);
  box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 30;
}
.td-input-filter .nice-select::after {
  font-size: 16px;
  inset-inline-end: 16px;
  width: 9px;
  height: 9px;
  font-size: 16px;
  content: "";
  position: absolute;
  top: 50%;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
}
[dir=rtl] .td-input-filter .nice-select::after {
  inset-inline: auto;
  inset-inline-end: 12px;
}
.td-input-filter .nice-select .option {
  font-size: 14px;
  font-weight: 500;
  line-height: 40px;
  min-height: 40px;
  color: var(--td-text-primary);
  border-radius: 10px;
  padding: 0 15px;
}
@media (max-width: 480px) {
  .td-input-filter .nice-select .option {
    font-size: 12px;
  }
}
[dir=rtl] .td-input-filter .nice-select .option {
  text-align: right;
}
.td-input-filter .nice-select .option.selected {
  font-weight: 500;
}
.td-input-filter .nice-select .option:hover {
  background-color: var(--td-alice-blue);
}
.td-input-filter .nice-select .option.selected.focus {
  background-color: var(--td-alice-blue);
}
.td-input-filter .nice-select.open, .td-input-filter .nice-select:focus {
  background-color: var(--td-white);
}
.td-input-filter.input-xxs .nice-select {
  height: 38px;
  font-size: 14px;
  min-width: 155px;
}
.td-input-filter.input-xxs .nice-select::after {
  width: 8px;
  height: 8px;
}
.td-input-filter.input-xxs .nice-select .option {
  line-height: 35px;
  min-height: 35px;
}

.td-single-input.fill-color .nice-select {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
}

/*----------------------------------------*/
/*  New releases css
/*----------------------------------------*/
.new-releases-shapes .shape-one {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
}

.new-releases-item {
  position: relative;
  z-index: 11;
}
.new-releases-item .thumb-inner {
  margin-bottom: 15px;
  position: relative;
  margin: 0 25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .new-releases-item .thumb-inner {
    margin: 0 15px;
  }
}
.new-releases-item .thumb-inner .thumb img {
  border-radius: 20px;
}
.new-releases-item .thumb-inner .item-wishlist {
  position: absolute;
  top: 20px;
  inset-inline-start: 20px;
}
.new-releases-item .thumb-inner .item-wishlist .wishlist-icon {
  width: 32px;
  height: 32px;
  background: var(--td-white);
  box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.new-releases-item .thumb-inner .item-wishlist .wishlist-icon.active {
  color: #F34455;
}
.new-releases-item .thumb-inner .item-badge {
  position: absolute;
  top: 16px;
  inset-inline-end: -25px;
}
.new-releases-item .thumb-inner .item-badge .td-badge {
  position: relative;
  background-color: var(--td-primary);
  padding: 15px 20px;
  font-size: 15px;
  font-weight: 600;
  color: var(--td-white);
  border-radius: 10px 10px 0px;
}
.new-releases-item .thumb-inner .item-badge .td-badge::before {
  position: absolute;
  content: "";
  top: 100%;
  inset-inline-end: 0px;
  border-top: 10px solid #0549B1;
  border-inline-end: 10px solid transparent;
}
.new-releases-item .contents-inner {
  padding: 15px 15px;
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 30px;
  padding-top: 125px;
  position: relative;
  margin-top: -110px;
  z-index: -1;
}
.new-releases-item .contents-inner .title-inner {
  margin-bottom: 15px;
}
.new-releases-item .contents-inner .title-inner > .title {
  font-size: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .new-releases-item .contents-inner .title-inner > .title {
    font-size: 16px;
  }
}
.new-releases-item .contents-inner .title-inner > .title a:hover {
  color: var(--td-primary);
}
.new-releases-item .contents-inner .themes-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.new-releases-item .contents-inner .themes-meta span {
  font-size: 14px;
  font-weight: 500;
}
.new-releases-item .contents-inner .themes-meta span span {
  color: var(--td-primary);
}
.new-releases-item .contents-inner .themes-meta span span a:hover {
  text-decoration: underline;
}
.new-releases-item .contents-inner .themes-meta .themes-sales span {
  display: flex;
  gap: 5px;
  align-items: center;
}
.new-releases-item .contents-inner .themes-meta .themes-sales span i {
  font-size: 18px;
  transform: rotate(90deg);
}
.new-releases-item .contents-inner .latest-themes-info {
  display: flex;
  justify-content: space-between;
}
.new-releases-item .contents-inner .latest-themes-info .rating span {
  font-size: 14px;
  font-weight: 500;
}
.new-releases-item .contents-inner .latest-themes-info .rating .rating-count {
  text-decoration: underline;
}
.new-releases-item .contents-inner .latest-themes-info .rating .rating-icon {
  color: var(--td-yellow);
}
.new-releases-item .contents-inner .latest-themes-info .price-info {
  display: flex;
  gap: 8px;
}
.new-releases-item .contents-inner .latest-themes-info .price-info .current-price {
  color: var(--td-primary);
  font-size: 20px;
  line-height: 130%;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .new-releases-item .contents-inner .latest-themes-info .price-info .current-price {
    font-size: 16px;
  }
}
.new-releases-item .contents-inner .latest-themes-info .price-info .old-price {
  font-size: 16px;
  font-weight: 600;
}
.new-releases-item .contents-inner .latest-themes-btn-inner {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 20px;
  margin-top: 17px;
  display: flex;
  align-items: center;
  gap: 15px 15px;
}

/*----------------------------------------*/
/* Free Files style
/*----------------------------------------*/
.free-files-shapes .shape-one {
  position: absolute;
  inset-inline-end: 0;
  bottom: 14%;
  z-index: -1;
}

/*----------------------------------------*/
/* featured themes style
/*----------------------------------------*/
.featured-shapes .shape-one {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
}

.featured-themes-item {
  position: relative;
  padding: 20px 20px;
  background: var(--td-aliceBlue);
  border: 1px solid rgba(0, 101, 255, 0.4);
  border-radius: 30px;
  display: flex;
  gap: 30px;
  align-items: center;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .featured-themes-item {
    flex-direction: column;
  }
}
.featured-themes-item .thumb-inner {
  width: 297px;
  flex: 0 0 auto;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .featured-themes-item .thumb-inner {
    width: 210px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .featured-themes-item .thumb-inner {
    width: 100%;
  }
}
.featured-themes-item .thumb-inner {
  position: relative;
}
.featured-themes-item .thumb-inner .thumb img {
  border-radius: 20px;
}
.featured-themes-item .thumb-inner .item-wishlist {
  position: absolute;
  top: 20px;
  inset-inline-start: 20px;
}
.featured-themes-item .thumb-inner .item-wishlist .wishlist-icon {
  width: 32px;
  height: 32px;
  background: var(--td-white);
  box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.featured-themes-item .thumb-inner .item-wishlist .wishlist-icon.active {
  color: #F34455;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .featured-themes-item .contents-inner {
    width: 100%;
  }
}
.featured-themes-item .contents-inner .title-inner {
  padding-inline-end: 75px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .featured-themes-item .contents-inner .title-inner {
    padding-inline-end: 55px;
  }
}
.featured-themes-item .contents-inner .title-inner > .title {
  font-size: 20px;
  margin-bottom: 10px;
  font-weight: 700;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .featured-themes-item .contents-inner .title-inner > .title {
    font-size: 16px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
  .featured-themes-item .contents-inner .title-inner > .title {
    font-size: 20px;
  }
}
@media (max-width: 480px), (max-width: 575px) {
  .featured-themes-item .contents-inner .title-inner > .title {
    font-size: 20px;
  }
}
.featured-themes-item .contents-inner .title-inner > .title a:hover {
  color: var(--td-primary);
}
.featured-themes-item .contents-inner .themes-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}
.featured-themes-item .contents-inner .themes-meta span {
  font-size: 14px;
  font-weight: 500;
}
.featured-themes-item .contents-inner .themes-meta span span {
  color: var(--td-primary);
}
.featured-themes-item .contents-inner .featured-theme-info {
  display: flex;
  justify-content: space-between;
}
.featured-themes-item .contents-inner .featured-theme-info .rating span {
  font-size: 14px;
  font-weight: 500;
}
.featured-themes-item .contents-inner .featured-theme-info .rating .rating-count {
  text-decoration: underline;
}
.featured-themes-item .contents-inner .featured-theme-info .rating .rating-count:hover {
  text-decoration: none;
}
.featured-themes-item .contents-inner .featured-theme-info .rating .rating-icon {
  color: var(--td-yellow);
}
.featured-themes-item .contents-inner .featured-theme-info .price-info {
  display: flex;
  gap: 8px;
  align-items: center;
}
.featured-themes-item .contents-inner .featured-theme-info .price-info .current-price {
  color: var(--td-primary);
  font-size: 20px;
  line-height: 130%;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .featured-themes-item .contents-inner .featured-theme-info .price-info .current-price {
    font-size: 16px;
  }
}
.featured-themes-item .contents-inner .featured-theme-info .price-info .old-price {
  font-size: 16px;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .featured-themes-item .contents-inner .featured-theme-info .price-info .old-price {
    font-size: 14px;
  }
}
.featured-themes-item .contents-inner .featured-themes-bottom {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 20px;
  margin-top: 17px;
  display: flex;
  align-items: center;
  gap: 10px 15px;
  justify-content: space-between;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .featured-themes-item .contents-inner .featured-themes-bottom {
    flex-wrap: wrap;
  }
}
.featured-themes-item .contents-inner .featured-themes-bottom .btn-wrap {
  gap: 10px 15px;
}
.featured-themes-item .contents-inner .featured-themes-bottom .themes-sales span {
  display: flex;
  gap: 5px;
  align-items: center;
  font-weight: 500;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .featured-themes-item .contents-inner .featured-themes-bottom .themes-sales span {
    font-size: 14px;
  }
}
.featured-themes-item .contents-inner .featured-themes-bottom .themes-sales span i {
  font-size: 18px;
  transform: rotate(90deg);
}
.featured-themes-item .item-badge {
  position: absolute;
  top: 30px;
  inset-inline-end: -10px;
}
.featured-themes-item .item-badge .td-badge {
  padding: 8px 14px;
  font-size: 14px;
}

/*----------------------------------------*/
/* Latest themes style
/*----------------------------------------*/
.latest-themes-item {
  padding: 15px 15px;
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 30px;
  height: 100%;
}
.latest-themes-item .thumb-inner {
  margin-bottom: 15px;
  position: relative;
}
.latest-themes-item .thumb-inner .thumb img {
  border-radius: 20px;
}
.latest-themes-item .thumb-inner .item-wishlist {
  position: absolute;
  top: 20px;
  inset-inline-start: 20px;
}
.latest-themes-item .thumb-inner .item-wishlist .wishlist-icon {
  width: 32px;
  height: 32px;
  background: var(--td-white);
  box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.latest-themes-item .thumb-inner .item-wishlist .wishlist-icon.active {
  color: #F34455;
}
.latest-themes-item .thumb-inner .item-badge {
  position: absolute;
  top: 16px;
  inset-inline-end: -25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .latest-themes-item .thumb-inner .item-badge .td-badge {
    padding: 9px 16px;
    font-size: 14px;
  }
}
.latest-themes-item .contents .title-inner {
  margin-bottom: 10px;
}
.latest-themes-item .contents .title-inner > .title {
  font-size: 20px;
}
@media (max-width: 575px) {
  .latest-themes-item .contents .title-inner > .title {
    font-size: 18px;
  }
}
.latest-themes-item .contents .title-inner > .title a:hover {
  color: var(--td-primary);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .latest-themes-item .contents .title-inner > .title {
    font-size: 16px;
  }
}
.latest-themes-item .contents .themes-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
.latest-themes-item .contents .themes-meta span {
  font-size: 14px;
  font-weight: 500;
}
.latest-themes-item .contents .themes-meta span span {
  color: var(--td-primary);
}
.latest-themes-item .contents .themes-meta span span:hover {
  text-decoration: underline;
}
.latest-themes-item .contents .themes-meta .themes-sales span {
  display: flex;
  gap: 5px;
  align-items: center;
}
.latest-themes-item .contents .themes-meta .themes-sales span i {
  font-size: 18px;
  transform: rotate(90deg);
}
.latest-themes-item .contents .latest-themes-info {
  display: flex;
  justify-content: space-between;
}
.latest-themes-item .contents .latest-themes-info .rating span {
  font-size: 14px;
  font-weight: 500;
}
.latest-themes-item .contents .latest-themes-info .rating .rating-count a {
  text-decoration: underline;
}
.latest-themes-item .contents .latest-themes-info .rating .rating-count a:hover {
  text-decoration: none;
}
.latest-themes-item .contents .latest-themes-info .rating .rating-icon {
  color: var(--td-yellow);
}
.latest-themes-item .contents .latest-themes-info .price-info {
  display: flex;
  gap: 8px;
}
.latest-themes-item .contents .latest-themes-info .price-info .current-price {
  color: var(--td-primary);
  font-size: 20px;
  line-height: 130%;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .latest-themes-item .contents .latest-themes-info .price-info .current-price {
    font-size: 16px;
  }
}
.latest-themes-item .contents .latest-themes-info .price-info .old-price {
  font-size: 16px;
  font-weight: 600;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .latest-themes-item .contents .latest-themes-info .price-info .old-price {
    font-size: 12px;
  }
}
.latest-themes-item .contents .latest-themes-btn-inner {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 20px;
  margin-top: 17px;
  display: flex;
  align-items: center;
  gap: 15px 15px;
}

.td-latest-themes-tab.td-tab .nav-tabs {
  width: max-content;
  margin: 0 auto;
  justify-content: center;
  background: var(--td-white);
  border-radius: 30px;
  margin-bottom: 50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-latest-themes-tab.td-tab .nav-tabs {
    width: 100%;
    padding: 15px 15px;
  }
}
.td-latest-themes-tab.td-tab .nav-tabs .nav-link {
  padding: 0 30px;
  height: 60px;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 500;
  color: var(--td-heading);
  min-width: 120px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-latest-themes-tab.td-tab .nav-tabs .nav-link {
    height: 55px;
    font-size: 14px;
  }
}
.td-latest-themes-tab.td-tab .nav-tabs .nav-link.active {
  background-color: var(--td-primary);
  color: var(--td-white);
}

/*----------------------------------------*/
/* save-budget style
/*----------------------------------------*/
.save-budget-shapes .shape-one {
  position: absolute;
  bottom: 0;
  inset-inline-end: 0;
  height: 100%;
  z-index: -1;
}
.save-budget-shapes .shape-one img {
  width: 100%;
}

.save-budget-thumb-wrap {
  position: relative;
  padding: 50px 40px 40px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  margin-inline-end: 140px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .save-budget-thumb-wrap {
    margin-inline-end: 50px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .save-budget-thumb-wrap {
    margin-inline-end: 0px;
  }
}
.save-budget-thumb-wrap .save-budget-thumb-one {
  max-width: 646px;
  -webkit-transform: translateX(190px);
  -moz-transform: translateX(190px);
  -ms-transform: translateX(190px);
  -o-transform: translateX(190px);
  transform: translateX(190px);
}
@media only screen and (min-width: 1600px) and (max-width: 1800px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .save-budget-thumb-wrap .save-budget-thumb-one {
    -webkit-transform: translateX(120px);
    -moz-transform: translateX(120px);
    -ms-transform: translateX(120px);
    -o-transform: translateX(120px);
    transform: translateX(120px);
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .save-budget-thumb-wrap .save-budget-thumb-one {
    -webkit-transform: translateX(60px);
    -moz-transform: translateX(60px);
    -ms-transform: translateX(60px);
    -o-transform: translateX(60px);
    transform: translateX(60px);
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .save-budget-thumb-wrap .save-budget-thumb-one {
    -webkit-transform: translateX(30px);
    -moz-transform: translateX(30px);
    -ms-transform: translateX(30px);
    -o-transform: translateX(30px);
    transform: translateX(30px);
  }
}
.save-budget-thumb-wrap .save-budget-thumb-two {
  position: relative;
  margin-top: -130px;
}
.save-budget-thumb-wrap .budget-badge-shape {
  position: absolute;
  bottom: 100px;
  inset-inline-end: -120px;
}
.save-budget-thumb-wrap .budget-badge-shape img {
  animation: 8s linear 0s infinite normal none running tdSpinner;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px) {
  .save-budget-thumb-wrap .budget-badge-shape {
    max-width: 180px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .save-budget-thumb-wrap .budget-badge-shape {
    bottom: 40px;
    max-width: 160px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .save-budget-thumb-wrap .budget-badge-shape {
    inset-inline-end: 0;
    bottom: 40px;
    max-width: 160px;
  }
}

/*----------------------------------------*/
/* FAQ style
/*----------------------------------------*/
.faq-style-one .accordion .accordion-button {
  padding: 22px 40px 23px 25px;
  font-size: 18px;
  background-color: var(--td-alice-blue);
  font-weight: 700;
}
@media (max-width: 575px) {
  .faq-style-one .accordion .accordion-button {
    font-size: 18px;
  }
}
@media (max-width: 480px) {
  .faq-style-one .accordion .accordion-button {
    font-size: 16px;
    padding: 21px 30px 20px 20px;
  }
}
.faq-style-one .accordion .accordion-button:not(.collapsed) {
  background: transparent;
  border: 0;
  border-radius: 0;
  color: var(--td-heading);
  box-shadow: none;
}
.faq-style-one .accordion .accordion-button:not(.collapsed):after {
  color: var(--td-white);
  content: "+";
}
.faq-style-one .accordion .accordion-button::after {
  color: var(--td-black);
  border: none;
  background: transparent;
  inset-inline-end: 10px;
  background: transparent;
  position: absolute;
  inset-inline-end: 30px;
  content: "\f068";
  background-image: none;
  font-family: var(--td-ff-fontawesome);
  font-size: 12px;
  font-weight: 400;
  height: 20px;
  width: 20px;
  line-height: 20px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  background-color: rgba(0, 101, 255, 0.1);
  text-align: center;
  color: var(--td-heading);
  top: 50%;
}
@media (max-width: 575px) {
  .faq-style-one .accordion .accordion-button::after {
    inset-inline-end: 0;
  }
}
.faq-style-one .accordion .accordion-button:focus {
  box-shadow: none;
}
.faq-style-one .accordion .accordion-button span {
  padding-inline-end: 7px;
  display: inline-block;
  transition: none;
}
.faq-style-one .accordion .accordion-body {
  padding-inline-start: 25px;
  padding-inline-end: 25px;
  background: transparent;
  padding-bottom: 25px;
  border-radius: 0px;
  padding-top: 0px;
}
@media (max-width: 480px) {
  .faq-style-one .accordion .accordion-body {
    padding-inline-start: 20px;
    padding-inline-end: 20px;
  }
}
.faq-style-one .accordion .accordion-body .description :link {
  color: var(--td-primary);
  text-decoration: underline;
}
.faq-style-one .accordion .accordion-body .accordion-body-list {
  margin-top: 14px;
}
.faq-style-one .accordion .accordion-body .accordion-body-list ul li:not(:last-child) {
  margin-bottom: 7px;
}
.faq-style-one .accordion .accordion-item {
  box-shadow: 0px 4px 80px rgba(0, 0, 0, 0.08);
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
}
.faq-style-one .accordion .accordion-item:not(:last-child) {
  margin-bottom: 20px;
}
.faq-style-one .accordion .accordion-item:first-of-type .accordion-button, .faq-style-one .accordion .accordion-item:first-of-type .accordion-button {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/*----------------------------------------*/
/* Footer style
/*----------------------------------------*/
.footer-section.footer-primary {
  background-color: #051327;
  position: relative;
  clip-path: polygon(0% 0%, 48.209% 11.734%, 48.209% 11.734%, 48.567% 11.813%, 48.925% 11.874%, 49.283% 11.917%, 49.642% 11.943%, 50% 11.952%, 50.358% 11.943%, 50.717% 11.917%, 51.075% 11.874%, 51.433% 11.813%, 51.791% 11.734%, 100% 0%, 100% 100%, 0% 100%);
  margin-top: -195px;
}
.footer-section.footer-primary::before {
  position: absolute;
  content: "";
  inset-inline-start: -22.4%;
  inset-inline-end: 79.32%;
  top: 96.61%;
  bottom: -5.02%;
  width: 827px;
  height: 827px;
  background: radial-gradient(50% 50% at 50% 50%, rgba(0, 101, 255, 0.5) 0%, #000000 100%);
  background-blend-mode: screen;
  mix-blend-mode: screen;
  opacity: 0.33;
}
.footer-section.footer-primary .shape-one {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  mix-blend-mode: screen;
  z-index: -1;
}
[dir=rtl] .footer-section.footer-primary .shape-one {
  inset-inline-start: -30px;
  -webkit-transform: rotate(270deg);
  -moz-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  -o-transform: rotate(270deg);
  transform: rotate(270deg);
}
.footer-section.footer-primary .shape-two {
  position: absolute;
  bottom: 0;
  inset-inline-end: 0;
  mix-blend-mode: screen;
  z-index: -1;
}
[dir=rtl] .footer-section.footer-primary .shape-two {
  inset-inline-end: -50px;
  transform: rotate(90deg);
}
.footer-section .footer-wg-title {
  margin-bottom: 20px;
}
.footer-section .footer-wg-title h5 {
  font-size: 20px;
  font-weight: 700;
  color: var(--td-white);
}
.footer-section .footer-social ul {
  display: flex;
  align-items: center;
  gap: 10px 15px;
}
.footer-section .footer-social ul li {
  list-style: none;
}
.footer-section .footer-social ul li a {
  width: 40px;
  height: 40px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--td-primary);
  color: var(--td-primary);
}
.footer-section .footer-social ul li a:hover {
  transform: translateY(-5px);
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: transparent;
}
.footer-section .footer-logo {
  margin-bottom: 40px;
}
.footer-section .footer-project-info {
  display: flex;
  align-items: center;
  gap: 20px 20px;
  margin-bottom: 40px;
}
.footer-section .footer-project-info .info-item .info {
  font-size: 14px;
  display: block;
  margin-bottom: 5px;
  color: rgba(255, 255, 255, 0.7);
}
.footer-section .footer-project-info .info-item .title {
  color: var(--td-white);
}
.footer-section .footer-pattern {
  position: absolute;
  top: 260px;
  inset-inline-start: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: center;
  z-index: -1;
}
.footer-section .footer-intro-main {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  grid-template-columns: auto 411px;
  align-items: center;
  margin-bottom: 30px;
  gap: 20px 30px;
}
@media (max-width: 575px) {
  .footer-section .footer-intro-main {
    grid-template-columns: 1fr;
  }
}
.footer-section .footer-intro-main .description {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  font-size: 12px;
  font-weight: 500;
}
.footer-section .footer-main {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  grid-template-columns: 30% 25% 25% 20%;
  padding-top: 290px;
  padding-bottom: 85px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: 50px 30px;
}
@media xs, only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .footer-section .footer-main {
    grid-template-columns: repeat(2, 1fr);
    padding-top: 260px;
    padding-bottom: 65px;
  }
}
@media (max-width: 575px) {
  .footer-section .footer-main {
    grid-template-columns: 1fr;
    padding-top: 35px;
    padding-bottom: 35px;
  }
}

.footer-links ul li {
  list-style: none;
}
.footer-links ul li:not(:last-child) {
  margin-bottom: 12px;
}
.footer-links ul li a {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}
.footer-links ul li a:hover {
  color: var(--td-sunsetStrip);
}

.footer-bottom .footer-bottom-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 28px 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .footer-bottom .footer-bottom-inner {
    gap: 10px 20px;
    flex-wrap: wrap;
    justify-content: center;
  }
}
.footer-bottom .footer-bottom-inner .footer-copyright {
  text-align: center;
}
.footer-bottom .footer-bottom-inner .footer-copyright .description {
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}
.footer-bottom .footer-bottom-inner .footer-bottom-links ul {
  display: flex;
  align-items: center;
  gap: 10px 27px;
}
.footer-bottom .footer-bottom-inner .footer-bottom-links ul li {
  list-style: none;
}
.footer-bottom .footer-bottom-inner .footer-bottom-links ul li a {
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}
.footer-bottom .footer-bottom-inner .footer-bottom-links ul li a:hover {
  color: var(--td-sunsetStrip);
}

/*----------------------------------------*/
/* fun fact style
/*----------------------------------------*/
.fun-fact-counter-grid {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  grid-template-columns: auto auto auto auto;
  align-items: center;
  gap: 30px 50px;
  position: relative;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .fun-fact-counter-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .fun-fact-counter-grid {
    grid-template-columns: 1fr;
  }
}
.fun-fact-counter-grid .fun-fact-bg {
  position: absolute;
  top: -20%;
  width: 1440px;
  height: 700px;
  filter: blur(302px);
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  z-index: -1;
}

.single-counter-item {
  position: relative;
  text-align: center;
}
.single-counter-item:nth-child(4)::before {
  display: none;
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .single-counter-item:nth-child(2n)::before {
    display: none;
  }
}
.single-counter-item::before {
  position: absolute;
  content: "";
  height: 35px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.2);
  inset-inline-start: 109%;
  bottom: 0%;
  transform: translateY(-50%);
}
@media (max-width: 480px) {
  .single-counter-item::before {
    display: none;
  }
}
.single-counter-item .icon {
  margin-bottom: 20px;
}
.single-counter-item .content .title,
.single-counter-item .content span {
  font-family: var(--td-ff-body);
  font-size: 56px;
  color: var(--td-white);
  font-weight: 200;
  line-height: 1;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 768px) and (max-width: 991px) {
  .single-counter-item .content .title,
  .single-counter-item .content span {
    font-size: 44px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .single-counter-item .content .title,
  .single-counter-item .content span {
    font-size: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .single-counter-item .content .title,
  .single-counter-item .content span {
    font-size: 36px;
  }
}
@media (max-width: 575px) {
  .single-counter-item .content .title,
  .single-counter-item .content span {
    font-size: 32px;
  }
}
.single-counter-item .content .description {
  color: var(--td-primary);
  font-size: 12px;
}

/*----------------------------------------*/
/*  how it works style
/*----------------------------------------*/
.rock-how-it-main {
  margin-top: 80px;
}

.rock-how-it-works-grid {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 50px 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .rock-how-it-works-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .rock-how-it-works-grid {
    grid-template-columns: 1fr;
  }
}

.rock-how-it-works-item {
  position: relative;
  text-align: center;
}
@media (max-width: 575px) and (max-width: 575px), only screen and (max-width: 575px) and (min-width: 576px) and (max-width: 767px), only screen and (max-width: 575px) and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) and (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 576px) and (max-width: 767px) and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 768px) and (max-width: 991px) and (max-width: 575px), only screen and (min-width: 768px) and (max-width: 991px) and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) and (min-width: 768px) and (max-width: 991px) {
  .rock-how-it-works-item:nth-child(2n) .line {
    display: none;
  }
}
@media (max-width: 480px) and (max-width: 575px), only screen and (max-width: 480px) and (min-width: 576px) and (max-width: 767px), only screen and (max-width: 480px) and (min-width: 768px) and (max-width: 991px) {
  .rock-how-it-works-item .line {
    display: none;
  }
}
.rock-how-it-works-item .content .title {
  color: var(--td-white);
  font-size: 16px;
  margin-bottom: 20px;
  font-weight: 700;
}
.rock-how-it-works-item .content .description {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}
.rock-how-it-works-item .icon {
  margin-bottom: 22px;
}
.rock-how-it-works-item .icon span {
  width: 104px;
  height: 104px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  -o-border-radius: 100px;
  -ms-border-radius: 100px;
  border-radius: 100px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.rock-how-it-works-item .icon span:before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 100px;
  -moz-border-radius: 100px;
  -o-border-radius: 100px;
  -ms-border-radius: 100px;
  border-radius: 100px;
  padding: 2px;
  background: linear-gradient(187.69deg, rgba(255, 205, 16, 0) 29.9074500799%, rgba(255, 205, 16, 0.4) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}
.rock-how-it-works-item .line {
  position: absolute;
  top: 52px;
  inset-inline-start: 105%;
  width: 75%;
  height: 1px;
  background: var(--td-white);
  transform: translateX(-50%);
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .rock-how-it-works-item .line {
    inset-inline-start: 106%;
    width: 67%;
  }
}
.rock-how-it-works-item .line::before, .rock-how-it-works-item .line::after {
  position: absolute;
  content: "";
  top: 50%;
  inset-inline-start: 0;
  width: 5px;
  height: 5px;
  background: var(--td-white);
  border-radius: 50%;
  transform: translateY(-50%);
}
.rock-how-it-works-item .line::after {
  inset-inline-start: auto;
  inset-inline-end: 0;
}
.rock-how-it-works-item:nth-child(2) .icon span {
  background: rgba(167, 186, 255, 0.1);
}
.rock-how-it-works-item:nth-child(2) .icon span::before {
  background: linear-gradient(187.69deg, rgba(167, 186, 255, 0) 29.9074500799%, rgba(167, 186, 255, 0.4) 100%);
}
.rock-how-it-works-item:nth-child(3) .icon span {
  background: rgba(85, 255, 224, 0.1);
}
.rock-how-it-works-item:nth-child(3) .icon span:before {
  background: linear-gradient(187.69deg, rgba(85, 255, 224, 0) 29.9074500799%, rgba(85, 255, 224, 0.4) 100%);
}
.rock-how-it-works-item:nth-child(4) .icon span {
  background: rgba(255, 255, 255, 0.1);
}
.rock-how-it-works-item:nth-child(4) .icon span::before {
  background: linear-gradient(187.69deg, rgba(255, 255, 255, 0) 29.9074500799%, rgba(255, 255, 255, 0.4) 100%);
}
.rock-how-it-works-item:nth-child(5) .icon span {
  background: rgba(162, 255, 182, 0.1);
}
.rock-how-it-works-item:nth-child(5) .icon span::before {
  background: linear-gradient(187.69deg, rgba(162, 255, 182, 0) 29.9074500799%, rgba(162, 255, 182, 0.4) 100%);
}
.rock-how-it-works-item:nth-child(6) .icon span {
  background: rgba(85, 112, 255, 0.1);
}
.rock-how-it-works-item:nth-child(6) .icon span::before {
  background: linear-gradient(187.69deg, rgba(85, 112, 255, 0) 29.9074500799%, rgba(85, 112, 255, 0.4) 100%);
}
.rock-how-it-works-item:nth-child(7) .icon span {
  background: rgba(202, 255, 160, 0.1);
}
.rock-how-it-works-item:nth-child(7) .icon span::before {
  background: linear-gradient(187.69deg, rgba(202, 255, 160, 0) 29.9074500799%, rgba(202, 255, 160, 0.4) 100%);
}
.rock-how-it-works-item:nth-child(8) .icon span {
  background: rgba(255, 255, 255, 0.1);
}
.rock-how-it-works-item:nth-child(8) .icon span:before {
  background: linear-gradient(187.69deg, rgba(255, 255, 255, 0) 29.9074500799%, rgba(255, 255, 255, 0.4) 100%);
}

/*----------------------------------------*/
/*  Main menu css
/*----------------------------------------*/
.td-main-menu.has-top ul {
  gap: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-main-menu.has-top ul {
    gap: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-main-menu.has-top ul {
    gap: 5px;
  }
}
.td-main-menu.has-top ul > li > a {
  font-size: 15px;
}
.td-main-menu > ul {
  display: inline-flex;
  gap: 28px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-main-menu > ul {
    gap: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-main-menu > ul {
    gap: 10px;
  }
}
.td-main-menu > ul > li > a {
  position: relative;
}
.td-main-menu > ul > li:last-child a::before {
  display: none;
}
.td-main-menu > ul > li:hover > a {
  color: var(--td-sunsetStrip);
}
.td-main-menu > ul > li:hover > ul {
  opacity: 1;
  pointer-events: all;
}
.td-main-menu > ul > li:hover > ul.dp-menu li:hover > ul {
  opacity: 1;
  pointer-events: all;
  inset-inline-start: 100%;
}
.td-main-menu > ul > li:has(ul) > a::after {
  content: "\f107";
  font-family: var(--td-ff-fontawesome);
  margin-inline-start: 5px;
  font-weight: 500;
  font-size: 13px;
  position: relative;
  top: 2px;
}
[dir=rtl] .td-main-menu > ul > li:has(ul) > a::after {
  margin-inline-end: 5px;
}
.td-main-menu > ul > li > ul.dp-menu > li.menu-item-has-children > a {
  position: relative;
}
.td-main-menu > ul > li > ul.dp-menu > li.menu-item-has-children > a:before {
  position: absolute;
  inset-inline-end: 0;
  content: "\f054";
  transform: translateY(1px);
  font-size: 12px;
  color: var(--td-heading);
  font-family: var(--td-ff-fontawesome);
  font-weight: 400;
  margin-inline-start: 5px;
  display: inline-block;
  transition: all 0.3s ease-out 0s;
}
[dir=rtl] .td-main-menu > ul > li > ul.dp-menu > li.menu-item-has-children > a:before {
  inset-inline-start: 0;
  inset-inline-end: auto;
  content: "\f053";
}
.td-main-menu > ul > li > ul.dp-menu > li.menu-item-has-children ul.dp-menu li.menu-item-has-children > a {
  position: relative;
}
.td-main-menu > ul > li > ul.dp-menu > li.menu-item-has-children ul.dp-menu li.menu-item-has-children > a::before {
  position: absolute;
  inset-inline-end: 0;
  content: "\f054";
  transform: translateY(1px);
  font-size: 12px;
  font-family: var(--td-ff-fontawesome);
  font-weight: 400;
  margin-inline-start: 5px;
  display: inline-block;
  transition: all 0.3s ease-out 0s;
}
[dir=rtl] .td-main-menu > ul > li > ul.dp-menu > li.menu-item-has-children ul.dp-menu li.menu-item-has-children > a::before {
  inset-inline-start: 0;
  inset-inline-end: auto;
  content: "\f053";
}
.td-main-menu li {
  position: relative;
  list-style: none;
}
.td-main-menu li a {
  font-size: 14px;
  font-weight: 600;
  line-height: 1;
  color: rgba(255, 255, 255, 0.8);
  padding: 37px 5px;
  display: inline-block;
  letter-spacing: 0.2px;
}
.td-main-menu .dp-menu {
  background: var(--td-white);
  padding: 18px 0px;
  width: 260px;
  position: absolute;
  inset-inline-start: 0px;
  opacity: 0;
  pointer-events: none;
  z-index: 10;
  transition: all 0.3s;
  top: 100%;
  border-radius: 10px 10px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}
.td-main-menu .dp-menu ul {
  background: var(--td-white);
  padding: 18px 0px;
  width: 260px;
  position: absolute;
  inset-inline-start: calc(100% + 10px);
  top: 0;
  opacity: 0;
  z-index: 10;
  transition: all 0.3s;
  border-radius: 10px 10px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
}
.td-main-menu .dp-menu li {
  position: relative;
  padding: 0 20px;
}
.td-main-menu .dp-menu li:hover > a {
  color: var(--td-sunsetStrip);
}
.td-main-menu .dp-menu li:hover > a::before {
  color: var(--td-sunsetStrip) !important;
}
.td-main-menu .dp-menu li:hover > ul {
  opacity: 1;
  transform: none !important;
  pointer-events: all;
}
.td-main-menu .dp-menu li a {
  font-size: 15px;
  font-weight: 500;
  color: var(--td-heading);
  padding: 12px;
  display: block;
  transition: all 0.3s;
  text-align: left;
  cursor: pointer;
}
.td-main-menu .dp-menu li a i {
  margin-inline-end: 7px;
}
.td-main-menu .has-mega-menu {
  position: static;
}
.td-main-menu .mega-menu {
  background-color: var(--td-heading);
  padding: 30px 50px;
  width: 100%;
  position: absolute;
  inset-inline-start: -5px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 50px;
  justify-content: center;
  overflow: hidden;
  opacity: 0;
  pointer-events: none;
  z-index: 10;
  transition: all 0.3s;
  top: 80px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-main-menu .mega-menu {
    column-gap: 30px;
  }
}
.td-main-menu .mega-menu li:has(ul) > a:after {
  content: "";
}
.td-main-menu .mega-menu li a {
  font-size: 16px;
  font-weight: 500;
  color: #999999;
  padding: 12px;
  display: block;
  align-items: center;
  gap: 8px;
  background: var(--td-heading);
  border-radius: 8px;
}
.td-main-menu .mega-menu li a:hover {
  color: var(--td-white);
  letter-spacing: 0.5px;
}
.td-main-menu .mega-menu .title {
  font-weight: 600;
  color: var(--td-white);
  text-transform: uppercase;
  border-bottom: 1px solid #333337;
  padding-bottom: 20px;
  margin-bottom: 20px;
  pointer-events: none;
  border-radius: 0;
}
.td-main-menu .mega-style-two {
  padding: 0 15%;
  gap: 0;
  grid-template-columns: repeat(2, 1fr);
}
.td-main-menu .mega-style-two .title {
  padding-top: 28px;
}
.td-main-menu .mega-style-two .title {
  height: 70px;
  padding-bottom: 0;
  margin-bottom: 0;
  position: relative;
  overflow: visible;
  padding-inline-start: 30px;
}
.td-main-menu .mega-style-two .title:after {
  position: absolute;
  content: "";
  width: 5000px;
  height: 1px;
  background-color: #333337;
  bottom: -1px;
  inset-inline-start: 50%;
  transform: translateX(-50%);
}
.td-main-menu .mega-style-two > li ul:not(:first-child) {
  border-inset-inline-start: 1px solid #333337;
}
.td-main-menu .mega-style-two ul {
  column-count: 2;
  position: relative;
  padding: 20px 0;
}
.td-main-menu .mega-style-two ul:after {
  position: absolute;
  content: "";
  width: 1px;
  height: 700px;
  background-color: #333337;
  top: 0;
  inset-inline-start: 50%;
  z-index: 1;
}
.td-main-menu .mega-style-two ul li a {
  padding-inline-start: 30px;
}
.td-main-menu .mega-style-three {
  padding: 0 0 0 20px;
  gap: 0;
  grid-template-columns: repeat(3, 1fr);
}
.td-main-menu .mega-style-three .title {
  padding-top: 28px;
}
.td-main-menu .mega-style-three .title {
  height: 70px;
  padding-bottom: 0;
  margin-bottom: 0;
  position: relative;
  overflow: visible;
  padding-inline-start: 30px;
}
.td-main-menu .mega-style-three .title:after {
  position: absolute;
  content: "";
  width: 5000px;
  height: 1px;
  background-color: #333337;
  bottom: -1px;
  inset-inline-start: 50%;
  transform: translateX(-50%);
}
.td-main-menu .mega-style-three > li:not(:first-child) {
  border-inset-inline-start: 1px solid #333337;
}
.td-main-menu .mega-style-three > li:last-child {
  border: none;
  width: 36vw;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-main-menu .mega-style-three > li:last-child {
    width: 32vw;
  }
}
.td-main-menu .mega-style-three ul {
  column-count: 2;
  position: relative;
  padding: 20px 0;
  column-gap: 0;
}
.td-main-menu .mega-style-three ul:after {
  position: absolute;
  content: "";
  width: 1px;
  height: 700px;
  background-color: #333337;
  top: 0;
  inset-inline-start: 50%;
  z-index: 1;
}
.td-main-menu .mega-style-three ul li {
  margin: 0 10px;
}
.td-main-menu .mega-style-three ul li a {
  padding-inline-start: 20px;
}
.td-main-menu .mega-grid-six {
  grid-template-columns: repeat(6, 1fr);
  gap: 0 30px;
}
.td-main-menu .mega-grid-two {
  grid-template-columns: repeat(2, 1fr);
  row-gap: 60px;
}
.td-main-menu .list-three-column ul {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 50px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-main-menu .list-three-column ul {
    column-gap: 30px;
  }
}
.td-main-menu .span-first-item ul li:first-child {
  grid-column: 1/-1;
  column-span: all;
}
.td-main-menu .new {
  font-size: 10px;
  font-weight: 600;
  background: #FFA38E;
  color: var(--td-heading);
  padding: 5px 8px 3px;
  line-height: 1;
  display: inline-block;
  border-radius: 2px;
}

.mega-menu-thumb {
  width: 108%;
  aspect-ratio: 100/83;
  position: absolute;
  inset-inline-end: 0;
  bottom: 0;
  z-index: -1;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mega-menu-thumb {
    display: none;
  }
}
.mega-menu-thumb:after {
  position: absolute;
  content: "";
  width: 76%;
  height: 100%;
  top: 0;
  inset-inline-start: 0;
  background: linear-gradient(270deg, rgba(28, 29, 32, 0) 0%, #1C1D20 100%);
}
.mega-menu-thumb .laptop-view {
  width: 70%;
  aspect-ratio: 100/114;
  object-fit: cover;
  object-position: center top;
  position: absolute;
  inset-inline-end: 70px;
  bottom: 0;
}

.mega-menu-counter__item {
  text-align: center;
  display: inline-block;
  margin-top: 35%;
  margin-inline-start: 17%;
  position: relative;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .mega-menu-counter__item {
    display: none;
  }
}
.mega-menu-counter__text p {
  font-size: 30px;
  line-height: 28px;
  color: var(--td-white);
  font-weight: 500;
}
.mega-menu-counter__number {
  font-size: 150px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 24px;
  color: var(--td-white);
  background: linear-gradient(136deg, #9479FF 0%, #FFA6D6 47.92%, #FFFCE3 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.menu-icon {
  width: 26px;
  height: 18px;
  position: relative;
  display: block;
}
.menu-icon::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  top: 0;
  inset-inline-start: 0;
  background: var(--td-white);
  transition: all 0.3s;
}
.menu-icon::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  bottom: 0;
  inset-inline-start: 0;
  background: var(--td-white);
  transition: all 0.3s;
}
.menu-icon span {
  position: absolute;
  content: "";
  width: 18px;
  height: 1px;
  top: 50%;
  inset-inline-end: 0;
  transition: all 0.3s;
  background-color: var(--td-white);
}
.menu-icon:hover::after, .menu-icon:hover::before,
.menu-icon:hover span {
  background-color: var(--td-sunsetStrip);
}

/*----------------------------------------*/
/*  Meanmenu css
/*----------------------------------------*/
.mean-container {
  margin-bottom: 35px;
}
.mean-container a.meanmenu-reveal {
  width: 22px;
  height: 22px;
  padding: 13px 13px 11px 13px;
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  cursor: pointer;
  color: var(--td-white);
  text-decoration: none;
  font-size: 16px;
  text-indent: -9999em;
  line-height: 22px;
  font-size: 1px;
  font-weight: 700;
  display: none !important;
}
.mean-container a.meanmenu-reveal span {
  display: block;
  background: var(--td-white);
  height: 3px;
  margin-top: 3px;
}
.mean-container .mean-push {
  float: left;
  width: 100%;
  padding: 0;
  margin: 0;
  clear: both;
}
.mean-container .mean-nav {
  background: none;
  margin-top: 0;
  float: left;
  width: 100%;
}
.mean-container .mean-nav .wrapper {
  width: 100%;
  padding: 0;
  margin: 0;
}
.mean-container .mean-nav > ul {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
  display: block !important;
}
.mean-container .mean-nav > ul > li:first-child > a {
  border-top: 0 !important;
}
.mean-container .mean-nav ul {
  padding: 0;
  margin: 0;
  width: 100%;
  list-style-type: none;
}
.mean-container .mean-nav ul li {
  position: relative;
  float: left;
  width: 100%;
}
.mean-container .mean-nav ul li.dropdown-opened > a, .mean-container .mean-nav ul li.dropdown-opened > span {
  color: var(--td-sunsetStrip);
}
.mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked, .mean-container .mean-nav ul li.dropdown-opened > span.mean-expand.mean-clicked {
  background: var(--td-white);
  color: var(--td-sunsetStrip);
  border-color: transparent;
}
.mean-container .mean-nav ul li.dropdown-opened > a.mean-expand.mean-clicked i, .mean-container .mean-nav ul li.dropdown-opened > span.mean-expand.mean-clicked i {
  color: var(--td-sunsetStrip);
  transform: rotate(90deg);
}
.mean-container .mean-nav ul li.mean-last {
  border-bottom: none;
  margin-bottom: 0;
}
.mean-container .mean-nav ul li > a.mean-expand i {
  display: inline-block;
  transition: 0.3s;
}
.mean-container .mean-nav ul li > a > i {
  display: none;
}
.mean-container .mean-nav ul li a, .mean-container .mean-nav ul li span {
  display: block;
  float: left;
  width: 90%;
  padding: 10px 5%;
  margin: 0;
  text-align: left;
  text-decoration: none;
  width: 100%;
  padding: 10px 0;
  color: var(--td-heading);
  border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
  font-size: 15px;
  line-height: 1.5;
  font-weight: 500;
}
.mean-container .mean-nav ul li a:hover, .mean-container .mean-nav ul li span:hover {
  color: var(--td-sunsetStrip);
}
.mean-container .mean-nav ul li a:hover i, .mean-container .mean-nav ul li span:hover i {
  color: var(--td-sunsetStrip);
}
.mean-container .mean-nav ul li a.mean-expand, .mean-container .mean-nav ul li span.mean-expand {
  text-align: center;
  position: absolute;
  inset-inline-end: 3px;
  top: 0;
  z-index: 2;
  font-weight: 700;
  background: transparent;
  border: none !important;
  font-size: 15px;
  margin-top: 5px;
  padding: 0 !important;
  line-height: 30px;
  top: 0;
  font-weight: 400;
  width: max-content;
}
.mean-container .mean-nav ul li a.mean-expand:hover, .mean-container .mean-nav ul li span.mean-expand:hover {
  border-color: var(--td-sunsetStrip);
}
.mean-container .mean-nav ul li a.mean-expand:hover i, .mean-container .mean-nav ul li span.mean-expand:hover i {
  color: var(--td-sunsetStrip);
}
.mean-container .mean-nav ul li a.mean-expand.mean-clicked, .mean-container .mean-nav ul li span.mean-expand.mean-clicked {
  color: var(--td-sunsetStrip);
}
.mean-container .mean-nav ul li li a {
  width: 90%;
  padding: 10px 7%;
  text-shadow: none !important;
  visibility: visible;
}
.mean-container .mean-nav ul li li li a {
  width: 80%;
  padding: 10px 12%;
}
.mean-container .mean-nav ul li li li li a {
  width: 70%;
  padding: 10px 17%;
}
.mean-container .mean-nav ul li li li li li a {
  width: 60%;
  padding: 10px 20%;
}
.mean-container .mean-bar {
  padding: 0;
  min-height: auto;
  background: none;
  float: left;
  width: 100%;
  position: relative;
  padding: 4px 0;
  min-height: 42px;
  z-index: 999999;
}
.mean-container .mean-bar, .mean-container .mean-bar * {
  /* Fix for box sizing on Foundation Framework etc. */
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

/*----------------------------------------*/
/* Newsletter style
/*----------------------------------------*/
.td-newsletter-area {
  position: relative;
  z-index: 5;
}

.newsletter-main {
  padding: 100px 100px;
  border-radius: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-main {
    padding: 40px 50px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-main {
    padding: 40px 50px 0;
  }
}
@media (max-width: 480px) {
  .newsletter-main {
    padding: 35px 20px 0;
  }
}
.newsletter-main .newsletter-contents .title {
  font-size: 50px;
  color: var(--td-white);
  margin-bottom: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-main .newsletter-contents .title {
    font-size: 40px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-main .newsletter-contents .title {
    font-size: 32px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .newsletter-main .newsletter-contents .title {
    font-size: 28px;
  }
}
@media (max-width: 575px) {
  .newsletter-main .newsletter-contents .title {
    font-size: 24px;
  }
}
.newsletter-main .newsletter-contents .description {
  color: var(--td-white);
  font-size: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-main .newsletter-contents .description {
    font-size: 18px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-main .newsletter-contents .description {
    font-size: 16px;
  }
}
.newsletter-main .newsletter-form {
  margin-top: 40px;
}
.newsletter-main .newsletter-form form {
  display: flex;
  align-items: center;
  gap: 10px 15px;
}
@media (max-width: 575px) {
  .newsletter-main .newsletter-form form {
    flex-direction: column;
  }
}
.newsletter-main .newsletter-form form input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border-radius: 11px;
  color: var(--td-white);
}
.newsletter-main .newsletter-form form input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: var(--td-white);
}
.newsletter-main .newsletter-form form input::-moz-placeholder { /* Firefox 19+ */
  color: var(--td-white);
}
.newsletter-main .newsletter-form form input:-moz-placeholder { /* Firefox 4-18 */
  color: var(--td-white);
}
.newsletter-main .newsletter-form form input:-ms-input-placeholder { /* IE 10+  Edge*/
  color: var(--td-white);
}
.newsletter-main .newsletter-form form input::placeholder { /* MODERN BROWSER */
  color: var(--td-white);
}
.newsletter-main .newsletter-form form .td-btn.submit-btn {
  height: 50px;
  padding: 0 30px;
  background-color: var(--td-white);
  color: var(--td-primary);
  border-radius: 10px;
}
@media (max-width: 575px) {
  .newsletter-main .newsletter-form form .td-btn.submit-btn {
    width: 100%;
  }
}
.newsletter-main .newsletter-thumb {
  max-width: 358px;
  position: absolute;
  bottom: 0;
  inset-inline-start: 50%;
  transform: translateX(50%);
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .newsletter-main .newsletter-thumb {
    inset-inline-start: inherit;
    transform: inherit;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .newsletter-main .newsletter-thumb {
    inset-inline-start: inherit;
    transform: inherit;
    position: inherit;
    margin: 0 auto;
  }
}
[dir=rtl] .newsletter-main .newsletter-thumb {
  -webkit-transform: translateX(-50%) scaleX(-1);
  -moz-transform: translateX(-50%) scaleX(-1);
  -ms-transform: translateX(-50%) scaleX(-1);
  -o-transform: translateX(-50%) scaleX(-1);
  transform: translateX(-50%) scaleX(-1);
  bottom: -1px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  [dir=rtl] .newsletter-main .newsletter-thumb {
    -webkit-transform: scaleX(-1);
    -moz-transform: scaleX(-1);
    -ms-transform: scaleX(-1);
    -o-transform: scaleX(-1);
    transform: scaleX(-1);
  }
}

/*----------------------------------------*/
/* Price style
/*----------------------------------------*/
.rock-pricing-main {
  padding-top: 130px;
  padding-bottom: 130px;
  position: relative;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .rock-pricing-main {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .rock-pricing-main {
    padding-top: 45px;
    padding-bottom: 0px;
  }
}

.rock-pricing-section {
  padding-bottom: 70px;
}

.price-world-bg {
  position: absolute;
  top: -40px;
  height: 100%;
  width: 110%;
  z-index: -1;
  opacity: 0.4;
  inset-inline-start: 50%;
  transform: translateX(-50%);
}

.rock-pricing-item {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  padding: 30px 30px;
  position: relative;
  z-index: 5;
  overflow: hidden;
}
.rock-pricing-item:before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  padding: 2px;
  background: linear-gradient(139.9deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}
@media (max-width: 575px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .rock-pricing-item {
    padding: 25px 20px 25px;
  }
}
.rock-pricing-item.is-active {
  background: rgba(255, 146, 17, 0.1);
  backdrop-filter: blur(4px);
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
}
.rock-pricing-item.is-active::before {
  background: linear-gradient(139.9deg, rgba(255, 163, 54, 0.4) 0%, rgba(255, 163, 54, 0) 100%);
}
.rock-pricing-item.outline-hidden::before {
  display: none;
}
.rock-pricing-item .item-title {
  font-size: 24px;
  margin-bottom: 10px;
  color: var(--td-white);
}
@media xs {
  .rock-pricing-item .item-title {
    font-size: 20px;
  }
}
.rock-pricing-item .price-value {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 10px;
}
.rock-pricing-item .price-badge {
  background: rgba(255, 255, 255, 0.08);
  mix-blend-mode: normal;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 32px;
  padding: 5px 11px;
  display: inline-block;
  color: var(--td-white);
  font-size: 10px;
  font-weight: 500;
}
.rock-pricing-item .price-suggest {
  font-size: 12px;
  color: var(--td-seaweed);
  font-weight: 500;
}
.rock-pricing-item .price-list {
  margin-top: 30px;
  margin-bottom: 25px;
}
.rock-pricing-item .price-list ul li {
  list-style: none;
}
.rock-pricing-item .price-list ul li:not(:last-child) {
  margin-bottom: 12px;
}
.rock-pricing-item .price-list ul li .single-list {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.rock-pricing-item .price-list ul li .single-list .list-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: start;
  gap: 10px;
}
.rock-pricing-item .price-list ul li .single-list .list-content .list-item-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}
.rock-pricing-item .price-list ul li .single-list .list-content .icon-list li {
  list-style: none;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: start;
  gap: 10px;
}
.rock-pricing-item .price-list ul li .single-list .list-content .icon-list li:not(:last-child) {
  margin-bottom: 20px;
}
.rock-pricing-item .price-list ul li .single-list .list-content .icon-list li .list-item-text {
  font-weight: 500;
}
.rock-pricing-item .price-list ul li .single-list .list-valu span {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-seaweed);
}
.rock-pricing-item .price-btn-wrp {
  text-align: center;
}
.rock-pricing-item .price-btn-wrp .description {
  color: var(--td-seaweed);
  font-size: 10px;
  font-weight: 500;
  margin-top: 8px;
}
.rock-pricing-item .price-shape {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  z-index: -1;
}

@media only screen and (min-width: 1600px) and (max-width: 1800px) {
  .rock-dashboard-pricing-section .rock-pricing-item {
    padding: 25px 25px;
  }
}

/*----------------------------------------*/
/* video style
/*----------------------------------------*/
.rock-video-section {
  margin-bottom: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .rock-video-section {
    margin-bottom: 0;
  }
}

.rock-video-thumb {
  position: relative;
}
.rock-video-thumb img {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  width: 100%;
}
.rock-video-thumb .play-btn {
  width: 72px;
  height: 72px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.34);
  border: 1px solid rgba(255, 255, 255, 0.7);
  color: var(--td-warning);
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
}
.rock-video-thumb .play-btn::before {
  content: "";
  display: inline-block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.7);
  top: 0;
  inset-inline-start: 0;
  inset-inline-end: 0px;
  bottom: 0px;
  z-index: 0;
  animation-name: popupBtn;
  animation-duration: 1.6s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  z-index: -1;
}
.rock-video-thumb .play-btn::after {
  content: "";
  display: inline-block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.7);
  top: 0;
  inset-inline-start: 0;
  inset-inline-end: 0;
  bottom: 0px;
  z-index: 0;
  animation-name: popupBtn;
  animation-duration: 1.8s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  z-index: -1;
}

/*----------------------------------------*/
/* rock why choose style
/*----------------------------------------*/
.rock-why-choose-section {
  padding-top: 250px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .rock-why-choose-section {
    padding-top: 100px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .rock-why-choose-section {
    padding-top: 65px;
  }
}

.rock-why-choose-grid {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
  grid-template-columns: 273px 273px 273px 273px;
  justify-content: center;
  align-items: center;
  gap: 30px;
  margin-top: 85px;
  margin-bottom: 50px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .rock-why-choose-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .rock-why-choose-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .rock-why-choose-grid {
    grid-template-columns: 1fr;
  }
}

.rock-why-choose-item {
  text-align: center;
}
.rock-why-choose-item .icon {
  margin-bottom: 30px;
}
.rock-why-choose-item .icon span {
  width: 104px;
  height: 104px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  position: relative;
}
.rock-why-choose-item .icon span:before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  padding: 2px;
  background: linear-gradient(145.53deg, rgba(255, 255, 255, 0) 57.4999988079%, rgb(255, 255, 255) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}
.rock-why-choose-item .content .title {
  font-size: 16px;
  margin-bottom: 20px;
  color: var(--td-white);
}
.rock-why-choose-item .content .description {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
}

/*----------------------------------------*/
/* categories style
/*----------------------------------------*/
.categories-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 30px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .categories-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .categories-grid {
    grid-template-columns: 1fr;
  }
}
.categories-grid .category-item:nth-child(2n) {
  margin-top: 30px;
}

.category-item {
  padding: 30px 20px 30px;
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 30px;
  height: max-content;
  position: relative;
  overflow: hidden;
  display: block;
  z-index: 1;
}
.category-item .icon {
  margin-bottom: 28px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .category-item .icon {
    margin-bottom: 20px;
  }
}
.category-item .contents .title {
  margin-bottom: 18px;
  font-size: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .category-item .contents .title {
    font-size: 16px;
  }
}
.category-item .contents .title a:hover {
  color: var(--td-primary);
}
.category-item .contents .description {
  font-size: 14px;
  font-weight: 500;
}
.category-item .category-shape {
  position: absolute;
  background-color: rgba(0, 101, 255, 0.2);
  width: 112px;
  height: 121.5px;
  clip-path: polygon(0% 99.18%, 35.253% 99.109%, 72.768% 99.59%, 99.949% 98.975%, 99.949% 75.582%, 100% 60.008%, 100% 29.357%, 100% 0%, 100% 0%, 99.923% 0.739%, 99.614% 2.841%, 98.955% 6.131%, 97.829% 10.434%, 96.117% 15.577%, 93.701% 21.385%, 90.464% 27.684%, 86.288% 34.3%, 81.056% 41.058%, 74.649% 47.785%, 74.649% 47.785%, 66.43% 54.535%, 57.332% 60.475%, 47.749% 65.774%, 38.071% 70.602%, 28.692% 75.128%, 20.004% 79.522%, 12.398% 83.955%, 6.267% 88.596%, 2.004% 93.614%, 0% 99.18%);
  bottom: -1px;
  inset-inline-end: 0;
  z-index: -1;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .category-item .category-shape {
    width: 92px;
    height: 92px;
  }
}
[dir=rtl] .category-item .category-shape {
  bottom: -9px;
  transform: rotate(90deg);
}
.category-item:hover .category-shape {
  background-color: var(--td-primary);
}

.category-bg-shape {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
}

.categories-card {
  position: absolute;
  inset-inline-start: 0;
  width: 40%;
  top: 50%;
  transform: translateY(-50%);
  padding: 150px 80px 150px 175px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .categories-card {
    padding: 60px 50px 60px 50px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .categories-card {
    padding: 50px 50px 50px 50px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .categories-card {
    padding: 50px 50px 50px 50px;
    position: inherit;
    top: inherit;
    transform: inherit;
    width: 100%;
  }
}

.category-item-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 64px 30px;
  margin-bottom: 82px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .category-item-grid {
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 0;
  }
}
@media (max-width: 480px) {
  .category-item-grid {
    grid-template-columns: 1fr;
  }
}

.category-item-two {
  background: #EBF3FF;
  border: 1px solid rgba(0, 101, 255, 0.88);
  border-radius: 15px;
  padding: 50px 25px 45px;
  position: relative;
  height: max-content;
}
.category-item-two .icon {
  position: absolute;
  top: -30px;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  z-index: 5;
}
.category-item-two .icon span {
  width: 64px;
  height: 64px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--td-white);
  border-radius: 50%;
  border: 1px solid var(--td-primary);
}
.category-item-two .contents .title {
  font-size: 20px;
  margin-bottom: 5px;
}
.category-item-two .contents .title a:hover {
  color: var(--td-primary);
}
.category-item-two .contents .description {
  font-size: 14px;
}
.category-item-two:nth-child(2), .category-item-two:nth-child(5), .category-item-two:nth-child(8), .category-item-two:nth-child(11) {
  position: relative;
  top: 45px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .category-item-two:nth-child(2), .category-item-two:nth-child(5), .category-item-two:nth-child(8), .category-item-two:nth-child(11) {
    top: inherit;
  }
}
.category-item-two:nth-child(3n) {
  position: relative;
  top: 110px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .category-item-two:nth-child(3n) {
    top: inherit;
  }
}

.category-product-title {
  margin-bottom: 30px;
  font-weight: 700;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .category-product-title {
    font-size: 26px;
  }
}

.product-filter-category {
  padding: 30px;
  background-color: var(--td-white);
  border-radius: 6px;
}

.product-filter-tile {
  font-size: 16px;
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.product-filter-list ul li {
  list-style: none;
}
.product-filter-list ul li:not(:last-child) {
  margin-bottom: 16px;
}
.product-filter-list ul li a {
  padding: 0px 16px;
  background-color: var(--td-alice-blue);
  width: 100%;
  height: 40px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}
.product-filter-list ul li a::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 5px;
  background: var(--td-yellow);
  inset-inline-start: 0;
  opacity: 0;
}
.product-filter-list ul li a:hover {
  color: var(--td-white);
  background-color: var(--td-primary);
}
.product-filter-list ul li a:hover::before {
  opacity: 1;
}

.filter-price .dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}
.filter-price .dropdown.active .dropdown-content {
  display: block;
}
.filter-price .dropdown-select {
  width: 100%;
  font-size: 14px;
  cursor: pointer;
  text-align: left;
  background: var(--td-alice-blue);
  border-radius: 5px;
  padding: 10px 15px;
  font-weight: 500;
  height: 40px;
  display: inline-flex;
  align-items: center;
  position: relative;
}
.filter-price .dropdown-select::after {
  position: absolute;
  content: "\e92b";
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 10px;
  font-family: "icomoon";
}
.filter-price .filter-option-title {
  font-size: 14px;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.filter-price .dropdown-content {
  display: none;
  background: var(--td-white);
  width: 180px;
  box-shadow: 4px 4px 20px 0px rgba(0, 0, 0, 0.1);
  padding: 15px 20px;
  position: absolute;
  top: 100%;
  inset-inline-end: 0;
}
.filter-price .dropdown-content .filter-option:not(:last-child) {
  margin-bottom: 3px;
}

/*----------------------------------------*/
/*  Postbox css
/*----------------------------------------*/
.postbox-top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 35px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .postbox-top {
    flex-direction: column;
  }
}

.postbox-top-content .title-inner {
  margin-bottom: 20px;
}
.postbox-top-content .title-inner > span {
  font-size: 14px;
}
.postbox-top-content .title-inner .title {
  display: inline-block;
  font-size: 24px;
}
@media (max-width: 575px) {
  .postbox-top-content .title-inner .title {
    font-size: 20px;
  }
}

.share-social ul {
  display: flex;
  align-items: center;
  justify-content: end;
  gap: 10px 15px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .share-social ul {
    justify-content: start;
  }
}
.share-social ul li {
  list-style: none;
}
.share-social ul li a {
  width: 30px;
  height: 30px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--td-primary);
  color: var(--td-primary);
}
.share-social ul li a:hover {
  transform: translateY(-2px);
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: transparent;
}

.postbox-top-share .blog-meta-inner {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 5px 20px;
}

.postbox-text p {
  line-height: 26px;
}
.postbox-text p b {
  color: var(--td-heading);
}

.postbox-thumb img {
  border-radius: 10px;
}

.postbox-title h4 {
  font-size: 20px;
  color: var(--td-heading);
  margin-bottom: 15px;
}
@media (max-width: 480px) {
  .postbox-title h4 {
    font-size: 18px;
  }
}

.sidebar-wrapper {
  padding: 25px 30px 30px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
}
@media (max-width: 480px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .sidebar-wrapper {
    padding: 20px 20px;
  }
}

.sidebar-widget-title {
  margin-bottom: 25px;
  color: var(--td-white);
  font-size: 22px;
}
@media (max-width: 480px) {
  .sidebar-widget-title {
    font-size: 20px;
  }
}

.rc-post-item {
  display: flex;
  align-items: start;
  gap: 20px;
  padding: 12px 12px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.08);
  mix-blend-mode: normal;
  border: 1px solid rgba(255, 255, 255, 0.08);
}
.rc-post-item:not(:last-child) {
  margin-bottom: 20px;
}

.rc-post-title {
  font-size: 15px;
  color: var(--td-white);
  margin-bottom: 8px;
}
@media (max-width: 480px) {
  .rc-post-title {
    font-size: 14px;
    line-height: 1.2;
  }
}
.rc-post-title a:hover {
  color: var(--td-primary);
}

.rc-meta p {
  font-size: 12px;
  margin-bottom: 0;
}

.rc-post-thumb {
  width: 80px;
  height: 90px;
  min-width: 80px;
}
@media (max-width: 480px) {
  .rc-post-thumb {
    max-width: 66px;
  }
}
.rc-post-thumb img {
  border-radius: 10px;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.rc-title {
  font-size: 18px;
  line-height: 1.3;
  font-weight: 500;
  letter-spacing: -0.2px;
  margin-bottom: 2px;
}
.rc-title a:hover {
  color: #ffffff;
}

.rc-meta span {
  font-size: 14px;
  position: relative;
  padding-inline-start: 15px;
}
.rc-meta span::before {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  background: rgba(124, 126, 130, 0.5);
  top: 50%;
  inset-inline-start: 0;
}

/*----------------------------------------*/
/* Blog style
/*----------------------------------------*/
.blog-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}
.blog-meta .thumb {
  max-width: 20px;
  height: 20px;
  display: inline-flex;
  flex: 0 0 auto;
}
.blog-meta .thumb img {
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  border: 1px solid var(--td-primary);
}
.blog-meta .content h6 {
  font-size: 16px;
}
.blog-meta .content h6 span {
  color: var(--td-primary);
}
.blog-meta .content h6 span a:hover {
  text-decoration: underline;
}

.single-blog-item .blog-thumb-inner {
  position: relative;
}
.single-blog-item .blog-thumb-inner .blog-thumb {
  margin-bottom: 15px;
}
.single-blog-item .blog-thumb-inner .blog-thumb img {
  border-radius: 10px;
  width: 100%;
}
.single-blog-item .blog-thumb-inner .blog-badge {
  position: absolute;
  top: 20px;
  inset-inline-start: 20px;
}
.single-blog-item .blog-thumb-inner .blog-badge .td-badge {
  border: 1px solid rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  color: var(--td-white);
  padding: 8px 16px;
}
.single-blog-item .blog-meta-wrap {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.single-blog-item .blog-content .title {
  font-size: 20px;
  margin-bottom: 15px;
  font-weight: 700;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .single-blog-item .blog-content .title {
    font-size: 18px;
  }
}
.single-blog-item .blog-content .title a:hover {
  color: var(--td-primary);
}
.single-blog-item .blog-content .description {
  font-size: 14px;
  font-weight: 400;
}

.blog-shapes .shape-one {
  position: absolute;
  inset-inline-end: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
}

.blog-more-update {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  padding: 30px 30px;
  border-radius: 10px;
}
.blog-more-update .heading {
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 15px;
}
.blog-more-update .heading .title {
  color: var(--td-white);
  font-size: 18px;
}
.blog-more-update .description {
  color: var(--td-white);
  margin-bottom: 20px;
}
.blog-more-update .form-inner .input-field {
  margin-bottom: 20px;
}
.blog-more-update .form-inner .input-field input {
  background: rgba(240, 246, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  color: var(--td-white);
}
.blog-more-update .form-inner .input-field input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: var(--td-white);
}
.blog-more-update .form-inner .input-field input::-moz-placeholder { /* Firefox 19+ */
  color: var(--td-white);
}
.blog-more-update .form-inner .input-field input:-moz-placeholder { /* Firefox 4-18 */
  color: var(--td-white);
}
.blog-more-update .form-inner .input-field input:-ms-input-placeholder { /* IE 10+  Edge*/
  color: var(--td-white);
}
.blog-more-update .form-inner .input-field input::placeholder { /* MODERN BROWSER */
  color: var(--td-white);
}
.blog-more-update .form-inner .td-btn {
  background-color: var(--td-white);
  color: var(--td-primary);
  padding: 0 30px;
  border-radius: 30px;
  gap: 5px;
  min-width: 140px;
}

.blog-sldebar-heading .title {
  font-size: 18px;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/*----------------------------------------*/
/*  3.2.5 Sidebar css
/*----------------------------------------*/
.tagcloud a {
  font-size: 14px;
  display: inline-block;
  padding: 9px 15px 9px;
  margin-bottom: 12px;
  margin-inline-end: 12px;
  position: relative;
  background: var(--td-white);
  text-transform: capitalize;
  color: var(--td-primary);
  border-radius: 30px;
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
}
.tagcloud a:hover {
  color: var(--td-white);
  background-color: var(--td-primary);
  border-color: var(--td-primary);
}

/*----------------------------------------*/
/*  banner css
/*----------------------------------------*/
.td-banner-area.banner-style-one {
  padding: 120px 0px;
  background-color: var(--td-primary);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one {
    padding: 100px 0px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-one {
    padding: 80px 0px 80px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .td-banner-area.banner-style-one {
    padding: 70px 0px 70px;
  }
}
.td-banner-area.banner-style-one .banner-contents {
  margin-bottom: 45px;
}
.td-banner-area.banner-style-one .banner-contents .description {
  font-size: 20px;
  color: var(--td-white);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one .banner-contents .description {
    font-size: 16px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-one .banner-contents .description {
    font-size: 18px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .td-banner-area.banner-style-one .banner-contents .description {
    font-size: 16px;
  }
}
.td-banner-area.banner-style-one .banner-contents .title {
  color: var(--td-white);
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-banner-area.banner-style-one .banner-contents .title {
    font-size: 38px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one .banner-contents .title {
    font-size: 36px;
  }
}
.td-banner-area.banner-style-one .banner-filter {
  position: relative;
  max-width: 575px;
}
.td-banner-area.banner-style-one .banner-filter .filter-input {
  background-color: var(--td-white);
  border: 0;
  border-radius: 30px;
  height: 58px;
  padding-inline-end: 130px;
}
.td-banner-area.banner-style-one .banner-filter .filter-btn {
  position: absolute;
  top: 50%;
  inset-inline-end: 10px;
  background-color: var(--td-primary);
  color: var(--td-white);
  border-radius: 30px;
  transform: translateY(-50%);
}
.td-banner-area.banner-style-one .banner-products-list {
  margin-top: 40px;
  padding: 20px 30px;
  display: grid;
  grid-template-columns: auto auto auto;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  border-radius: 12px;
  max-width: 575px;
}
@media (max-width: 480px) {
  .td-banner-area.banner-style-one .banner-products-list {
    padding: 15px 15px;
  }
}
.td-banner-area.banner-style-one .banner-products-list .list-item {
  text-align: center;
}
.td-banner-area.banner-style-one .banner-products-list .list-item .icon {
  margin-bottom: 15px;
}
.td-banner-area.banner-style-one .banner-products-list .list-item .content .title {
  font-size: 20px;
  color: var(--td-white);
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .td-banner-area.banner-style-one .banner-products-list .list-item .content .title {
    font-size: 16px;
  }
}
@media (max-width: 480px) {
  .td-banner-area.banner-style-one .banner-products-list .list-item .content .title {
    font-size: 14px;
  }
}
.td-banner-area.banner-style-one .banner-gallery-wrapper {
  -webkit-transform: rotate(30deg);
  -moz-transform: rotate(30deg);
  -ms-transform: rotate(30deg);
  -o-transform: rotate(30deg);
  transform: rotate(30deg);
  display: flex;
  gap: 10px;
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  z-index: -1;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .td-banner-area.banner-style-one .banner-gallery-wrapper {
    top: 50px;
    width: 55%;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-banner-area.banner-style-one .banner-gallery-wrapper {
    top: 5%;
    width: 55%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one .banner-gallery-wrapper {
    width: 40%;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-banner-area.banner-style-one .banner-gallery-wrapper {
    transform: inherit;
    position: inherit;
  }
}
[dir=rtl] .td-banner-area.banner-style-one .banner-gallery-wrapper {
  -webkit-transform: rotate(335deg);
  -moz-transform: rotate(335deg);
  -ms-transform: rotate(335deg);
  -o-transform: rotate(335deg);
  transform: rotate(335deg);
}
.td-banner-area.banner-style-one .banner-gallery-wrapper .gallery-thumb:nth-child(1) {
  transform: translateY(-80px);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one .banner-gallery-wrapper .gallery-thumb:nth-child(1) {
    transform: translateY(0px);
  }
}
.td-banner-area.banner-style-one .banner-gallery-wrapper .gallery-thumb:nth-child(2) {
  transform: translateY(-450px);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one .banner-gallery-wrapper .gallery-thumb:nth-child(2) {
    transform: translateY(0px);
  }
}
.td-banner-area.banner-style-one .banner-gallery-wrapper .gallery-thumb:nth-child(3) {
  transform: translateY(-220px);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-banner-area.banner-style-one .banner-gallery-wrapper .gallery-thumb:nth-child(3) {
    transform: translateY(0px);
  }
}
.td-banner-area.banner-style-one .banner-pattern-bg {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 58%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  z-index: -1;
}

/*----------------------------------------*/
/*  Header css
/*----------------------------------------*/
.header-transparent {
  position: absolute;
  inset-inline-start: 0;
  margin: auto;
  width: 100%;
  z-index: 99;
  background-color: transparent;
}

.header-primary {
  padding: 30px 30px;
}
@media (max-width: 575px) {
  .header-primary {
    padding: 20px 15px;
  }
}

.header-logo img {
  max-height: 35px;
}

.active-sticky {
  position: fixed !important;
  top: 0;
  z-index: 111;
  inset-inline-end: 0;
  inset-inline-start: 0;
  width: 100%;
  background-color: #0f2167;
  box-shadow: 0px 20px 40px rgba(37, 9, 62, 0.1);
  animation: sticky 0.3s;
  -webkit-animation: sticky 0.3s;
}

.header-lang-item {
  position: relative;
}
.header-lang-item > span {
  position: relative;
  font-size: 14px;
  transition: all 0.1s linear;
  z-index: 1;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  display: inline-flex;
  align-items: center;
  gap: 5px;
  flex-direction: row-reverse;
}
.header-lang-item > span:hover {
  cursor: pointer;
}
.header-lang-item ul {
  position: absolute;
  top: calc(100% + 20px);
  inset-inline-end: 0;
  z-index: 33;
  padding: 10px 15px;
  visibility: hidden;
  opacity: 0;
  min-width: 150px;
  -webkit-transition: all 0.2s 0s ease-out;
  -moz-transition: all 0.2s 0s ease-out;
  -ms-transition: all 0.2s 0s ease-out;
  -o-transition: all 0.2s 0s ease-out;
  transition: all 0.2s 0s ease-out;
  background: var(--td-white);
  mix-blend-mode: normal;
  box-shadow: 0px 1px 3px rgba(3, 4, 28, 0.12);
  backdrop-filter: blur(50px);
  border-radius: 10px;
}
@media (max-width: 480px) {
  .header-lang-item ul {
    inset-inline-start: auto;
    inset-inline-end: 0;
  }
}
.header-lang-item ul.lang-list-open {
  visibility: visible;
  opacity: 1;
}
.header-lang-item ul li {
  list-style: none;
}
.header-lang-item ul li:not(:last-child) {
  margin-bottom: 3px;
}
.header-lang-item ul li span.icon {
  display: none;
  transition: none;
}
.header-lang-item ul li .active span.icon {
  display: inline-block;
}
.header-lang-item ul li a {
  font-size: 14px;
  font-weight: 500;
}
.header-lang-item ul li a:hover {
  color: var(--td-primary);
}

.header-layout {
  background: #0f2167;
}

@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-header-top-area {
    padding: 18px 0;
  }
}
.td-header-top-area .header-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.td-header-top-area .header-right {
  display: flex;
  align-items: center;
  gap: 80px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-header-top-area .header-right {
    gap: 20px;
  }
}
.td-header-top-area .header-right .header-lang-switcher {
  display: flex;
  align-items: center;
  gap: 20px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-header-top-area .header-right .header-lang-switcher {
    gap: 12px;
  }
}
.td-header-top-area .header-right .header-quick-action {
  display: flex;
  align-items: center;
  gap: 30px;
}
@media (max-width: 575px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-header-top-area .header-right .header-quick-action {
    gap: 20px;
  }
}

.mode-switcher button {
  position: relative;
  top: -3px;
}

.td-header-bottom-area {
  position: relative;
  z-index: 30;
}
.td-header-bottom-area.has-border-top {
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}
.td-header-bottom-area .header-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow-x: clip;
}
.td-header-bottom-area .header-inner .header-right {
  display: flex;
  align-items: center;
  gap: 35px;
}

.header-text-btn {
  font-weight: 800;
  color: #576363;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  position: relative;
  font-size: 14px;
  color: var(--td-white);
  font-weight: 600;
}
.header-text-btn i {
  font-size: 18px;
}
.header-text-btn::after {
  position: absolute;
  content: "";
  width: calc(100% - 25px);
  height: 1px;
  bottom: -2px;
  inset-inline-end: 0;
  background-color: var(--td-white);
  transform: scaleX(1);
  transform-origin: bottom right;
  transition: transform 0.3s;
}
.header-text-btn:hover {
  color: var(--td-white);
}
.header-text-btn:hover::after {
  transform-origin: bottom left;
  transform: scaleX(0);
}
.header-text-btn:focus {
  color: var(--td-white);
}

.quick-access > ul {
  display: flex;
  align-items: center;
  gap: 10px;
}
.quick-access > ul > li {
  list-style: none;
}

.quick-access-item {
  width: 30px;
  height: 30px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  position: relative;
}
.quick-access-item .quick-access-count {
  position: absolute;
  width: 14px;
  height: 14px;
  background: #C70959;
  font-size: 8px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--td-white);
  top: -1px;
  inset-inline-start: 0px;
}
.quick-access-item .quick-access-btn {
  color: var(--td-white);
}
.quick-access-item .quick-access-user img {
  border: 1px solid var(--td-white);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
}

.td-user-menu-list-wrapper {
  min-width: 230px;
  background-color: var(--td-white);
  border-top: 1px solid rgba(230, 227, 241, 0.31);
  position: absolute;
  top: 60px;
  inset-inline-end: 0;
  visibility: hidden;
  clip: rect(0, 200vw, 0, 0);
  opacity: 0;
  transition: opacity 0.4s linear, clip 0.6s linear, visibility 0s 0.4s;
  transform: translateZ(0);
  z-index: 99;
  box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 10px 10px;
}
.td-user-menu-list-wrapper .inner {
  padding: 20px 20px;
}
.td-user-menu-list-wrapper .inner .devider {
  margin-top: 10px;
  margin-bottom: 10px;
}

@media (max-width: 480px) {
  .quick-access {
    display: none;
  }
}

.quick-access-user {
  position: relative;
  cursor: pointer;
}
.quick-access-user.quick-user-click.active .td-user-menu-list-wrapper {
  visibility: visible;
  opacity: 1;
  clip: rect(0, 100vw, 200vh, -30px);
  transition: clip 0.6s linear, opacity 0.4s linear;
}

.td-admin-profile {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  text-align: start;
}
.td-admin-profile .title {
  font-size: 14px;
  font-weight: 600;
}
.td-admin-profile .info {
  font-size: 14px;
}
.td-admin-profile .info:hover {
  color: var(--td-primary);
}

.admin-thumbnail {
  max-width: 30px;
}

.user-list-wrapper.logout li a {
  color: #F34141;
}
.user-list-wrapper.logout li a i {
  transform: rotate(90deg);
}
.user-list-wrapper li {
  list-style: none;
  text-align: left;
}
.user-list-wrapper li:not(:last-child) {
  margin-bottom: 4px;
}
.user-list-wrapper li:hover a {
  background-color: var(--td-alice-blue);
}
.user-list-wrapper li a {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 12px;
  align-items: center;
  border-radius: 3px;
}
.user-list-wrapper li a span {
  font-size: 14px;
}

.header-quick-action-item .cart-btn {
  position: relative;
  top: 5px;
  color: var(--td-white);
}
.header-quick-action-item .cart-btn .shopping-count {
  position: absolute;
  width: 16px;
  height: 16px;
  background: #09C711;
  font-size: 8px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--td-white);
  top: -7px;
  inset-inline-start: -7px;
}

.header-quick-action ul li {
  list-style: none;
}
.header-quick-action ul li .chats-dropdown-list {
  height: 370px;
  overflow-y: scroll;
  scrollbar-width: thin;
  padding-inline-end: 5px;
  margin-bottom: 25px;
}

/*----------------------------------------*/
/* Authentication style
/*----------------------------------------*/
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .auth-header-top {
    margin-bottom: 30px;
  }
}
.auth-header-top .header-lang-switcher {
  display: flex;
  align-items: center;
  gap: 14px;
  justify-content: end;
}
.auth-header-top .header-lang-switcher .header-lang-item > span {
  color: var(--td-text-primary);
}
.auth-header-top .header-lang-switcher .header-lang-item svg * {
  stroke: var(--td-text-primary);
}
.auth-header-top .header-lang-switcher .header-lang-item ul {
  border: 1px solid #ddd;
}
.auth-header-top .mode-switcher button svg * {
  stroke: var(--td-text-primary);
}

.auth-main-grid {
  display: grid;
  grid-template-columns: 50% 50%;
  min-height: 100vh;
  background-color: var(--td-white);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .auth-main-grid {
    grid-template-columns: 1fr;
  }
}

.auth-banner-wrapper {
  padding: 50px 50px;
  background-color: var(--td-primary);
  display: grid;
  place-content: center;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .auth-banner-wrapper {
    padding: 50px 30px;
    grid-row: 2;
  }
}
@media (max-width: 480px) {
  .auth-banner-wrapper {
    padding: 40px 20px;
  }
}

.auth-banner-content {
  max-width: 405px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .auth-banner-content {
    max-width: 100%;
  }
}
.auth-banner-content .title {
  font-size: 44px;
  color: var(--td-white);
  margin-bottom: 20px;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .auth-banner-content .title {
    font-size: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .auth-banner-content .title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .auth-banner-content .title {
    font-size: 30px;
  }
}
@media (max-width: 480px) {
  .auth-banner-content .title {
    font-size: 24px;
  }
}
.auth-banner-content .description {
  color: var(--td-white);
  max-width: 375px;
}

.auth-banner-thumb-wrapper {
  margin-top: 40px;
}

.auth-banner-thumb img {
  width: 100%;
}

.auth-from-main {
  padding: 25px 50px;
  display: grid;
  align-content: stretch;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px) {
  .auth-from-main {
    padding: 50px 30px;
  }
}
@media (max-width: 575px) {
  .auth-from-main {
    padding: 30px 20px;
  }
}
.auth-from-main .auth-from-inner {
  min-width: 635px;
  margin: 0 auto;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .auth-from-main .auth-from-inner {
    min-width: 100%;
  }
}
.auth-from-main .auth-from-top-content {
  text-align: center;
}
.auth-from-main .auth-from-top-content .auth-logo {
  margin-bottom: 24px;
}
.auth-from-main .auth-from-top-content .auth-logo a {
  display: block;
}
.auth-from-main .auth-from-top-content .title {
  font-size: 36px;
  margin-bottom: 13px;
  font-weight: 700;
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .auth-from-main .auth-from-top-content .title {
    font-size: 32px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .auth-from-main .auth-from-top-content .title {
    font-size: 28px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .auth-from-main .auth-from-top-content .title {
    font-size: 28px;
  }
}
@media (max-width: 480px) {
  .auth-from-main .auth-from-top-content .title {
    font-size: 22px;
  }
}
.auth-from-main .auth-from-top-content .description {
  max-width: 386px;
  margin: 0 auto;
}
.auth-from-main .auth-from-top-content .email-hidden {
  font-size: 16px;
  font-weight: 600;
  margin-top: 25px;
}
.auth-from-main .auth-login-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 27px;
  margin-top: 6px;
}
.auth-from-main .auth-login-option .auth-remind-checkbox input[type=checkbox] ~ label::before {
  width: 16px;
  height: 16px;
  top: 3px;
}
.auth-from-main .auth-login-option .auth-remind-checkbox input[type=checkbox] ~ label::after {
  width: 16px;
  height: 16px;
}
.auth-from-main .auth-login-option .auth-remind-checkbox .remind-me {
  font-size: 14px;
  padding-inline-start: 24px;
}
.auth-from-main .auth-login-option .forget-content span {
  font-size: 14px;
  font-weight: 500;
}
.auth-from-main .auth-login-option .forget-content span a {
  text-decoration: underline;
  color: var(--td-primary);
}
.auth-from-main .auth-login-option .forget-content span a:hover {
  text-decoration: none;
}

.auth-divide {
  -moz-box-align: center;
  align-items: center;
  display: -moz-box;
  display: flex;
  padding: 25px 0 25px;
}
.auth-divide .divider-line {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  -moz-box-flex: 1;
  flex-grow: 1;
  height: 0;
}
.auth-divide .or {
  cursor: default;
  flex-shrink: 0;
  font-size: 16px;
  margin-inline-start: 10px;
  margin-inline-end: 10px;
  font-weight: 500;
}

.auth-social ul {
  list-style: none;
  display: flex;
  gap: 18px;
  justify-content: center;
}
.auth-social ul li a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  border: 1px solid var(--td-primary);
  border-radius: 50%;
  font-size: 18px;
  color: var(--td-primary);
}
.auth-social ul li a:hover {
  background-color: var(--td-primary);
  color: var(--td-white);
}

.auth-from-bottom-content {
  text-align: center;
}

.auth-account .description {
  font-weight: 500;
}
.auth-account .description .link {
  color: var(--td-primary);
  text-decoration: underline;
}
.auth-account .description .link:hover {
  text-decoration: none;
}

.auth-privacy-policy {
  max-width: 323px;
  margin: auto;
  margin-top: 30px;
}
.auth-privacy-policy .description {
  font-size: 14px;
}
.auth-privacy-policy .description .link {
  font-weight: 600;
  color: var(--td-primary);
  text-decoration: underline;
}
.auth-privacy-policy .description .link:hover {
  text-decoration: none;
}

/*----------------------------------------*/
/* Affiliate program style
/*----------------------------------------*/
.aff-pro-add-content {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 8px;
  padding: 25px 24px 24px;
}
.aff-pro-add-content .title {
  font-size: 16px;
  margin-bottom: 15px;
}
.aff-pro-add-content .item:not(:last-child) {
  margin-bottom: 24px;
}
.aff-pro-add-content .item .list ul li {
  list-style: none;
  font-size: 14px;
  line-height: 1.7;
  list-style: none;
  font-size: 14px;
}
.aff-pro-add-content .item .list ul li:not(:last-child) {
  margin-bottom: 15px;
}
.aff-pro-add-content .item .list ul li b {
  font-weight: 600;
  color: var(--td-heading);
}
.aff-pro-add-content .item .list ul li strong {
  color: var(--td-primary);
}
.aff-pro-add-content .item .list ul li .list-item {
  display: flex;
  gap: 5px;
}
.aff-pro-add-content .item .list ul li .list-item span {
  color: var(--td-heading);
  font-weight: 600;
}

.share-news-thumb img {
  border: 1px solid var(--td-primary);
  border-radius: 15px;
}

.share-news-card {
  background-color: var(--td-primary);
  padding: 50px 50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .share-news-card {
    padding: 30px 30px;
  }
}
@media (max-width: 480px) {
  .share-news-card {
    padding: 20px 20px;
  }
}
.share-news-card .section-subtitle {
  color: var(--td-white);
}
.share-news-card .section-title {
  color: var(--td-white);
  font-size: clamp(1.375rem, 0.875rem + 1.5vw, 2.625rem);
}
.share-news-card .description {
  color: var(--td-white);
  line-height: 1.75;
}

.share-news-bg {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
}

.social-sharing-section {
  position: relative;
  z-index: 5;
}
.social-sharing-section:before {
  position: absolute;
  content: "";
  height: calc(100% - 100px);
  width: 80%;
  background: #B0E1E7;
  inset-inline-start: 0;
  z-index: -1;
  clip-path: polygon(36.282% 0%, 100% 0%, 100% 100%, 0% 100%, 36.282% 0%);
  bottom: 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .social-sharing-section:before {
    clip-path: none;
    width: 100%;
    height: 60%;
  }
}

.social-share-thumb {
  text-align: center;
}

.logo-banner-content {
  padding-inline-end: 25px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .logo-banner-content {
    padding-inline-end: 0;
  }
}

.logo-banner-wrapper {
  display: flex;
  align-items: center;
  gap: 50px;
  position: relative;
  padding: 0 50px 65px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .logo-banner-wrapper {
    padding: 0 30px 45px;
    gap: 30px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .logo-banner-wrapper {
    flex-direction: column;
  }
}
.logo-banner-wrapper::before {
  position: absolute;
  content: "";
  height: 162px;
  width: 100%;
  background-color: var(--td-primary);
  z-index: -1;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.logo-banner-wrapper > .logo {
  width: 285px;
  background-color: var(--td-white);
  height: 190px;
  line-height: 190px;
  text-align: center;
  border-radius: 20px;
  border: 1px solid var(--td-primary);
  flex: 0 0 auto;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .logo-banner-wrapper > .logo {
    width: 250px;
  }
}
.logo-banner-wrapper .card-info {
  background-repeat: no-repeat;
  padding: 20px 30px;
  min-height: 190px;
  background-size: 100% 100.2%;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  border: 1px solid #FFCF3E;
  width: 100%;
}
.logo-banner-wrapper .card-info .logo {
  max-width: 85px;
  margin-bottom: 15px;
}
.logo-banner-wrapper .card-info .contents .title {
  font-size: 16px;
  margin-bottom: 5px;
}
.logo-banner-wrapper .card-info .contents .description {
  font-size: 5px;
}

.logo-banner-mask {
  position: absolute;
  inset-inline-start: 0;
  bottom: 0;
  z-index: -1;
}

/*----------------------------------------*/
/* Tab customize
/*----------------------------------------*/
.added-to-cart {
  position: fixed;
  right: 0;
  display: none;
  background-color: #EAF7E6;
  z-index: 99;
  top: 15%;
  padding-right: 30px;
  box-shadow: 0px 16px 32px 0px rgba(0, 0, 0, 0.04);
}
.added-to-cart .message-text {
  display: flex;
  gap: 3px;
}
.added-to-cart i {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  font-size: 16px;
  color: var(--td-white);
  background-color: #3A8946;
  border-radius: 0;
  margin-right: 15px;
}
.added-to-cart p {
  margin-bottom: 0;
  color: #3A8946;
}
.added-to-cart .cart-count {
  color: #3A8946;
}

/*----------------------------------------*/
/* Checkout cart scss
/*----------------------------------------*/
.payment-methods-box {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  padding: 30px 30px;
  height: 100%;
}
@media (max-width: 575px) {
  .payment-methods-box {
    padding: 20px 20px;
  }
}

.payment-methods-heading {
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.payment-methods-title {
  font-size: 20px;
}

.payment-methods-list ul li {
  list-style: none;
}
.payment-methods-list ul li:not(:last-child) {
  margin-bottom: 30px;
}
.payment-methods-list ul li .form-check-label {
  font-size: 14px;
  font-weight: 600;
}
@media (max-width: 575px) {
  .payment-methods-list ul li .payment-methods-thumb {
    max-width: 65px;
  }
}

.payment-methods-list-item {
  background: #EFF5FF;
  border: 1px solid rgba(0, 101, 255, 0.18);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  height: 46px;
}

.payment-attention {
  margin-top: 40px;
  text-align: center;
  margin-bottom: 20px;
}
.payment-attention .description {
  font-size: 14px;
  font-weight: 600;
}

.payment-form-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px 10px;
  flex-wrap: wrap;
}
.payment-form-bottom .add-button {
  display: flex;
  align-items: center;
  gap: 5px;
}

/*----------------------------------------*/
/* Shopping cart scss
/*----------------------------------------*/
.product-shopping-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px 15px;
}
.product-shopping-top .title-inner .title {
  font-size: 24px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-shopping-top .title-inner .title {
    font-size: 24px;
  }
}

.shopping-card-item {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  padding: 30px 30px;
  position: relative;
}
@media (max-width: 575px) {
  .shopping-card-item {
    padding: 15px 15px;
  }
}
.shopping-card-item:not(:last-child) {
  margin-bottom: 30px;
}

.shopping-card-inner {
  display: flex;
  gap: 30px;
  position: relative;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .shopping-card-inner {
    flex-direction: column;
  }
}

.shopping-card-thumb {
  width: 176px;
  height: 100%;
  border-radius: 10px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .shopping-card-thumb {
    width: 100%;
  }
}
.shopping-card-thumb img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 10px;
}

.shopping-card-thumb-wrap {
  position: relative;
}

.product-extend-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.product-extend-content .description {
  font-size: 14px;
}

.shopping-card-close {
  position: absolute;
  top: 30px;
  inset-inline-end: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .shopping-card-close {
    margin-top: 10px;
    position: inherit;
    top: inherit;
    inset-inline-end: inherit;
  }
}
.shopping-card-close .close-btn {
  display: flex;
  gap: 5px;
  align-items: center;
  color: var(--td-danger);
  font-size: 14px;
}
.shopping-card-close .close-btn span {
  text-decoration: underline;
}

.shopping-card-quantity {
  display: flex;
  gap: 5px;
  align-items: center;
  position: relative;
  top: 40px;
}
.shopping-card-quantity > span {
  font-weight: 600;
}
.shopping-card-quantity .shopping-input {
  width: 30px;
  height: 25px;
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.54);
  border-radius: 6px;
  padding: 0;
  text-align: center;
}
.shopping-card-quantity .shopping-update-btn {
  text-decoration: underline;
  font-size: 14px;
  color: var(--td-primary);
}

.shopping-card-content .shopping-product-title {
  margin-bottom: 15px;
  font-size: 18px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .shopping-card-content .shopping-product-title {
    font-size: 20px;
  }
}
.shopping-card-content .shopping-product-title a:hover {
  color: var(--td-primary);
}
.shopping-card-content .product-purchase-price {
  margin-bottom: 5px;
}
.shopping-card-content .product-purchase-price .current-price {
  font-size: 20px;
}
.shopping-card-content .description p {
  font-size: 14px;
}

.product-shopping-btn {
  flex-basis: 33%;
  display: flex;
  align-items: end;
  justify-content: end;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-shopping-btn {
    justify-content: 40%;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-shopping-btn {
    justify-content: start;
  }
}

.product-summary-box {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  padding: 30px 30px;
}

.product-summary-heading {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.product-summary-title {
  font-size: 20px;
}

.product-summary-list ul li {
  list-style: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.product-summary-list ul li:not(:last-child) {
  margin-bottom: 20px;
}
.product-summary-list .product-purchase-price .current-price {
  font-size: 20px;
}

.product-summary-bottom {
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px 5px;
}
.product-summary-bottom .description {
  font-size: 14px;
  color: var(--td-danger);
  margin-bottom: 5px;
}

/*----------------------------------------*/
/* Breadcrumb style
/*----------------------------------------*/
.breadcrumb-space {
  padding: 80px 0 80px;
}
@media (max-width: 575px) {
  .breadcrumb-space {
    padding: 60px 0 60px;
  }
}

.breadcrumb-overlay {
  background: var(--td-primary);
}

.breadcrumb-content .td-breadcrumb-menu {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  display: inline-block;
  padding: 2px 15px 4px;
  margin-bottom: 20px;
}
.breadcrumb-content .td-breadcrumb-menu nav ul {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  gap: 10px 25px;
  justify-content: center;
  flex-wrap: wrap;
}
.breadcrumb-content .td-breadcrumb-menu nav ul li {
  list-style: none;
  position: relative;
  line-height: 1;
  color: rgba(255, 255, 255, 0.6);
}
.breadcrumb-content .td-breadcrumb-menu nav ul li:not(:nth-child(1))::before {
  display: inline-block;
  content: "\f324";
  position: absolute;
  top: calc(50% + 2px);
  inset-inline-start: -19px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  font-family: var(--td-ff-fontawesome);
}
.breadcrumb-content .td-breadcrumb-menu nav ul li:last-child {
  color: white;
}
.breadcrumb-content .td-breadcrumb-menu nav ul li:last-child::before {
  color: white;
}
.breadcrumb-content .td-breadcrumb-menu nav ul li.active span {
  color: var(--td-primary);
}
.breadcrumb-content .td-breadcrumb-menu nav ul li span {
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}
.breadcrumb-content .td-breadcrumb-menu nav ul li span a {
  font-weight: var(--td-fw-medium);
}
.breadcrumb-content .td-breadcrumb-menu nav ul li span a:hover {
  color: var(--td-primary);
}
.breadcrumb-content .breadcrumb-title {
  font-size: 44px;
  color: var(--td-white);
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .breadcrumb-content .breadcrumb-title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px) {
  .breadcrumb-content .breadcrumb-title {
    font-size: 28px;
  }
}
@media (max-width: 575px) {
  .breadcrumb-content .breadcrumb-title {
    font-size: 26px;
  }
}
@media (max-width: 480px) {
  .breadcrumb-content .breadcrumb-title {
    font-size: 24px;
  }
}
.breadcrumb-content .breadcrumb-filter {
  position: relative;
  max-width: 575px;
}
.breadcrumb-content .breadcrumb-filter .filter-input {
  background-color: var(--td-white);
  border: 0;
  border-radius: 30px;
  height: 58px;
  font-size: 16px;
  font-weight: 500;
  padding-inline-end: 135px;
}
.breadcrumb-content .breadcrumb-filter .filter-input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 16px;
  font-weight: 500;
}
.breadcrumb-content .breadcrumb-filter .filter-input::-moz-placeholder { /* Firefox 19+ */
  font-size: 16px;
  font-weight: 500;
}
.breadcrumb-content .breadcrumb-filter .filter-input:-moz-placeholder { /* Firefox 4-18 */
  font-size: 16px;
  font-weight: 500;
}
.breadcrumb-content .breadcrumb-filter .filter-input:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 16px;
  font-weight: 500;
}
.breadcrumb-content .breadcrumb-filter .filter-input::placeholder { /* MODERN BROWSER */
  font-size: 16px;
  font-weight: 500;
}
.breadcrumb-content .breadcrumb-filter .filter-btn {
  position: absolute;
  top: 50%;
  inset-inline-end: 10px;
  background-color: var(--td-primary);
  color: var(--td-white);
  border-radius: 30px;
  transform: translateY(-50%);
}
.breadcrumb-content > .description {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
  max-width: 624px;
}

.breadcrumb-gallery-wrapper {
  -webkit-transform: rotate(30deg);
  -moz-transform: rotate(30deg);
  -ms-transform: rotate(30deg);
  -o-transform: rotate(30deg);
  transform: rotate(30deg);
  display: flex;
  gap: 10px;
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  z-index: -1;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .breadcrumb-gallery-wrapper {
    top: 50px;
    width: 55%;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .breadcrumb-gallery-wrapper {
    top: 5%;
    width: 55%;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb-gallery-wrapper {
    width: 40%;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .breadcrumb-gallery-wrapper {
    transform: inherit;
    position: inherit;
  }
}
[dir=rtl] .breadcrumb-gallery-wrapper {
  -webkit-transform: rotate(335deg);
  -moz-transform: rotate(335deg);
  -ms-transform: rotate(335deg);
  -o-transform: rotate(335deg);
  transform: rotate(335deg);
}
.breadcrumb-gallery-wrapper .breadcrumb-thumb:nth-child(1) {
  transform: translateY(-42%);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb-gallery-wrapper .breadcrumb-thumb:nth-child(1) {
    transform: translateY(0px);
  }
}
.breadcrumb-gallery-wrapper .breadcrumb-thumb:nth-child(2) {
  transform: translateY(-16%);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb-gallery-wrapper .breadcrumb-thumb:nth-child(2) {
    transform: translateY(0px);
  }
}
.breadcrumb-gallery-wrapper .breadcrumb-thumb:nth-child(3) {
  transform: translateY(-30%);
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .breadcrumb-gallery-wrapper .breadcrumb-thumb:nth-child(3) {
    transform: translateY(0px);
  }
}

.breadcrumb-pattern {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  z-index: -1;
  background-size: cover;
  background-position: center;
}

/*----------------------------------------*/
/*  Modal
/*----------------------------------------*/
#exampleModal,
#withdrawLogModal,
#referModal,
#payout-methodModal,
#itemReviewModal,
#create-topic-methodModal,
#hire-freelancer-modal,
#hire-Freelancer-Modal {
  background: rgba(0, 0, 0, 0.7);
}

.product-modal .modal-dialog,
.create-topic .modal-dialog {
  min-width: 1036px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .product-modal .modal-dialog,
  .create-topic .modal-dialog {
    width: 100%;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-modal .modal-dialog,
  .create-topic .modal-dialog {
    min-width: 80%;
  }
}
.product-modal .modal-content,
.create-topic .modal-content {
  background: var(--td-alice-blue);
  border-radius: 15px;
  border: 0;
  padding: 30px 50px 45px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-modal .modal-content,
  .create-topic .modal-content {
    padding: 25px 30px 30px;
  }
}
@media (max-width: 480px) {
  .product-modal .modal-content,
  .create-topic .modal-content {
    padding: 25px 20px 30px;
  }
}
.product-modal .modal-content .modal-header,
.create-topic .modal-content .modal-header {
  padding: 0;
  padding-bottom: 20px;
}
.product-modal .modal-content .modal-header .modal-title,
.create-topic .modal-content .modal-header .modal-title {
  font-size: 26px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-modal .modal-content .modal-header .modal-title,
  .create-topic .modal-content .modal-header .modal-title {
    font-size: 24px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .product-modal .modal-content .modal-header .modal-title,
  .create-topic .modal-content .modal-header .modal-title {
    font-size: 20px;
  }
}
.product-modal .modal-body,
.create-topic .modal-body {
  padding: 0;
  padding-top: 40px;
  margin-bottom: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-modal .modal-body,
  .create-topic .modal-body {
    padding-top: 30px;
  }
}
.product-modal .product-modal-content,
.create-topic .product-modal-content {
  width: 100%;
}
.product-modal .product-modal-content .product-modal-top,
.create-topic .product-modal-content .product-modal-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 10px;
}
.product-modal .product-modal-content .product-purchase-price,
.create-topic .product-modal-content .product-purchase-price {
  display: flex;
  gap: 5px;
  align-items: center;
  margin-bottom: 5px;
}
.product-modal .product-modal-content .description,
.create-topic .product-modal-content .description {
  margin-bottom: 13px;
}

.product-modal-wrapper {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 10px;
  padding: 25px 20px 20px;
}
.product-modal-wrapper .product-modal-item {
  display: flex;
  gap: 30px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-modal-wrapper .product-modal-item {
    flex-direction: column;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-modal-wrapper .product-modal-item .author-title {
    font-size: 20px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .product-modal-wrapper .product-modal-item .author-title {
    font-size: 18px;
  }
}
.product-modal-wrapper .product-modal-item .product-modal-thumb-wrap {
  position: relative;
}
.product-modal-wrapper .product-modal-item .product-modal-thumb-wrap .product-modal-thumb {
  width: 176px;
  height: 179px;
  border-radius: 10px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .product-modal-wrapper .product-modal-item .product-modal-thumb-wrap .product-modal-thumb {
    width: 100%;
  }
}
.product-modal-wrapper .product-modal-item .product-modal-thumb-wrap .product-modal-thumb::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 10px;
  top: 0;
  inset-inline-start: 0;
}
.product-modal-wrapper .product-modal-item .product-modal-thumb-wrap .product-modal-thumb img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  border-radius: 10px;
}
.product-modal-wrapper .product-modal-item .product-modal-thumb-wrap .product-modal-thumb-content {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  text-align: center;
  padding: 20px;
}
.product-modal-wrapper .product-modal-item .product-modal-thumb-wrap .product-modal-thumb-content .icon span {
  width: 48px;
  height: 48px;
  background: var(--td-white);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-bottom: 10px;
}
.product-modal-wrapper .product-modal-item .product-modal-thumb-wrap .product-modal-thumb-content .icon span i {
  font-size: 24px;
  color: var(--td-primary);
}
.product-modal-wrapper .product-modal-item .product-modal-thumb-wrap .product-modal-thumb-content .content .title {
  font-size: 16px;
  color: var(--td-white);
  font-weight: 700;
}
.product-modal-wrapper .product-extend-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 5px 10px;
  padding-top: 20px;
  margin-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.product-modal-wrapper .product-extend-content .description {
  font-size: 14px;
}

.modal-buttons {
  display: flex;
  align-items: center;
  gap: 15px;
}

.round-btn-close {
  width: 34px;
  height: 34px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  border-radius: 50%;
  position: absolute;
  inset-inline-end: 20px;
  top: 20px;
  color: var(--td-black);
  z-index: 2;
}

.modal-author-info span {
  font-size: 14px;
  font-weight: 600;
}
.modal-author-info span strong {
  color: var(--td-primary);
  font-weight: 600;
}

.product-extend-content-form {
  margin-top: 35px;
}
.product-extend-content-form .input-field input {
  outline: none;
  height: 45px;
  width: 100%;
  font-size: 14px;
  color: var(--td-text-primary);
  padding: 0 12px;
  background: rgba(255, 255, 255, 0.08);
  mix-blend-mode: normal;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  color: var(--td-white);
}
.product-extend-content-form .input-field input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: rgba(255, 255, 255, 0.3);
}
.product-extend-content-form .input-field input::-moz-placeholder { /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.3);
}
.product-extend-content-form .input-field input:-moz-placeholder { /* Firefox 4-18 */
  color: rgba(255, 255, 255, 0.3);
}
.product-extend-content-form .input-field input:-ms-input-placeholder { /* IE 10+  Edge*/
  color: rgba(255, 255, 255, 0.3);
}
.product-extend-content-form .input-field input::placeholder { /* MODERN BROWSER */
  color: rgba(255, 255, 255, 0.3);
}
.product-extend-content-form .input-field input:focus {
  border-color: var(--td-primary);
}
.product-extend-content-form .input-field.input-group {
  flex-wrap: nowrap;
}
.product-extend-content-form .input-field .input-group-text {
  color: var(--td-white);
  background: rgba(255, 255, 255, 0.08);
  mix-blend-mode: normal;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}
.product-extend-content-form .input-field .input-label {
  color: var(--td-black);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
}
.product-extend-content-form .input-select .nice-select {
  height: 44px;
  width: 100%;
  padding: 0 15px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  float: none;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
}
.product-extend-content-form .input-select .nice-select .current {
  text-align: left;
  font-size: 14px;
  position: relative;
}
.product-extend-content-form .input-select .nice-select .list {
  -webkit-transform: scale(1) translateY(0);
  -moz-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  padding: 10px 0;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background: var(--td-white);
  border-radius: 12px;
  padding: 12px 12px 12px 12px;
  max-height: 300px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
.product-extend-content-form .input-select .nice-select::after {
  font-size: 16px;
  inset-inline-end: 16px;
  width: 8px;
  height: 8px;
}
.product-extend-content-form .input-select .nice-select .option {
  font-size: 14px;
  line-height: 38px;
  min-height: 38px;
  border-radius: 10px;
  padding: 0 10px;
}
.product-extend-content-form .input-select .nice-select .option.selected {
  font-weight: 500;
}
.product-extend-content-form .input-select .nice-select .option:hover {
  background-color: var(--td-alice-blue);
}
.product-extend-content-form .input-select .nice-select .option.selected.focus {
  background-color: var(--td-alice-blue);
}

.review-modal .modal-dialog,
.payout-method .modal-dialog,
.hire-freelancer-modal .modal-dialog {
  min-width: 769px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .review-modal .modal-dialog,
  .payout-method .modal-dialog,
  .hire-freelancer-modal .modal-dialog {
    width: 100%;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .review-modal .modal-dialog,
  .payout-method .modal-dialog,
  .hire-freelancer-modal .modal-dialog {
    min-width: 80%;
  }
}
.review-modal .modal-card-heading .description,
.payout-method .modal-card-heading .description,
.hire-freelancer-modal .modal-card-heading .description {
  font-size: 14px;
  max-width: 598px;
}
.review-modal .modal-content,
.payout-method .modal-content,
.hire-freelancer-modal .modal-content {
  background: var(--td-alice-blue);
  border-radius: 15px;
  border: 0;
  padding: 40px 50px 45px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .review-modal .modal-content,
  .payout-method .modal-content,
  .hire-freelancer-modal .modal-content {
    padding: 30px 30px 30px;
  }
}
@media (max-width: 575px) {
  .review-modal .modal-content,
  .payout-method .modal-content,
  .hire-freelancer-modal .modal-content {
    padding: 25px 20px 25px;
  }
}
.review-modal .modal-content .modal-header,
.review-modal .modal-content .modal-body,
.payout-method .modal-content .modal-header,
.payout-method .modal-content .modal-body,
.hire-freelancer-modal .modal-content .modal-header,
.hire-freelancer-modal .modal-content .modal-body {
  padding: 0;
}
.review-modal .modal-content .modal-header,
.payout-method .modal-content .modal-header,
.hire-freelancer-modal .modal-content .modal-header {
  padding: 0;
  margin-bottom: 25px;
  padding-bottom: 20px;
}

.alert-modal .alert-icon,
.form-modal .alert-icon {
  width: 60px;
  height: 60px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -o-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  margin-bottom: 15px;
  font-size: 26px;
}
.alert-modal .alert-icon.danger,
.form-modal .alert-icon.danger {
  background-color: #FFE4E6;
  color: var(--td-danger);
}
.alert-modal .modal-body,
.form-modal .modal-body {
  padding: 30px 30px;
}
@media (max-width: 480px) {
  .alert-modal .modal-body,
  .form-modal .modal-body {
    padding: 20px 20px;
  }
}

/*----------------------------------------*/
/* Badge styles
/*----------------------------------------*/
.td-badge {
  font-size: 16px;
  text-transform: capitalize;
  display: inline-block;
  line-height: 1;
  text-align: center;
  position: relative;
  background-color: var(--td-primary);
  padding: 15px 20px;
  font-size: 15px;
  font-weight: 600;
  color: var(--td-white);
  border-radius: 10px 10px 0px;
  box-shadow: 0px 3.95168px 7.90336px rgba(0, 0, 0, 0.15);
}
[dir=rtl] .td-badge {
  border-radius: 6px 6px 0px 0px;
}
.td-badge::before {
  position: absolute;
  content: "";
  top: 100%;
  inset-inline-end: 0px;
  border-top: 10px solid #0549B1;
  border-inline-end: 10px solid transparent;
}
[dir=rtl] .td-badge::before {
  inset-inline-end: auto;
  inset-inline-start: 0px;
  border-inline-end: 0;
  border-inset-inline-start: 10px solid transparent;
}
.td-badge.primary {
  background: var(--td-primary);
  color: var(--td-white);
}
.td-badge.warning {
  background: var(--td-yellow);
}
.td-badge.warning::before {
  border-top: 10px solid #C67C10;
}
.td-badge.danger {
  color: var(--td-white);
  background: var(--td-bg-danger);
}
.td-badge.success {
  color: var(--td-white);
  background: var(--td-bg-success);
}
.td-badge.black {
  color: var(--td-white);
  background: var(--td-black);
}

.purchased-badge {
  font-size: 12px;
  font-style: italic;
  font-weight: 500;
  color: var(--td-primary);
}

.td-text-badge {
  font-size: 14px;
  display: inline-block;
  margin-inline-start: 5px;
}

.td-short-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0px 14px;
  min-width: 87px;
  height: 28px;
  border-radius: 6px;
  line-height: 1;
}
.td-short-badge.warning {
  background: rgba(247, 158, 28, 0.1);
  color: var(--td-yellow);
}
.td-short-badge.success {
  background: rgba(13, 189, 63, 0.1);
  color: #0DBD3F;
}
.td-short-badge.danger {
  background: rgba(237, 64, 48, 0.1);
  color: var(--td-danger);
}
.td-short-badge.green {
  background: rgba(13, 189, 63, 0.1);
  color: #0DBD3F;
}

/*---------------------------------
/*  Button styles
---------------------------------*/
.btn-wrap {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 15px 15px;
  flex-wrap: wrap;
}

.td-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 46px;
  color: var(--td-white);
  font-size: 16px;
  font-weight: 600;
}
.td-btn.primary-btn {
  background-color: var(--td-primary);
  border-radius: 12px;
  padding: 0 30px;
  gap: 5px;
}
.td-btn.primary-btn:hover {
  background-color: #0954C8;
  color: var(--td-white);
}
.td-btn.primary-opacity-btn {
  background: rgba(0, 101, 255, 0.1);
  color: var(--td-primary);
  padding: 0 30px;
  border-radius: 12px;
}
.td-btn.btn-gradient {
  background-image: url(../images/bg/button-bg.svg);
  background-repeat: no-repeat, repeat;
  padding: 0px 30px;
  height: 46px;
  border-radius: 30px;
  gap: 5px;
  background-size: cover;
  background-position: center;
  font-size: 14px;
}
.td-btn.btn-gradient i {
  font-size: 16px;
  position: relative;
}
.td-btn.btn-gradient:hover, .td-btn.btn-gradient:focus {
  color: var(--td-white);
}
.td-btn.btn-gradient.opacity {
  background-image: none;
  position: relative;
  z-index: 5;
  color: var(--td-primary);
}
.td-btn.btn-gradient.opacity::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  background-image: url(../images/bg/button-bg.svg);
  top: 0;
  inset-inline-start: 0;
  opacity: 20%;
  z-index: -1;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  background-repeat: no-repeat;
  background-size: cover;
}
.td-btn.gradient-outline {
  position: relative;
  padding: 0 30px;
  gap: 5px;
  font-size: 14px;
  color: var(--td-primary);
  box-shadow: 2px 1000px 1px var(--td-white) inset;
  font-weight: 500;
  border: 1px solid transparent;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  z-index: 2;
  transition: 0.3s;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px) {
  .td-btn.gradient-outline {
    padding: 0 15px;
  }
}
.td-btn.gradient-outline i {
  font-size: 18px;
}
.td-btn.gradient-outline:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0)), linear-gradient(101deg, #2392FE, #4A19FE);
  opacity: 0;
  transition: 0.3s ease;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  z-index: -1;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}
.td-btn.gradient-outline::after {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
  padding: 1px;
  background: linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0)), linear-gradient(101deg, #2392FE, #4A19FE);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}
.td-btn.gradient-outline:hover {
  color: var(--td-white);
}
.td-btn.gradient-outline:hover:before {
  opacity: 1;
}
.td-btn.success-btn {
  background: rgba(237, 64, 48, 0.1);
  border-radius: 28px;
  color: var(--td-success);
  padding: 0 30px;
  gap: 5px;
}
.td-btn.danger-btn {
  background: rgba(237, 64, 48, 0.1);
  border-radius: 28px;
  color: var(--td-danger);
  padding: 0 30px;
  gap: 5px;
}
.td-btn.danger-btn:hover {
  background-color: #E75446;
  color: var(--td-white);
}
.td-btn.action-wishlist {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 30px;
  width: 60px;
}
.td-btn.action-wishlist i {
  font-size: 20px;
  color: var(--td-text-primary);
}
.td-btn.action-wishlist:hover {
  background-color: var(--td-primary);
}
.td-btn.action-wishlist:hover i {
  color: var(--td-white);
}
.td-btn.btn-fill {
  background-color: var(--td-primary);
  padding: 0 15px;
  border-radius: 10px;
}
.td-btn.btn-fill:hover {
  color: var(--td-white);
}
.td-btn.fill-warning {
  padding: 0 24px;
  background-color: var(--td-warning);
  border-radius: 30px;
  gap: 5px;
  color: var(--td-heading);
}
.td-btn.btn-xxxs {
  height: 32px;
  padding: 0 14px;
  gap: 5px;
  font-size: 12px;
  font-weight: 500;
}
.td-btn.btn-xxs {
  height: 38px;
  padding: 0 20px;
  gap: 7px;
  font-size: 14px;
  font-weight: 600;
}
.td-btn.btn-xs {
  height: 58px;
}

.text-btn {
  font-weight: 800;
  color: #576363;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  position: relative;
  font-size: 14px;
  color: var(--td-white);
  font-weight: 600;
}
.text-btn i {
  font-size: 18px;
}
.text-btn::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  bottom: -2px;
  inset-inline-start: -3px;
  background-color: var(--td-white);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s;
}
.text-btn:hover {
  color: var(--td-white);
}
.text-btn:hover::after {
  transform-origin: bottom left;
  transform: scaleX(1);
}
.text-btn:focus {
  color: var(--td-white);
}

.text-btn-underline {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-primary);
  text-decoration: underline;
}
.text-btn-underline:hover {
  color: var(--td-primary);
}

.td-action-btn {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}
.td-action-btn .action-btn {
  font-size: 18px;
}
.td-action-btn .action-btn.primary-btn {
  color: var(--td-primary);
}
.td-action-btn .action-btn.warning-btn {
  color: var(--td-warning);
}
.td-action-btn .action-btn.danger-btn {
  color: var(--td-danger);
}

.td-dropdown .dropdown-toggle::after {
  content: "\e92b";
  font-family: "icomoon" !important;
  border: 0;
  font-size: 14px;
  transform: translateY(2px);
  margin-inset-inline-start: 0;
}
.td-dropdown .dropdown-menu {
  background: var(--td-white);
  box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.1);
  border: 0;
  padding: 12px 0px;
}
.td-dropdown .dropdown-item {
  font-size: 12px;
}
.td-dropdown .dropdown-item .dropdown-item:focus, .td-dropdown .dropdown-item .dropdown-item:hover {
  color: var(--td-primary);
  background-color: var(--td-primary);
}
.td-dropdown li:not(:last-child) {
  margin-bottom: 5px;
}

.td-action-fill-btn {
  border-radius: 9px !important;
  padding: 0 15px;
  color: var(--td-white);
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.td-action-fill-btn.primary-btn {
  background: var(--td-primary);
}
.td-action-fill-btn.white-btn {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  color: var(--td-primary);
}
.td-action-fill-btn.gray-btn {
  background: rgba(0, 101, 255, 0.1);
  border: 1px solid var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  color: rgba(0, 0, 0, 0.6);
}

.td-outline-btn {
  padding: 0px 20px;
  font-size: 16px;
  height: 38px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
}
.td-outline-btn.primary-btn {
  font-size: 14px;
  border: 1px solid var(--td-primary);
  color: var(--td-primary);
  font-weight: 600;
}

/*----------------------------------------*/
/*  Offcanvas
/*----------------------------------------*/
.offcanvas-area {
  background: var(--td-white) none repeat scroll 0 0;
  position: fixed;
  right: 0;
  top: 0;
  width: 340px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
.offcanvas-area ::-webkit-scrollbar {
  display: none;
}
@media (max-width: 480px) {
  .offcanvas-area {
    width: 100%;
  }
}
.offcanvas-area.info-open {
  opacity: 1;
  transform: translateX(0);
}

.offcanvas-logo a img {
  max-width: 160px;
}
@media (max-width: 575px) {
  .offcanvas-logo a img {
    height: 30px;
  }
}

.offcanvas-content {
  padding-bottom: 45px;
}

.offcanva-wrapper {
  position: relative;
  height: 100%;
  padding: 30px 30px;
}

.offcanvas-top {
  margin-bottom: 25px;
}

.offcanvas-title {
  color: var(--td-white);
  font-size: 20px;
  margin-bottom: 20px;
}
@media (max-width: 480px) {
  .offcanvas-title {
    font-size: 20px;
  }
}

.offcanva-wrapper p {
  margin-bottom: 25px;
}

.offcanvas-overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: #000000;
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  inset-inline-end: 0;
  transition: 0.3s;
}
.offcanvas-overlay.overlay-open {
  opacity: 0.6;
  visibility: visible;
}

.sidebar-toggle {
  cursor: pointer;
}

.offcanvas-contact-icon {
  margin-inline-end: 15px;
}

.offcanvas-btn {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 15px;
}

.offcanvas-close-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  height: 44px;
  width: 44px;
  line-height: 40px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  background-color: transparent;
  border-radius: 50%;
}
.offcanvas-close-icon:hover {
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: transparent;
}

.mobile-top-ber-menu .submenu {
  display: none;
  background: transparent;
  font-size: 14px;
}
.mobile-top-ber-menu .submenu li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-left: 7%;
}
.mobile-top-ber-menu .submenu a {
  display: block;
  padding: 10px 0px;
  -webkit-transition: all 0.25s ease;
  -o-transition: all 0.25s ease;
  transition: all 0.25s ease;
  font-size: 15px;
  line-height: 1.5;
  font-weight: 500;
  color: var(--td-heading);
  text-transform: capitalize;
}
.mobile-top-ber-menu .submenu a:hover {
  background: transparent;
  color: var(--td-sunsetStrip);
}
.mobile-top-ber-menu .accordion .link {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.4s ease;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
  color: var(--td-heading);
}
.mobile-top-ber-menu .accordion li {
  list-style: none;
}
.mobile-top-ber-menu .accordion li.open .link {
  color: var(--td-sunsetStrip);
}
.mobile-top-ber-menu .accordion > li > a {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.4s ease;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
  color: var(--td-heading);
}
.mobile-top-ber-menu .accordion > li > a:hover {
  color: var(--td-sunsetStrip);
}

/*----------------------------------------*/
/* Section Title
/*----------------------------------------*/
.section-subtitle {
  font-size: 20px;
  font-weight: 500;
  display: block;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .section-subtitle {
    font-size: 18px;
    margin-bottom: 15;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .section-subtitle {
    font-size: 16px;
    margin-bottom: 15;
  }
}

/*----------------------------------------*/
/* Tab customize
/*----------------------------------------*/
.td-tab .nav-tabs {
  padding: 0;
  margin: 0;
  border: 0;
}
.td-tab .nav-tabs .nav-link {
  padding: 0;
  margin: 0;
  border: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

/*----------------------------------------
   Basic pagination
-----------------------------------------*/
.pagination-wrapper {
  margin-top: 50px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .pagination-wrapper {
    margin-top: 30px;
  }
}

.td-pagination ul {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 5px;
  flex-wrap: wrap;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-pagination ul {
    justify-content: start;
  }
}
.td-pagination ul li {
  list-style: none;
}
.td-pagination ul li a {
  width: 45px;
  height: 45px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  inset-inline-end: 0;
  top: 50%;
  font-weight: 500;
  font-size: 18px;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  background: var(--td-white);
  border: 1px solid #F1F1F1;
}
.td-pagination ul li a:hover {
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: var(--td-primary);
}
.td-pagination ul li a i {
  font-size: 14px;
}
.td-pagination ul li .current {
  width: 45px;
  height: 45px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  inset-inline-end: 0;
  top: 50%;
  font-weight: 500;
  font-size: 16px;
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: var(--td-primary);
}

/*----------------------------------------*/
/* Back to top
/*----------------------------------------*/
.back-to-top-wrap {
  position: fixed;
  bottom: 30px;
  inset-inline-end: 30px;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
  background: var(--td-primary);
}
@media (max-width: 480px) {
  .back-to-top-wrap {
    bottom: 20px;
    inset-inline-end: 20px;
  }
}
.back-to-top-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0px);
}
.back-to-top-wrap::after {
  position: absolute;
  font-family: var(--td-ff-fontawesome);
  content: "\f062";
  text-align: center;
  line-height: 46px;
  font-size: 16px;
  font-weight: 400;
  color: var(--td-white);
  inset-inline-start: 0;
  top: 0;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  z-index: 1;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}
.back-to-top-wrap svg path {
  fill: none;
}
.back-to-top-wrap svg.backtotop-circle path {
  stroke: #ccc;
  stroke-width: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}

/*----------------------------------------*/
/*  forms scss
/*----------------------------------------*/
.was-validated .td-single-input .input-field {
  position: relative;
}
.was-validated .td-single-input .input-field input {
  border-color: var(--tdvar(--td-danger));
  background: rgba(220, 29, 75, 0.1);
}
.was-validated .td-single-input .input-field input:focus {
  background: rgba(220, 29, 75, 0.1);
  border-color: #DC1D4B;
}

.td-single-input.has-right-icon .box-input {
  padding-inline-end: 45px;
}
.td-single-input.has-right-icon .input-icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
}
.td-single-input.has-right-icon .input-icon.eyeicon {
  cursor: pointer;
}
.td-single-input .input-field {
  position: relative;
}
.td-single-input .input-field.disabled input,
.td-single-input .input-field.disabled textarea {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}
.td-single-input .input-field.disabled input:focus,
.td-single-input .input-field.disabled textarea:focus {
  border-color: rgba(255, 255, 255, 0.08);
}
.td-single-input .input-field .text-content {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  position: absolute;
  top: 50%;
  inset-inline-end: 5px;
  transform: translateY(-50%);
  padding: 5px 8px 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--td-primary);
}
.td-single-input .input-field input,
.td-single-input .input-field textarea {
  font-size: 16px;
  font-weight: 500;
}
.td-single-input .input-field input:focus,
.td-single-input .input-field textarea:focus {
  background: rgba(0, 101, 255, 0.1);
}
.td-single-input .input-field input::-webkit-input-placeholder,
.td-single-input .input-field textarea::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 16px;
  font-weight: 500;
}
.td-single-input .input-field input::-moz-placeholder,
.td-single-input .input-field textarea::-moz-placeholder { /* Firefox 19+ */
  font-size: 16px;
  font-weight: 500;
}
.td-single-input .input-field input:-moz-placeholder,
.td-single-input .input-field textarea:-moz-placeholder { /* Firefox 4-18 */
  font-size: 16px;
  font-weight: 500;
}
.td-single-input .input-field input:-ms-input-placeholder,
.td-single-input .input-field textarea:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 16px;
  font-weight: 500;
}
.td-single-input .input-field input::placeholder,
.td-single-input .input-field textarea::placeholder { /* MODERN BROWSER */
  font-size: 16px;
  font-weight: 500;
}
.td-single-input .input-field textarea {
  padding: 12px 15px;
  height: 150px;
  resize: none;
}
.td-single-input .input-field textarea:focus {
  border-color: var(--td-primary);
}
.td-single-input .input-description {
  font-size: 12px;
  margin-top: 7px;
}
.td-single-input .input-label {
  font-size: 14px;
  margin-bottom: 7px;
  font-weight: 600;
}
.td-single-input .input-label span {
  padding-inline-start: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.td-single-input .input-label-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.td-single-input .input-label-inner > p {
  font-size: 12px;
}
.td-single-input .input-field.input-group {
  flex-wrap: nowrap;
}
.td-single-input .input-field .input-group-text {
  color: var(--td-white);
  background: rgba(255, 255, 255, 0.08);
  mix-blend-mode: normal;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}
.td-single-input .input-select .nice-select {
  height: 44px;
  width: 100%;
  padding: 0 15px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  float: none;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.08);
}
.td-single-input .input-select .nice-select .current {
  text-align: left;
  font-size: 14px;
  position: relative;
  color: var(--td-white);
}
.td-single-input .input-select .nice-select .list {
  -webkit-transform: scale(1) translateY(0);
  -moz-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  padding: 10px 0;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background: #242424;
  border-radius: 12px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.08);
  border-width: 1px;
  padding: 12px 12px 12px 12px;
  max-height: 300px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
.td-single-input .input-select .nice-select::after {
  font-size: 16px;
  inset-inline-end: 16px;
  width: 8px;
  height: 8px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-inline-end: 1.5px solid var(--td-text-primary);
  font-size: 16px;
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border: 5px solid;
  border-top-color: rgba(0, 0, 0, 0);
  border-left-color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
  transition: all ease-in-out 0.2s;
  margin-top: -2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
}
.td-single-input .input-select .nice-select .option {
  font-size: 14px;
  line-height: 38px;
  min-height: 38px;
  color: var(--td-white);
  border-radius: 10px;
  padding: 0 10px;
}
.td-single-input .input-select .nice-select .option.selected {
  font-weight: 500;
}
.td-single-input .input-select .nice-select .option:hover {
  background-color: #353535;
}
.td-single-input .input-select .nice-select .option.selected.focus {
  background-color: #353535;
}
.td-single-input .input-select .nice-select.open, .td-single-input .input-select .nice-select:focus {
  background-color: #353535;
}
.td-single-input.input-fill input,
.td-single-input.input-fill textarea {
  background: #EFF5FF;
  border: 1px solid rgba(0, 101, 255, 0.18);
  border-radius: 8px;
  height: 46px;
  font-size: 14px;
}
.td-single-input.input-fill input::-webkit-input-placeholder,
.td-single-input.input-fill textarea::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
}
.td-single-input.input-fill input::-moz-placeholder,
.td-single-input.input-fill textarea::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
}
.td-single-input.input-fill input:-moz-placeholder,
.td-single-input.input-fill textarea:-moz-placeholder { /* Firefox 4-18 */
  font-size: 14px;
}
.td-single-input.input-fill input:-ms-input-placeholder,
.td-single-input.input-fill textarea:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 14px;
}
.td-single-input.input-fill input::placeholder,
.td-single-input.input-fill textarea::placeholder { /* MODERN BROWSER */
  font-size: 14px;
}
.td-single-input.input-fill textarea {
  height: 214px;
  background: #EFF5FF;
}
.td-single-input.input-fill textarea:focus {
  background: #EFF5FF;
}
.td-single-input .otp-verification {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 20px 20px;
  flex-wrap: wrap;
  max-width: max-content;
  justify-content: center;
  margin: 0 auto;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-single-input .otp-verification {
    gap: 10px 10px;
  }
}
.td-single-input .otp-verification input {
  background: rgba(103, 107, 113, 0.1);
  height: 67px;
  width: 62px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-single-input .otp-verification input {
    height: 55px;
    width: 50px;
  }
}
.td-single-input .otp-verification input:focus {
  background: rgba(0, 101, 255, 0.1);
}
.td-single-input.fill-white .input-field input {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border: 0;
  font-size: 14px;
  height: 46px;
  border-radius: 5px;
}
.td-single-input.fill-white .input-field input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
}
.td-single-input.fill-white .input-field input::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
}
.td-single-input.fill-white .input-field input:-moz-placeholder { /* Firefox 4-18 */
  font-size: 14px;
}
.td-single-input.fill-white .input-field input:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 14px;
}
.td-single-input.fill-white .input-field input::placeholder { /* MODERN BROWSER */
  font-size: 14px;
}
.td-single-input.fill-white .upload-custom-file label {
  background-color: var(--td-white);
}

.rock-single-input.has-invalid .input-field input:focus,
.rock-single-input.has-invalid .input-field input[type=email]:focus,
.rock-single-input.has-invalid .input-field input[type=tel]:focus,
.rock-single-input.has-invalid .input-field input[type=number]:focus,
.rock-single-input.has-invalid .input-field input[type=password]:focus,
.rock-single-input.has-invalid .input-field input[type=date]:focus,
.rock-single-input.has-invalid .input-field textarea:focus {
  border-color: var(--tdvar(--td-danger));
}
.rock-single-input.has-invalid .input-field textarea:focus {
  border-color: var(--tdvar(--td-danger));
}

.feedback-invalid {
  font-size: 12px;
  margin-top: 3px;
  color: #DC1D4B;
  display: none;
}

.input-attention {
  font-size: 12px;
  color: var(--tdvar(--td-danger));
}
.input-attention.xs {
  font-size: 10px;
}

.td-single-input.payment-input-group .input-group-inner {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 101, 255, 0.18);
}
.td-single-input.payment-input-group .input-group-inner > .input-field > .box-input {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.td-single-input.payment-input-group .input-group-inner > .input-field input {
  border: 0;
  border-radius: 0;
  font-size: 14px;
}
.td-single-input.payment-input-group .input-group-inner > .input-field input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
}
.td-single-input.payment-input-group .input-group-inner > .input-field input::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
}
.td-single-input.payment-input-group .input-group-inner > .input-field input:-moz-placeholder { /* Firefox 4-18 */
  font-size: 14px;
}
.td-single-input.payment-input-group .input-group-inner > .input-field input:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 14px;
}
.td-single-input.payment-input-group .input-group-inner > .input-field input::placeholder { /* MODERN BROWSER */
  font-size: 14px;
}
.td-single-input.payment-input-group .input-group-inner .input-group .input-field {
  width: 50%;
}
.td-single-input.payment-input-group .input-group-inner .input-group .input-field:last-child {
  border-inline-start: 1px solid rgba(0, 0, 0, 0.1);
}
.td-single-input.payment-input-group .input-group-inner .input-group .input-field input {
  border: 0;
  border-radius: 0;
}

/*----------------------------------------
   Image Preview
-----------------------------------------*/
.file-upload-wrap .top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.file-upload-wrap .input-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #000000;
}

.upload-custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 155px;
  text-align: center;
  border: 2px dashed #0065ff;
  border-radius: 8px;
}
.upload-custom-file input[type=file] {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 2px;
  height: 2px;
  overflow: hidden;
  opacity: 0;
}
.upload-custom-file label {
  z-index: 1;
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  bottom: 0;
  inset-inline-end: 0;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  border-radius: 8px;
  transition: transform 0.4s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  -webkit-transition: -webkit-transform 0.4s;
  -moz-transition: -moz-transform 0.4s;
  -ms-transition: -ms-transform 0.4s;
  -o-transition: -o-transform 0.4s;
  transition: transform 0.4s;
  background-color: rgba(0, 101, 255, 0.1);
}
.upload-custom-file label span {
  display: block;
  color: var(--td-text-primary);
  font-size: 14px;
  -webkit-transition: color 0.4s;
  -moz-transition: color 0.4s;
  -ms-transition: color 0.4s;
  -o-transition: color 0.4s;
  transition: color 0.4s;
}
.upload-custom-file label span b {
  color: #0065ff;
  font-weight: 500;
  text-decoration: underline;
}
.upload-custom-file label .type-file-text {
  margin-top: 5px;
  color: var(--td-danger);
}
.upload-custom-file label .upload-icon {
  width: 40px;
  margin: 0 auto;
  margin-bottom: 15px;
}
.upload-custom-file label.file-ok {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
}
.upload-custom-file label.file-ok span {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  padding: 0.3rem;
  color: #ffffff;
  background-color: rgba(0, 101, 255, 0.5);
  font-weight: 500;
  font-size: 16px;
  margin: auto;
  text-decoration: none;
}
.upload-custom-file label.file-ok .upload-icon {
  display: none;
}
.upload-custom-file.without-image {
  height: 176px;
}

.upload-thumb-close {
  position: absolute;
  inset-inline-end: 10px;
  top: 35px;
  z-index: 5;
  color: var(--td-danger);
  display: none;
}

/*----------------------------------------*/
/*  Animations
/*----------------------------------------*/
@keyframes popupBtn {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.4);
    opacity: 0.3;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes sticky {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0%);
  }
}
@keyframes tdSpinner {
  from {
    -webkit-transform: rotate(0turn);
    transform: rotate(0turn);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
/*----------------------------------------*/
/*  Shortcodes
/*----------------------------------------*/
.text-body {
  color: var(--td-heading);
}

.text-white {
  color: var(--td-white);
}

.text-black {
  color: var(--td-black);
}

.text-primary {
  color: var(--td-primary);
}

.text-secondary {
  color: var(--td-secondary);
}

.text-quaternary {
  color: var(--td-tertiary);
}

.text-gradient-1 {
  background: linear-gradient(135deg, rgba(253, 216, 25, 0.5) 0%, rgba(248, 23, 23, 0.5) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.radius-4 {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}

.radius-5 {
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
}

.radius-8 {
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
}

.radius-10 {
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
}

.radius-20 {
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
}

.radius-30 {
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
}

.radius-40 {
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -o-border-radius: 40px;
  -ms-border-radius: 40px;
  border-radius: 40px;
}

.radius-45 {
  -webkit-border-radius: 45px;
  -moz-border-radius: 45px;
  -o-border-radius: 45px;
  -ms-border-radius: 45px;
  border-radius: 45px;
}

.radius-50 {
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -o-border-radius: 50px;
  -ms-border-radius: 50px;
  border-radius: 50px;
}

.radius-60 {
  -webkit-border-radius: 60px;
  -moz-border-radius: 60px;
  -o-border-radius: 60px;
  -ms-border-radius: 60px;
  border-radius: 60px;
}

.title-font {
  font-family: var(--td-ff-title);
}

.fs-12 {
  font-size: 12px;
}

.fs-14 {
  font-size: 12px;
}

.fs-16 {
  font-size: 16px;
}

.fw-3 {
  font-weight: var(--td-fw-light);
}

.fw-4 {
  font-weight: var(--td-fw-regular);
}

.fw-5 {
  font-weight: var(--td-fw-medium);
}

.fw-6 {
  font-weight: var(--td-fw-sbold);
}

.fw-7 {
  font-weight: var(--td-fw-bold);
}

.fw-8 {
  font-weight: var(--td-fw-ebold);
}

.fw-9 {
  font-weight: var(--td-fw-black);
}

.gap--5 {
  gap: 5px;
}

.gap-10 {
  gap: 10px;
}

.gap-15 {
  gap: 15px;
}

.gap-20 {
  gap: 20px;
}

.gap-24 {
  gap: 24px;
}

.hide {
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

.font-xxs {
  font-size: 14px;
}

/*----------------------------------------*/
/*  Background Css
/*----------------------------------------*/
.white-bg {
  background-color: var(--td-white);
}

.black-bg {
  background-color: var(--td-black);
}

.theme-bg {
  background-color: var(--td-bg-primary);
}

.alice-blue-bg {
  background-color: var(--td-alice-blue);
}

/*----------------------------------------*/
/*  Preloader scss
/*----------------------------------------*/
#preloader {
  position: fixed;
  width: 100%;
  height: 100%;
  inset-inline-start: 0;
  top: 0;
  background-color: var(--td-white);
  z-index: 999;
}

.sk-three-bounce {
  margin: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  background-color: var(--td-white);
}
.sk-three-bounce .sk-child {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background-color: var(--td-primary);
  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
  animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
}
.sk-three-bounce .sk-bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.sk-three-bounce .sk-bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

@-webkit-keyframes sk-three-bounce {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes sk-three-bounce {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
/*----------------------------------------*/
/*  Colors
/*----------------------------------------*/
.white-text {
  color: var(--td-white) !important;
}

.black-text {
  color: var(--td-black);
}

.text-heading {
  color: var(--td-heading);
}

.theme-primary {
  color: var(--td-bg-primary);
}

.theme-secondary {
  color: var(--td-secondary);
}

.theme-text-primary {
  color: var(--td-text-secondary);
}

.theme-text-secondary {
  color: var(--td-text-secondary);
}

.theme-text-tertiary {
  color: var(--td-text-tertiary);
}

.warning-text {
  color: var(--td-warning);
}

.success-text {
  color: var(--td-success);
}

.danger-text {
  color: var(--td-danger);
}

.green-text {
  color: var(--td-green);
}

.magenta-text {
  color: var(--td-magenta);
}

.sidecar-text {
  color: var(--td-sidecar);
}

.pinkDiamond-text {
  color: var(--td-pinkDiamond);
}

.kissable-text {
  color: var(--td-kissable);
}

.kittensEye-text {
  color: var(--td-kittensEye);
}

.pureLaughter-text {
  color: var(--td-pureLaughter);
}

.gradient-text-1 {
  background: linear-gradient(135deg, rgb(253, 217, 136) 0%, rgb(250, 130, 111) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/*----------------------------------------*/
/* Upload css
/*----------------------------------------*/
.checkbox-wrapper {
  display: grid;
  grid-template-columns: auto auto auto auto auto;
  justify-content: space-between;
  gap: 10px;
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.18);
  border-radius: 10px;
  padding: 20px 24px 20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .checkbox-wrapper {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 480px) {
  .checkbox-wrapper {
    grid-template-columns: repeat(2, 1fr);
  }
}

.license-input-item {
  display: flex;
  align-items: center;
  gap: 20px;
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.18);
  border-radius: 10px;
  padding: 24px 24px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .license-input-item {
    flex-direction: column;
    align-items: start;
  }
}
.license-input-item .license-input-field .inner {
  display: grid;
  grid-template-columns: auto 60px auto 60px max-content;
  gap: 20px;
  margin-bottom: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 768px) and (max-width: 991px) {
  .license-input-item .license-input-field .inner {
    grid-template-columns: auto 60px auto 60px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .license-input-item .license-input-field .inner {
    grid-template-columns: repeat(2, auto);
  }
}
.license-input-item .license-input-field input {
  background: var(--td-white);
  background-color: rgb(255, 255, 255);
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  height: 60px;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
}
.license-input-item .license-input-field input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 16px;
}
.license-input-item .license-input-field input::-moz-placeholder { /* Firefox 19+ */
  font-size: 16px;
}
.license-input-item .license-input-field input:-moz-placeholder { /* Firefox 4-18 */
  font-size: 16px;
}
.license-input-item .license-input-field input:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 16px;
}
.license-input-item .license-input-field input::placeholder { /* MODERN BROWSER */
  font-size: 16px;
}
.license-input-item .license-input-field .td-btn.primary-btn {
  height: 60px;
  padding: 0 20px;
  font-size: 14px;
}
.license-input-item .license-input-field .link a {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-primary);
  text-decoration: underline;
}
.license-input-item .price-suggestion {
  width: 228px;
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 8px;
  padding: 15px 15px;
}
.license-input-item .price-suggestion .description {
  font-size: 13px;
}
.license-input-item .price-suggestion span {
  text-align: center;
  color: var(--td-primary);
  display: block;
}

.earn-details-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.earn-details-card .card-header .close-btn {
  border: none;
  cursor: pointer;
  color: #666;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 20px;
  line-height: 1;
}
.earn-details-card .price-details {
  margin-bottom: 20px;
}
.earn-details-card .price-details .detail {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 16px;
  height: 46px;
  background: #F0F6FF;
  border-radius: 10px;
  padding: 0 16px;
}
.earn-details-card .price-details .title {
  font-size: 14px;
  font-weight: 500;
}
.earn-details-card .price-details .price,
.earn-details-card .price-details .earn {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-primary);
}
.earn-details-card .fees-section p {
  margin: 5px 0;
  font-size: 14px;
}
.earn-details-card .fees-section p strong {
  font-weight: 600;
}
.earn-details-card .fees-section .author-fee-title {
  font-size: 18px;
  margin-bottom: 10px;
}
.earn-details-card .fees-section .author-fee {
  color: #ff4d4f;
}
.earn-details-card .fees-section .fee-description {
  color: #ff4d4f;
  font-size: 12px;
}
.earn-details-card .fees-section .author-fee-description {
  margin-top: 5px;
}

.td-sidebar-sticky {
  position: sticky;
  top: 100px;
}

/*----------------------------------------*/
/* User Profile
/*----------------------------------------*/
.user-dashboard-info-area {
  background: var(--td-white);
  border-radius: 15px;
  padding: 40px 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px 20px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .user-dashboard-info-area {
    flex-direction: column;
    align-items: start;
  }
}
@media (max-width: 575px) {
  .user-dashboard-info-area {
    padding: 20px 20px;
  }
}

.user-dashboard-info-inner {
  display: flex;
  align-items: center;
  align-items: center;
  gap: 130px 50px;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .user-dashboard-info-inner {
    gap: 30px 50px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .user-dashboard-info-inner {
    gap: 30px 20px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .user-dashboard-info-inner {
    flex-direction: column;
    align-items: start;
  }
}

.dashboard-user-profile {
  display: flex;
  align-items: center;
  gap: 16px;
}
.dashboard-user-profile .user-thumbnail {
  max-width: 120px;
  flex: 0 0 auto;
  border: 1px solid var(--td-primary);
  border-radius: 50%;
}
@media (max-width: 480px) {
  .dashboard-user-profile .user-thumbnail {
    max-width: 86px;
  }
}
.dashboard-user-profile .title {
  margin-bottom: 10px;
  font-size: 30px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px) {
  .dashboard-user-profile .title {
    font-size: 24px;
    margin-bottom: 5px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .dashboard-user-profile .title {
    font-size: 20px;
  }
}
.dashboard-user-profile .info {
  font-size: 14px;
}
.dashboard-user-profile .info strong {
  color: var(--td-black);
}
.dashboard-user-profile .user-info-inner .user-badge {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}
@media (max-width: 575px) {
  .dashboard-user-profile .user-info-inner .user-badge {
    max-width: 25px;
  }
}
.dashboard-user-profile .user-info-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dashboard-user-info ul {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
@media (max-width: 480px) {
  .dashboard-user-info ul {
    display: grid;
    grid-template-columns: auto auto;
    gap: 20px;
  }
}
.dashboard-user-info ul li {
  list-style: none;
}
.dashboard-user-info ul li:not(:last-child) {
  padding-inline-end: 50px;
  margin-inline-end: 50px;
  position: relative;
}
@media only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1600px) and (max-width: 1800px) {
  .dashboard-user-info ul li:not(:last-child) {
    padding-inline-end: 30px;
    margin-inline-end: 30px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .dashboard-user-info ul li:not(:last-child) {
    padding-inline-end: 20px;
    margin-inline-end: 20px;
  }
}
@media (max-width: 480px) {
  .dashboard-user-info ul li:not(:last-child) {
    padding-inline-end: 12px;
    margin-inline-end: 12px;
  }
}
.dashboard-user-info ul li::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  top: 0;
  inset-inline-end: 0;
  background-color: rgba(0, 0, 0, 0.1);
}
.dashboard-user-info ul li .single-list .title {
  font-size: 26px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .dashboard-user-info ul li .single-list .title {
    font-size: 20px;
  }
}
.dashboard-user-info ul li .single-list .description {
  font-size: 14px;
  font-weight: 600;
}

.dashboard-social-info {
  text-align: end;
}
.dashboard-social-info .author-social {
  margin-bottom: 15px;
}
.dashboard-social-info .author-social ul {
  display: flex;
  align-items: center;
  gap: 10px 15px;
  justify-content: end;
}
.dashboard-social-info .author-social ul li {
  list-style: none;
}
.dashboard-social-info .author-social ul li a {
  width: 30px;
  height: 30px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--td-primary);
  color: var(--td-primary);
}
.dashboard-social-info .author-social ul li a:hover {
  transform: translateY(-3px);
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: transparent;
}
.dashboard-social-info .dashboard-author-rating {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}
.dashboard-social-info .dashboard-author-rating p {
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 500;
}
.dashboard-social-info .dashboard-author-rating .rating-icon {
  color: #FF9900;
  font-size: 14px;
}

.user-dashboard-right {
  display: flex;
  align-items: center;
  gap: 50px;
}

.dashboard-main-box {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 15px;
  padding: 30px 30px;
}
@media (max-width: 480px) {
  .dashboard-main-box {
    padding: 20px 20px;
  }
}

.dashboard-main-top-content {
  padding-bottom: 20px;
  margin-bottom: 50px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.dashboard-main-top-content .dashboard-info-title {
  font-size: 24px;
}

.dashboard-pages-list {
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 51px;
  padding: 10px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .dashboard-pages-list {
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    -o-border-radius: 15px;
    -ms-border-radius: 15px;
    border-radius: 15px;
  }
}
.dashboard-pages-list ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 5px 5px;
  justify-content: space-between;
}
.dashboard-pages-list ul li {
  list-style: none;
}
.dashboard-pages-list ul li.active a {
  color: var(--td-white);
  background-color: var(--td-primary);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
}
.dashboard-pages-list ul li a {
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 35px;
  color: var(--td-primary);
  border-radius: 51px;
  font-size: 14px;
  font-weight: 600;
}

.profile-info-box {
  background: var(--td-alice-blue);
  border-radius: 15px;
  padding: 30px 30px;
}
@media (max-width: 575px) {
  .profile-info-box {
    padding: 20px 20px;
  }
}
.profile-info-box .description {
  margin-bottom: 40px;
}
.profile-info-box .author-sidebar-content hr {
  margin-top: 60px;
  margin-bottom: 25px;
}

/*----------------------------------------*/
/* site table
/*----------------------------------------*/
.td-product-description {
  display: flex;
  align-items: center;
  gap: 10px;
}
.td-product-description .thumb {
  max-width: 70px;
}
.td-product-description .thumb img {
  border-radius: 6px;
}
.td-product-description .content .title {
  font-size: 14px;
  margin-bottom: 5px;
}
.td-product-description .content .title a:hover {
  color: var(--td-primary);
}
.td-product-description .content .description {
  font-size: 12px;
  color: var(--td-primary);
}

.td-table table {
  border-spacing: 0px 20px;
  border-collapse: separate;
  margin-bottom: 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-table table {
    min-width: 650px;
  }
}
.td-table table thead tr th {
  color: var(--td-body-text);
  font-size: 14px;
  font-weight: 500;
  border: 0;
  background: transparent;
  padding: 0px 12px;
}
.td-table table thead tr th span {
  display: flex;
  align-items: center;
  gap: 6px;
}
.td-table table tbody tr {
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
}
.td-table table tbody tr td {
  font-size: 14px;
  font-weight: 500;
  background: var(--td-white);
  color: var(--td-body-text);
  padding: 12px 12px;
  border: 0;
  vertical-align: middle;
}
.td-table table tbody tr td:first-child {
  border-radius: 8px 0px 0px 8px;
}
[dir=rtl] .td-table table tbody tr td:first-child {
  border-radius: 0px 8px 8px 0px;
}
.td-table table tbody tr td:last-child {
  border-radius: 0px 8px 8px 0px;
}
[dir=rtl] .td-table table tbody tr td:last-child {
  border-radius: 8px 0px 0px 8px;
}

@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .downloads-history-table table {
    min-width: 1000px;
  }
}
.downloads-history-table table tbody tr {
  box-shadow: none;
}
.downloads-history-table table tbody tr td {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.38);
  border-inline-start: 0;
  border-inline-end: 0;
}
.downloads-history-table table tbody tr td:first-child {
  border-inline-start: 1px solid rgba(0, 101, 255, 0.38);
}
.downloads-history-table table tbody tr td:last-child {
  border-inline-end: 1px solid rgba(0, 101, 255, 0.38);
}

.td-table.payout-history-table table {
  border-spacing: 0px 20px;
  border-collapse: collapse;
  margin-bottom: 0;
}
.td-table.payout-history-table table thead tr th {
  padding: 15px 20px;
  background: var(--td-white);
  border-radius: 8px;
  border-radius: 0;
}
.td-table.payout-history-table table thead tr th:first-child {
  border-radius: 8px 0px 0px 8px;
}
[dir=rtl] .td-table.payout-history-table table thead tr th:first-child {
  border-radius: 0px 8px 8px 0px;
}
.td-table.payout-history-table table thead tr th:last-child {
  border-radius: 0px 8px 8px 0px;
}
[dir=rtl] .td-table.payout-history-table table thead tr th:last-child {
  border-radius: 8px 0px 0px 8px;
}
.td-table.payout-history-table table tbody tr {
  box-shadow: none;
}
.td-table.payout-history-table table tbody tr td {
  background: var(--td-alice-blue);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 20px 12px;
}
.td-table.payout-history-table table tbody tr td:first-child, .td-table.payout-history-table table tbody tr td:last-child {
  border-radius: 0;
}
.td-table.statements-table table {
  border-spacing: 0px 20px;
  border-collapse: collapse;
  margin-bottom: 0;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px) {
  .td-table.statements-table table {
    min-width: 900px;
  }
}
.td-table.statements-table table thead tr th {
  padding: 14px 20px;
  background: var(--td-alice-blue);
  border-radius: 8px;
  border-radius: 0;
}
.td-table.statements-table table thead tr th:first-child {
  border-radius: 8px 0px 0px 8px;
}
[dir=rtl] .td-table.statements-table table thead tr th:first-child {
  border-radius: 0px 8px 8px 0px;
}
.td-table.statements-table table thead tr th:last-child {
  border-radius: 0px 8px 8px 0px;
}
[dir=rtl] .td-table.statements-table table thead tr th:last-child {
  border-radius: 8px 0px 0px 8px;
}
.td-table.statements-table table tbody tr {
  box-shadow: none;
}
.td-table.statements-table table tbody tr td {
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 20px 12px;
}
.td-table.statements-table table tbody tr td:first-child, .td-table.statements-table table tbody tr td:last-child {
  border-radius: 0;
}

.td-table.table-style-one table {
  border-spacing: 0px 20px;
  border-collapse: collapse;
  margin-bottom: 0;
}
.td-table.table-style-one table thead tr th {
  padding: 15px 20px;
  border-inline-end: 0;
  background: var(--td-alice-blue);
}
.td-table.table-style-one table thead tr th:first-child {
  border-radius: 8px 0px 0px 8px;
}
[dir=rtl] .td-table.table-style-one table thead tr th:first-child {
  border-radius: 0px 8px 8px 0px;
}
.td-table.table-style-one table thead tr th:last-child {
  border-radius: 0px 8px 8px 0px;
}
[dir=rtl] .td-table.table-style-one table thead tr th:last-child {
  border-radius: 8px 0px 0px 8px;
}
.td-table.table-style-one table tbody tr {
  box-shadow: none;
}
.td-table.table-style-one table tbody tr td {
  background: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 20px 15px;
}
.td-table.table-style-one table tbody tr td:first-child, .td-table.table-style-one table tbody tr td:last-child {
  border-radius: 0;
}
.td-table.analytics-table table {
  min-width: 700px;
}

/*----------------------------------------*/
/* dashboard card
/*----------------------------------------*/
.dashboard-white-card {
  padding: 30px 30px;
  background: var(--td-white);
  box-shadow: 0px 4px 20px rgba(0, 101, 255, 0.08);
  border-radius: 15px;
}
@media (max-width: 480px) {
  .dashboard-white-card {
    padding: 20px 20px;
  }
}

.dashboard-aliceBlue-card {
  background: var(--td-alice-blue);
  border-radius: 15px;
  padding: 30px 30px;
}
@media (max-width: 575px) {
  .dashboard-aliceBlue-card {
    padding: 20px 20px;
  }
}
.dashboard-aliceBlue-card.upload-card {
  height: 480px;
}
.dashboard-aliceBlue-card.upload-card .content-inner {
  display: flex;
  align-items: end;
  height: 83%;
}

.dashboard-card-heading {
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.dashboard-card-heading > .description {
  font-size: 14px;
}
.dashboard-card-heading .td-dashboard-title {
  font-size: 20px;
}

.dashboard-card-heading-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px 10px;
}

.td-dashboard-sidebar {
  padding: 20px 20px;
  background: var(--td-alice-blue);
  border-radius: 15px;
}

.sidebar-heading {
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.td-dashboard-sidebar-list {
  padding-inline-start: 16px;
  border-inline-start: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 25px;
}
.td-dashboard-sidebar-list ul li {
  list-style: none;
}
.td-dashboard-sidebar-list ul li:not(:last-child) {
  margin-bottom: 18px;
}
.td-dashboard-sidebar-list ul li.active a {
  color: var(--td-primary);
  position: relative;
}
.td-dashboard-sidebar-list ul li.active a::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 2px;
  background-color: var(--td-primary);
  top: 0;
  inset-inline-start: -17.5px;
}
.td-dashboard-sidebar-list ul li a {
  display: flex;
  align-items: center;
  gap: 8px;
}
.td-dashboard-sidebar-list ul li a span {
  font-size: 14px;
  font-weight: 500;
}

.td-underline-link {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-primary);
  text-decoration: underline;
}

/*----------------------------------------*/
/* Dashboard css
/*----------------------------------------*/
.td-overlayping-top {
  margin: -125px auto 0;
  position: relative;
  z-index: 9;
}

.td-dashboard-banner-area {
  background: var(--td-primary);
  height: 345px;
  position: relative;
  z-index: 5;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px) {
  .td-dashboard-banner-area {
    height: 245px;
  }
}
@media only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px) {
  .td-dashboard-banner-area {
    height: 245px;
  }
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px) {
  .td-dashboard-banner-area {
    height: 215px;
  }
}
.td-dashboard-banner-area .banner-pattern {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  z-index: -1;
  background-size: cover;
  background-position: center;
}

.dashboard-edit {
  text-align: end;
  padding-top: 40px;
}
@media (max-width: 575px), only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 1400px) and (max-width: 1599px), only screen and (min-width: 1600px) and (max-width: 1800px) {
  .dashboard-edit {
    padding-top: 20PX;
  }
}
.dashboard-edit .dashboard-edit-btn {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: var(--td-white);
  border-radius: 10px;
  font-size: 18px;
  color: var(--td-heading);
}

.dashboard-sales-chart {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 20px 20px;
}

.announcements-list ul li {
  list-style: none;
}
.announcements-list ul li .link {
  color: var(--td-primary);
}
.announcements-list ul li:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  padding-bottom: 20px;
}
.announcements-list ul li .description {
  font-size: 14px;
}

.announcements-date {
  font-size: 12px;
  font-weight: 600;
}

.author-info-list ul {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px 14px;
}
@media only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 768px) and (max-width: 991px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .author-info-list ul {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 480px) {
  .author-info-list ul {
    grid-template-columns: auto;
  }
}
.author-info-list ul li {
  list-style: none;
}
.author-info-list ul li span {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--td-primary);
}
.author-info-list ul li span a {
  color: var(--td-primary);
  text-decoration: underline;
}
@media (max-width: 480px) {
  .author-info-list ul li span a {
    font-size: 14px;
  }
}
.author-info-list ul li span a:hover {
  text-decoration: none;
}

.archived-single-item {
  display: grid;
  grid-template-columns: 163px auto auto;
  gap: 30px 30px;
  place-items: center;
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 8px;
  padding: 20px 20px;
  margin-bottom: 5px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .archived-single-item {
    grid-template-columns: 1fr;
    place-items: start;
  }
}
.archived-single-item .thumb img {
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}
.archived-single-item .contents .title {
  font-size: 24px;
  margin-bottom: 8px;
}
@media only screen and (min-width: 1600px) and (max-width: 1800px), only screen and (min-width: 1400px) and (max-width: 1599px) {
  .archived-single-item .contents .title {
    font-size: 18px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1399px), only screen and (min-width: 992px) and (max-width: 1199px), only screen and (min-width: 576px) and (max-width: 767px), (max-width: 575px) {
  .archived-single-item .contents .title {
    font-size: 18px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px), (max-width: 480px) {
  .archived-single-item .contents .title {
    font-size: 16px;
  }
}
.archived-single-item .contents .title a:hover {
  color: var(--td-primary);
}
.archived-single-item .contents .info {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-primary);
}
.archived-single-item .content-info .medium-price-info {
  margin-bottom: 5px;
}
.archived-single-item .date-info span {
  font-size: 14px;
  font-weight: 500;
}

.small-price-info .current-price {
  font-size: 14px;
  color: var(--td-primary);
  font-weight: 500;
}
.small-price-info .old-price {
  font-size: 10px;
}

.medium-price-info .current-price {
  font-size: 18px;
  color: var(--td-primary);
  font-weight: 500;
}
.medium-price-info .old-price {
  font-size: 14px;
}

.item-rating-option {
  margin-bottom: 15px;
}
.item-rating-option > span {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}

.item-rating-star {
  display: inline-block;
  border: 0;
}
.item-rating-star > input {
  display: none;
}
.item-rating-star > label {
  float: right;
}
.item-rating-star > label:before {
  display: inline-block;
  font-size: 16px;
  padding: 0rem 0.2rem;
  margin: 0;
  cursor: pointer;
  font-family: FontAwesome;
  content: "\f005";
}
.item-rating-star .half:before {
  content: "\f089";
  position: absolute;
  padding-inline-end: 0;
}
.item-rating-star input:checked ~ label,
.item-rating-star label > :hover, .item-rating-star label:hover ~ label {
  color: #FFD700;
}
.item-rating-star input:checked + label:hover, .item-rating-star input:checked ~ label:hover,
.item-rating-star input:checked ~ label:hover ~ label,
.item-rating-star label:hover ~ input:checked ~ label {
  color: #FFED85;
}

.item-rating-form .td-single-input .input-field textarea {
  background-color: var(--td-white);
  border: 0;
  font-size: 14px;
}
.item-rating-form .td-single-input .input-field textarea::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
}
.item-rating-form .td-single-input .input-field textarea::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
}
.item-rating-form .td-single-input .input-field textarea:-moz-placeholder { /* Firefox 4-18 */
  font-size: 14px;
}
.item-rating-form .td-single-input .input-field textarea:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 14px;
}
.item-rating-form .td-single-input .input-field textarea::placeholder { /* MODERN BROWSER */
  font-size: 14px;
}
.item-rating-form .td-single-input .input-field textarea:focus {
  background-color: var(--td-white);
}

.single-reviews-card {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 30px 30px;
}
@media (max-width: 480px) {
  .single-reviews-card {
    padding: 20px 20px;
  }
}
.single-reviews-card .contents .info {
  margin-bottom: 10px;
  display: block;
  color: var(--td-heading);
  font-size: 14px;
  font-weight: 500;
}
.single-reviews-card .contents .title-inner {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  gap: 5px;
}
.single-reviews-card .contents .title-inner .rating-icon {
  display: flex;
  gap: 2px;
}
.single-reviews-card .contents .title-inner .rating-icon span {
  color: var(--td-yellow);
  font-size: 14px;
}
.single-reviews-card .contents .title {
  font-size: 30px;
  color: var(--td-primary);
}
.single-reviews-card .contents .description {
  font-size: 12px;
  font-weight: 500;
}

.user-review-rating-bar .rating-item {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  align-items: center;
  line-height: 1;
}
.user-review-rating-bar .rating-item:not(:last-child) {
  margin-bottom: 4px;
}
.user-review-rating-bar .rating-item .progress {
  flex-grow: 1;
}
.user-review-rating-bar .rating-item .rating-star {
  font-size: 12px;
}
.user-review-rating-bar .rating-item .rating-number span {
  font-size: 14px;
  font-weight: 600;
}
.user-review-rating-bar .rating-item .progress-tittle {
  font-size: 12px;
}
.user-review-rating-bar .progress {
  display: flex;
  height: 7px;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 0.25rem;
}

.user-email-checkbox,
.td-licence-check {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  padding: 0px 15px;
  height: 50px;
  display: flex;
  align-items: center;
}

.payout-card-box [class*=col-]:nth-child(2) .payout-card-item {
  background-color: #FBA11D;
}
.payout-card-box [class*=col-]:nth-child(3) .payout-card-item {
  background-color: #27ED7E;
}

.payout-card-item {
  background: #0EDDF4;
  border-radius: 10px;
  padding: 20px 25px;
}
.payout-card-item .contents .description {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
}
.payout-card-item .contents .title {
  font-size: 30px;
}
@media (max-width: 575px) {
  .payout-card-item .contents .title {
    font-size: 20px;
  }
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .payout-card-item .contents .title {
    font-size: 24px;
  }
}
.payout-card-item .contents .date {
  margin-top: 5px;
  display: flex;
  gap: 5px;
}
.payout-card-item .contents .date span {
  font-size: 14px;
}
.payout-card-item .contents .link .text-link {
  font-size: 12px;
  text-decoration: underline;
}

.payout-sales-chart {
  background: var(--td-white);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 20px 20px;
}

.dashboard-chart-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px 10px;
}
.dashboard-chart-heading .title {
  font-size: 20px;
}
.dashboard-chart-heading .td-dropdown .dropdown-menu {
  background: var(--td-white);
  box-shadow: 4px 4px 20px rgba(0, 0, 0, 0.1);
  border: 0;
  padding: 15px 20px;
}
.dashboard-chart-heading .td-dropdown ul li:not(:last-child) {
  margin-bottom: 10px;
}

.sales-progress-wrapper .rating-item:not(:last-child) {
  margin-bottom: 25px;
}
.sales-progress-wrapper .rating-item .progress {
  border-radius: 30px;
}
.sales-progress-wrapper .rating-item .progress .progress-bar {
  border-radius: 30px;
}
.sales-progress-wrapper .progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 5px;
}
.sales-progress-wrapper .progress-info .info {
  color: var(--td-body-text);
  font-size: 14px;
  font-weight: 400;
}
.sales-progress-wrapper .progress-info .percentage {
  color: var(--td-heading);
  font-weight: 500;
  font-size: 14px;
}

.user-settings-form-box .payment-max-input .td-single-input .input-field input {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.08);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  font-size: 14px;
  height: 46px;
}
.user-settings-form-box .payment-max-input .td-single-input .input-field input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
}
.user-settings-form-box .payment-max-input .td-single-input .input-field input::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
}
.user-settings-form-box .payment-max-input .td-single-input .input-field input:-moz-placeholder { /* Firefox 4-18 */
  font-size: 14px;
}
.user-settings-form-box .payment-max-input .td-single-input .input-field input:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 14px;
}
.user-settings-form-box .payment-max-input .td-single-input .input-field input::placeholder { /* MODERN BROWSER */
  font-size: 14px;
}
.user-settings-form-box .payout-option-check-input {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.08);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  padding: 10px 15px;
}
.user-settings-form-box .payout-option-check-input .td-single-input .input-field input {
  height: 30px;
  background: rgba(247, 158, 28, 0.1);
  border-radius: 5px;
  font-size: 10px;
  color: var(--td-yellow);
  padding: 0 10px;
  border: 0;
}
.user-settings-form-box .payout-option-check-input .td-single-input .input-field input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: var(--td-yellow);
  font-size: 10px;
}
.user-settings-form-box .payout-option-check-input .td-single-input .input-field input::-moz-placeholder { /* Firefox 19+ */
  color: var(--td-yellow);
  font-size: 10px;
}
.user-settings-form-box .payout-option-check-input .td-single-input .input-field input:-moz-placeholder { /* Firefox 4-18 */
  color: var(--td-yellow);
  font-size: 10px;
}
.user-settings-form-box .payout-option-check-input .td-single-input .input-field input:-ms-input-placeholder { /* IE 10+  Edge*/
  color: var(--td-yellow);
  font-size: 10px;
}
.user-settings-form-box .payout-option-check-input .td-single-input .input-field input::placeholder { /* MODERN BROWSER */
  color: var(--td-yellow);
  font-size: 10px;
}
.user-settings-form-box .checkbox-inner {
  display: flex;
  break-before: column;
  justify-content: space-between;
}
.user-settings-form-box .checkbox-inner .form-check-label {
  font-size: 12px;
  padding-inline-start: 22px;
  font-weight: 500;
}

.payout-history-form {
  display: grid;
  grid-template-columns: repeat(3, 1fr) auto;
  width: 100%;
  gap: 15px;
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  border-radius: 5px;
  padding: 20px 20px;
  margin-bottom: 30px;
}
@media only screen and (min-width: 576px) and (max-width: 767px), only screen and (min-width: 768px) and (max-width: 991px) {
  .payout-history-form {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .payout-history-form {
    grid-template-columns: 1fr;
  }
}
.payout-history-form .td-single-input .input-field input {
  font-size: 14px;
  font-weight: 500;
  background: rgba(0, 101, 255, 0.1);
  border: 1px solid rgba(0, 101, 255, 0.1);
  border-radius: 6px;
  padding: 0 16px;
  height: 46px;
}
.payout-history-form .td-single-input .input-field input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
}
.payout-history-form .td-single-input .input-field input::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
}
.payout-history-form .td-single-input .input-field input:-moz-placeholder { /* Firefox 4-18 */
  font-size: 14px;
}
.payout-history-form .td-single-input .input-field input:-ms-input-placeholder { /* IE 10+  Edge*/
  font-size: 14px;
}
.payout-history-form .td-single-input .input-field input::placeholder { /* MODERN BROWSER */
  font-size: 14px;
}
.payout-history-form .td-single-input .td-input-filter .nice-select.open {
  background: rgba(0, 101, 255, 0.1);
  border: 1px solid rgba(0, 101, 255, 0.1);
}
.payout-history-form .td-single-input .td-input-filter .nice-select {
  background: rgba(0, 101, 255, 0.1);
  border: 1px solid rgba(0, 101, 255, 0.1);
}
.payout-history-form .td-single-input .td-input-filter .nice-select:focus {
  background: rgba(0, 101, 255, 0.1);
  border: 1px solid rgba(0, 101, 255, 0.1);
}

.statements-card-form {
  display: flex;
  align-items: center;
  gap: 8px;
}
.statements-card-form .td-single-input input {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  height: 38px;
  padding: 0 18px;
}
.statements-card-form .td-single-input .td-input-filter .nice-select {
  background: var(--td-alice-blue);
  border: 1px solid rgba(0, 101, 255, 0.2);
  border-radius: 10px;
  padding: 0 18px;
}

.author-sidebar-box {
  background: var(--td-white);
  border-radius: 15px;
  padding: 25px 30px 30px;
}
@media (max-width: 480px) {
  .author-sidebar-box {
    padding: 15px 20px 20px;
  }
}
.author-sidebar-box .heading {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.author-sidebar-box .author-sidebar-content .description {
  font-size: 14px;
  line-height: 1.7;
}
.author-sidebar-box .author-sidebar-content .description strong {
  color: var(--td-heading);
}
.author-sidebar-box .author-sidebar-content hr {
  margin-top: 60px;
  margin-bottom: 25px;
}
@media (max-width: 480px) {
  .author-sidebar-box .author-sidebar-content hr {
    margin-top: 40px;
    margin-bottom: 15px;
  }
}

.example-list-box {
  background: var(--td-white);
  border-radius: 6px;
  padding: 20px 20px;
}

.example-list-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
}

.example-list ul li {
  list-style: none;
  font-size: 12px;
  position: relative;
  padding-inline-start: 15px;
}
.example-list ul li:not(:last-child) {
  margin-bottom: 5px;
}
.example-list ul li:before {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  background-color: var(--td-text-primary);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 0;
}

/*# sourceMappingURL=styles.css.map */
