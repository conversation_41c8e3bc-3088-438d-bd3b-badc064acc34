<?php

namespace App\Http\Controllers\Backend;

use App\Enums\TxnStatus;
use App\Enums\UserMiningStatus;
use App\Http\Controllers\Controller;
use App\Models\UserMining;
use Illuminate\Http\Request;

class UserMiningController extends Controller
{
    public function userMinings(Request $request)
    {
        $userMinings = UserMining::query()
            ->with(['user', 'scheme'])
            ->when($request->user, fn ($q) => $q->whereHas('user', fn ($q) => $q->where('username', 'like', '%'.$request->user.'%')))
            ->when($request->tnx, fn ($q) => $q->where('tnx', 'like', '%'.$request->tnx.'%'))
            ->when($request->type, fn ($q) => $q->where('type', $request->type))
            ->when($request->status, fn ($q) => $q->where('status', $request->status))
            ->when($request->schema, fn ($q) => $q->where('schema_id', $request->schema))
            ->when(in_array(request('sort_field'), ['created_at', 'plan_price', 'total_mined_amount', 'total_mined_count']), function ($query) {
                $query->orderBy(request('sort_field'), request('sort_dir'));
            })
            ->when($request->get('sort_field') == 'user', function ($query) {
                $query->whereHas('user', function ($userQuery) {
                    $userQuery->orderBy('username', request('sort_dir'));
                });
            })
            ->when(! $request->has('sort_field'), function ($query) {
                $query->latest();
            })
            ->paginate()
            ->withQueryString();

        return view('backend.user_mining.index', compact('userMinings'));
    }

    public function show($id)
    {
        $userMining = UserMining::with(['user', 'scheme', 'scheme.miner', 'transaction'])
            ->findOrFail($id);

        return view('backend.user_mining.show', compact('userMining'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'mining_count' => 'required|integer|min:0',
            'last_mining_time' => 'nullable|date',
            'next_mining_time' => 'nullable|date',
            'status' => 'required',
        ]);

        $userMining = UserMining::findOrFail($id);

        $userMining->update([
            'mining_count' => $request->mining_count,
            'last_mining_time' => empty($request->last_mining_time) ? null : $request->last_mining_time,
            'next_mining_time' => $request->next_mining_time,
            'status' => $request->status,
        ]);

        // if cancelled, then cancel all related transactions
        if ($request->status == UserMiningStatus::Canceled) {
            $userMining->transaction->update([
                'status' => TxnStatus::Failed,
            ]);
            $userMining->childTransactions->update([
                'status' => TxnStatus::Failed,
            ]);
        }

        notify()->success(__('User mining details updated successfully'));

        return redirect()->back();
    }
}
