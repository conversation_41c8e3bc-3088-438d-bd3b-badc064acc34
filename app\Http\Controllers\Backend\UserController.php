<?php

namespace App\Http\Controllers\Backend;

use App\Enums\KYCStatus;
use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Enums\UserType;
use App\Http\Controllers\Controller;
use App\Models\Kyc;
use App\Models\Ticket;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserKyc;
use App\Models\UserWallet;
use App\Traits\ImageUpload;
use App\Traits\NotifyTrait;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class UserController extends Controller implements HasMiddleware
{
    use ImageUpload;
    use NotifyTrait;

    public static function middleware()
    {
        return [
            new Middleware('permission:customer-list|customer-login|customer-mail-send|customer-basic-manage|customer-change-password|all-type-status|customer-balance-add-or-subtract', ['only' => ['index', 'activeUser', 'disabled', 'mailSendAll', 'mailSend']]),
            new Middleware('permission:customer-login', ['only' => ['userLogin']]),
            new Middleware('permission:customer-mail-send', ['only' => ['mailSendAll', 'mailSend']]),
            new Middleware('permission:customer-basic-manage', ['only' => ['update']]),
            new Middleware('permission:customer-change-password', ['only' => ['passwordUpdate']]),
            new Middleware('permission:all-type-status', ['only' => ['statusUpdate']]),
        ];
    }

    public function index(Request $request)
    {
        $search = $request->query('query') ?? null;

        $users = User::query()
            ->role(UserType::User)
            ->when(! blank(request('email_status')), function ($query) {
                if (request('email_status')) {
                    $query->whereNotNull('email_verified_at');
                } else {
                    $query->whereNull('email_verified_at');
                }
            })
            ->when(! blank(request('kyc_status')), function ($query) {
                $query->where('kyc', request('kyc_status'));
            })
            ->when(! blank(request('status')), function ($query) {
                $query->where('status', request('status'));
            })
            ->when(! blank(request('sort_field')), function ($query) {
                $query->orderBy(request('sort_field'), request('sort_dir'));
            })
            ->when(! request()->has('sort_field'), function ($query) {
                $query->latest();
            })
            ->search($search)
            ->paginate();

        $title = __('All Customers');

        return view('backend.user.index', ['users' => $users, 'title' => $title]);
    }

    public function activeUser(Request $request)
    {

        $search = $request->query('query') ?? null;

        $users = User::active()
            ->role(UserType::User)
            ->when(! blank(request('email_status')), function ($query) {
                if (request('email_status')) {
                    $query->whereNotNull('email_verified_at');
                } else {
                    $query->whereNull('email_verified_at');
                }
            })
            ->when(! blank(request('kyc_status')), function ($query) {
                $query->where('kyc', request('kyc_status'));
            })
            ->when(! blank(request('status')), function ($query) {
                $query->where('status', request('status'));
            })
            ->when(! blank(request('sort_field')), function ($query) {
                $query->orderBy(request('sort_field'), request('sort_dir'));
            })
            ->when(! request()->has('sort_field'), function ($query) {
                $query->latest();
            })
            ->search($search)
            ->paginate();

        $title = __('Active Customers');

        return view('backend.user.index', ['users' => $users, 'title' => $title]);
    }

    public function disabled(Request $request)
    {
        $search = $request->query('query') ?? null;

        $users = User::disabled()
            ->role(UserType::User)
            ->when(! blank(request('email_status')), function ($query) {
                if (request('email_status')) {
                    $query->whereNotNull('email_verified_at');
                } else {
                    $query->whereNull('email_verified_at');
                }
            })
            ->when(! blank(request('kyc_status')), function ($query) {
                $query->where('kyc', request('kyc_status'));
            })
            ->when(! blank(request('status')), function ($query) {
                $query->where('status', request('status'));
            })
            ->when(! blank(request('sort_field')), function ($query) {
                $query->orderBy(request('sort_field'), request('sort_dir'));
            })
            ->when(! request()->has('sort_field'), function ($query) {
                $query->latest();
            })
            ->search($search)
            ->paginate();

        $title = __('Disabled Customers');

        return view('backend.user.index', ['users' => $users, 'title' => $title]);
    }

    public function closed(Request $request)
    {
        $search = $request->query('query') ?? null;

        $users = User::closed()
            ->role(UserType::User)
            ->when(! blank(request('email_status')), function ($query) {
                if (request('email_status')) {
                    $query->whereNotNull('email_verified_at');
                } else {
                    $query->whereNull('email_verified_at');
                }
            })
            ->when(! blank(request('kyc_status')), function ($query) {
                $query->where('kyc', request('kyc_status'));
            })
            ->when(! blank(request('status')), function ($query) {
                $query->where('status', request('status'));
            })
            ->when(! blank(request('sort_field')), function ($query) {
                $query->orderBy(request('sort_field'), request('sort_dir'));
            })
            ->when(! request()->has('sort_field'), function ($query) {
                $query->latest();
            })
            ->search($search)
            ->paginate();

        $title = __('Closed Customers');

        return view('backend.user.index', ['users' => $users, 'title' => $title]);
    }

    public function create()
    {
        $kycs = Kyc::where('status', true)->get();

        return view('backend.user.create', ['kycs' => $kycs]);
    }

    public function edit($id)
    {
        $user = User::role(UserType::User)->findOrFail($id);

        $transactions = null;
        $tickets = null;

        if (request('tab') == 'transactions') {
            $transactions = Transaction::with('wallet.coin')->where('user_id', $id)
                ->search(request('query'))
                ->when(request('type') != null, function ($query) {
                    $query->where('type', request('type'));
                })
                ->when(request('sort_field') != null, function ($query) {
                    $query->orderBy(request('sort_field'), request('sort_dir'));
                })
                ->when(! request()->has('sort_field'), function ($query) {
                    $query->latest();
                })
                ->paginate()
                ->withQueryString();
        } elseif (request('tab') == 'ticket') {
            $tickets = Ticket::where('user_id', $id)
                ->when(request('query') != null, function ($query) {
                    $query->where('title', 'LIKE', '%'.request('query').'%');
                })
                ->when(in_array(request('sort_field'), ['created_at', 'title', 'status']), function ($query) {
                    $query->orderBy(request('sort_field'), request('sort_dir'));
                })
                ->when(! request()->has('sort_field'), function ($query) {
                    $query->latest();
                })
                ->paginate()
                ->withQueryString();
        }

        $statistics = [
            'total_withdraw' => $user->totalWithdrawCount(),
            'total_deposit' => $user->totalDepositCount(),
            'total_payments' => $user->totalPaymentsCount(),
            'total_tickets' => $user->tickets()->count(),
            'total_referral' => $user->referrals()->count(),
        ];

        return view('backend.user.edit', [
            'user' => $user,
            'statistics' => $statistics,
            'transactions' => $transactions,
            'tickets' => $tickets,
        ]);
    }

    public function statusUpdate($id, Request $request)
    {
        DB::beginTransaction();

        try {
            $data = [
                'status' => $request->status,
                'kyc' => $request->kyc,
                'two_fa' => $request->two_fa,
                'deposit_status' => $request->deposit_status,
                'withdraw_status' => $request->withdraw_status,
                'plan_purchase_status' => $request->plan_purchase_status,
                'otp_status' => $request->otp_status,
                'email_verified_at' => $request->email_verified == 1 ? now() : null,
            ];

            $user = User::find($id);

            if ($user->status != $request->status && ! $request->status) {

                $shortcodes = [
                    '[[full_name]]' => $user->full_name,
                    '[[site_title]]' => setting('site_title', 'global'),
                    '[[site_url]]' => route('home'),
                ];

                $this->mailNotify($user->email, 'user_account_disabled', $shortcodes);
                $this->smsNotify('user_account_disabled', $shortcodes, $user->phone);
            }

            User::find($id)->update($data);

            if (! $request->kyc) {
                $this->markAsUnverified($id);
            }

            DB::commit();

            $status = 'success';
            $message = __('Status Updated Successfully');
        } catch (\Exception $exception) {
            DB::rollBack();

            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }

    public function balanceUpdate($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required',
            'type' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first(), 'Error');

            return redirect()->back();
        }

        try {
            $amount = $request->amount;
            $type = $request->type;
            $user = User::find($id);
            $adminUser = Auth::user();
            $wallet_type = $request->wallet_type;
            $user_wallet = UserWallet::find($wallet_type);

            if ($wallet_type == 'default') {
                $wallet_name = 'Main';
            } else {
                $wallet_name = $user_wallet?->currency?->name;
            }

            if ($type == 'add') {
                if ($wallet_type == 'default') {
                    $user->balance += $amount;
                    $user->save();
                } else {
                    $user_wallet->balance += $amount;
                    $user_wallet->save();
                }

                Transaction::create([
                    'user_id' => $id,
                    'from_user_id' => $adminUser->id,
                    'wallet_type' => $wallet_type,
                    'from_model' => 'Admin',
                    'description' => 'Money added in '.ucwords($wallet_name).' Wallet from System',
                    'type' => TxnType::Credit,
                    'amount' => $amount,
                    'charge' => 0,
                    'final_amount' => $amount,
                    'method' => 'System',
                    'status' => TxnStatus::Success,
                ]);

                $message = __('Balance added successfully!');
            } elseif ($type == 'subtract') {
                if ($wallet_type == 0) {
                    $user->balance -= $amount;
                    $user->save();
                } else {
                    $user_wallet->balance -= $amount;
                    $user_wallet->save();
                }

                Transaction::create([
                    'user_id' => $id,
                    'from_user_id' => $adminUser->id,
                    'wallet_type' => $wallet_type,
                    'from_model' => 'Admin',
                    'description' => 'Money subtract in '.ucwords($wallet_name).' Wallet from System',
                    'type' => TxnType::Debit,
                    'amount' => $amount,
                    'charge' => 0,
                    'final_amount' => $amount,
                    'method' => 'System',
                    'status' => TxnStatus::Success,
                ]);

                $message = __('Balance subtracted successfully!');
            }

            notify()->success($message);

            return redirect()->back();
        } catch (Exception $e) {
            notify()->warning(__('Sorry, something is wrong'));

            return back();
        }
    }

    protected function markAsUnverified($user_id)
    {
        UserKyc::where('user_id', $user_id)->where('is_valid', true)->update([
            'is_valid' => false,
        ]);
    }

    public function store(Request $request)
    {
        $usernameRequired = $this->isFieldRequired('username');
        $phoneRequired = $this->isFieldRequired('phone');
        $countryRequired = $this->isFieldRequired('country');
        $genderRequired = $this->isFieldRequired('gender');

        $validator = Validator::make($request->all(), [
            'fname' => 'required',
            'lname' => 'required',
            'date_of_birth' => 'nullable|date',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
            'username' => [Rule::requiredIf($usernameRequired), 'string', 'max:255', 'unique:users,username'],
            'phone' => [Rule::requiredIf($phoneRequired), 'string', 'max:255', 'unique:users,phone'],
            'country' => [Rule::requiredIf($countryRequired), 'string', 'max:255'],
            'gender' => [Rule::requiredIf($genderRequired), 'string', 'in:male,female,other', 'max:255'],
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back()->withInput();
        }

        try {

            DB::beginTransaction();

            $generateUsername = $request->fname.$request->lname;

            $usernameExists = User::where('username', $generateUsername)->exists();

            $user = User::create([
                'first_name' => $request->fname,
                'last_name' => $request->lname,
                'username' => $usernameExists ? generateUniqueUsername($generateUsername) : strtolower($generateUsername),
                'email' => $request->email,
                'phone' => $request->phone,
                'country' => $request->country,
                'gender' => $request->gender,
                'date_of_birth' => $request->filled('date_of_birth') ? $request->date('date_of_birth') : null,
                'city' => $request->city,
                'zip_code' => $request->zip_code,
                'address' => $request->address,
                'password' => bcrypt($request->password),
                'status' => 1,
                'two_fa' => 0,
                'withdraw_status' => 1,
                'otp_status' => 0,
                'email_verified_at' => now(),
                'kyc' => KYCStatus::Verified,
                'portfolio_id' => null,
                'portfolios' => json_encode([]),
            ]);

            $kycs = $request->kyc_credential;
            $user_kycs = [];

            if (is_array($request->kyc_ids)) {

                foreach ($kycs as $id => $kyc) {
                    if (is_array($kyc)) {
                        foreach ($kyc as $key => $value) {
                            if (is_file($value)) {
                                $kycs[$id][$key] = self::imageUploadTrait($value);
                            }
                        }
                    }
                }

                foreach ($request->kyc_ids as $id) {
                    $kyc = Kyc::find($id);

                    $user_kycs[] = [
                        'kyc_id' => $kyc->id,
                        'type' => $kyc->name,
                        'data' => $kycs[$id],
                        'is_valid' => true,
                        'status' => 'approved',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }
            }

            // Create User KYC
            $user->kycs()->createMany($user_kycs);

            DB::commit();

            $status = 'success';
            $message = __('Customer added successfully!');

            notify()->$status($message, $status);

            return to_route('admin.user.edit', $user->id);
        } catch (\Exception $exception) {
            DB::rollBack();

            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();

            notify()->$status($message, $status);

            return back()->withInput();
        }
    }

    private function isFieldRequired($field)
    {
        return getPageSetting("{$field}_show") && getPageSetting("{$field}_validation");
    }

    public function update($id, Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'first_name' => 'required',
            'last_name' => 'required',
            'username' => 'required|unique:users,username,'.$id,
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back();
        }

        try {
            User::find($id)->update($input);

            $status = 'success';
            $message = __('User Info Updated Successfully');
        } catch (\Exception $exception) {
            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }

    public function passwordUpdate($id, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'new_password' => ['required'],
            'new_confirm_password' => ['same:new_password'],
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back();
        }

        try {
            $password = $validator->validated();

            User::find($id)->update([
                'password' => bcrypt($password['new_password']),
            ]);

            $status = 'success';
            $message = __('User Password Updated Successfully');
        } catch (\Exception $exception) {
            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }

    public function mailSendAll()
    {
        return view('backend.user.mail_send_all');
    }

    public function mailSend(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required',
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back();
        }

        try {

            $input = [
                'subject' => $request->subject,
                'message' => $request->message,
            ];

            $shortcodes = [
                '[[site_url]]' => route('home'),
                '[[site_title]]' => setting('site_title', 'global'),
                '[[subject]]' => $input['subject'],
                '[[message]]' => $input['message'],
            ];

            if ($request->id !== null) {
                $user = User::find($request->id);

                $shortcodes = array_merge($shortcodes, ['[[full_name]]' => $user->full_name]);

                $this->sendNotify($user->email, 'user_mail', 'User', $shortcodes, $user->phone, $user->id, route('user.dashboard'));
            } else {
                $users = User::where('status', 1)->get();

                foreach ($users as $user) {
                    $shortcodes = array_merge($shortcodes, ['[[full_name]]' => $user->full_name]);

                    $this->sendNotify($user->email, 'user_mail', 'User', $shortcodes, $user->phone, $user->id, route('user.dashboard'));
                }
            }

            $status = 'Success';
            $message = __('Mail Send Successfully');
        } catch (Exception $exception) {

            $status = 'warning';
            $message = __('Sorry, something is wrong');
        }

        notify()->$status($message, $status);

        return back();
    }

    public function userLogin($id)
    {

        Auth::guard('web')->loginUsingId($id);

        return redirect(route('user.dashboard'));
    }

    public function destroy($id)
    {
        if (config('app.demo')) {
            notify()->error(__('This action is disabled in demo!'));

            return back();
        }

        try {

            DB::beginTransaction();

            $user = User::find($id);
            $user->kycs()->delete();
            $user->agent()->delete();
            $user->merchant()->delete();
            $user->transaction()->delete();
            $user->ticket()->delete();
            $user->activities()->delete();
            $user->notifications()->delete();
            $user->withdrawAccounts()->delete();
            $user->wallets()->delete();
            $user->delete();

            DB::commit();

            notify()->success(__('User deleted successfully'));

            return back();
        } catch (\Throwable $throwable) {
            DB::rollBack();

            notify()->error(__('Sorry, something went wrong!'));

            return back();
        }
    }
}
