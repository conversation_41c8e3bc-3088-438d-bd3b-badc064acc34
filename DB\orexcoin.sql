-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 14, 2025 at 09:42 AM
-- Server version: 8.0.30
-- PHP Version: 8.3.19

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `orexcoin_buyer`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` bigint UNSIGNED NOT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `avatar`, `name`, `email`, `phone`, `password`, `status`, `created_at`, `updated_at`) VALUES
(1, 'global/uploads/images/QTUTUkDJ8ye2qLvFpFYu.png', 'Super Admin', '<EMAIL>', '', '$2y$12$WbpxsP5oRRAv.WtRjbBVnuTNUZHX04628SFh5IHWsdY5JDUPNp3xi', 1, '2024-11-19 22:45:36', '2025-05-22 03:31:59');

-- --------------------------------------------------------

--
-- Table structure for table `blogs`
--

CREATE TABLE `blogs` (
  `id` bigint UNSIGNED NOT NULL,
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `locale_id` int DEFAULT NULL,
  `locale` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'en',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `coins`
--

CREATE TABLE `coins` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `conversion_rate` float(28,8) DEFAULT '0.********',
  `type` enum('fiat','crypto') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'fiat',
  `symbol` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cron_jobs`
--

CREATE TABLE `cron_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `next_run_at` timestamp NULL DEFAULT NULL,
  `last_run_at` timestamp NULL DEFAULT NULL,
  `schedule` int DEFAULT NULL,
  `type` enum('system','custom') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('running','paused') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reserved_method` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cron_jobs`
--

INSERT INTO `cron_jobs` (`id`, `name`, `next_run_at`, `last_run_at`, `schedule`, `type`, `status`, `reserved_method`, `url`, `created_at`, `updated_at`) VALUES
(1, 'User Inactive Account Disabled', '2025-04-24 04:53:11', '2025-04-24 04:23:11', 1800, 'system', 'running', 'userInactive', NULL, NULL, '2025-04-24 04:23:11'),
(2, 'User Portfolio Update', '2025-05-19 11:30:10', '2025-05-19 11:00:10', 1800, 'system', 'running', 'userPortfolio', NULL, NULL, '2025-05-19 11:00:10'),
(3, 'User Mining Return', '2025-06-03 05:10:45', '2025-06-03 04:40:45', 1800, 'system', 'running', 'userMiningReturn', NULL, NULL, '2025-06-03 04:40:45');

-- --------------------------------------------------------

--
-- Table structure for table `cron_job_logs`
--

CREATE TABLE `cron_job_logs` (
  `id` bigint UNSIGNED NOT NULL,
  `cron_job_id` bigint UNSIGNED DEFAULT NULL,
  `error` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `started_at` timestamp NULL DEFAULT NULL,
  `ended_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `currencies`
--

CREATE TABLE `currencies` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` enum('fiat','crypto') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'fiat',
  `symbol` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `conversion_rate` decimal(28,8) DEFAULT NULL,
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `custom_csses`
--

CREATE TABLE `custom_csses` (
  `id` bigint UNSIGNED NOT NULL,
  `css` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `custom_csses`
--

INSERT INTO `custom_csses` (`id`, `css`, `created_at`, `updated_at`) VALUES
(1, '//The Custom CSS will be added on the site head tag\r\n.site-head-tag {\r\n	margin: 0;\r\n  	padding: 0;\r\n}', '2024-11-19 22:45:36', '2025-03-19 02:11:55');

-- --------------------------------------------------------

--
-- Table structure for table `deposit_methods`
--

CREATE TABLE `deposit_methods` (
  `id` bigint UNSIGNED NOT NULL,
  `gateway_id` bigint UNSIGNED DEFAULT NULL,
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` enum('auto','manual') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'manual',
  `gateway_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `charge` decimal(28,8) DEFAULT '0.********',
  `charge_type` enum('percentage','fixed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `minimum_deposit` decimal(28,8) DEFAULT NULL,
  `maximum_deposit` decimal(28,8) DEFAULT NULL,
  `currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency_symbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `field_options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `payment_details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `status` tinyint NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `gateways`
--

CREATE TABLE `gateways` (
  `id` bigint UNSIGNED NOT NULL,
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `gateway_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `supported_currencies` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `credentials` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_withdraw` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0',
  `status` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `gateways`
--

INSERT INTO `gateways` (`id`, `logo`, `name`, `gateway_code`, `supported_currencies`, `credentials`, `is_withdraw`, `status`, `created_at`, `updated_at`) VALUES
(1, 'global/gateway/paypal.png', 'Paypal', 'paypal', '[\"USD\", \"EUR\", \"GBP\", \"CAD\", \"AUD\", \"JPY\", \"SGD\", \"NZD\", \"CHF\", \"SEK\", \"NOK\", \"DKK\", \"PLN\", \"HUF\", \"CZK\", \"ILS\", \"BRL\", \"MXN\", \"HKD\", \"TWD\", \"TRY\", \"INR\", \"RUB\", \"ZAR\", \"MYR\", \"THB\", \"IDR\", \"PHP\", \"NGN\", \"GHS\"]', '{\"client_id\":\"AUnfkQ3v2J-d5g-ZEq8l5Oosha8FmAJ3Z9jt71BkD6l-Z3FMIR5FmkYp_6QHwaYx0LvMNvHDsB9Vh84O\",\"client_secret\":\"EJjci35CFj762ut15pn0VWmojEG0GwE68byyHnBq_NoAXXW9mjkjdLFYLfVQwyLs8QAbP1QJAXPl5oAl\",\"app_id\":\"APP-80W284485P519543T\",\"mode\":\"sandbox\"}', 'paypal_email', 1, NULL, '2023-06-20 10:51:45'),
(2, 'global/gateway/stripe.png', 'Stripe', 'stripe', '[\"USD\",\"AUD\",\"BRL\",\"CAD\",\"CHF\",\"DKK\",\"EUR\",\"GBP\",\"HKD\",\"INR\",\"JPY\",\"MXN\",\"MYR\",\"NOK\",\"NZD\",\"PLN\",\"SEK\",\"SGD\"]', '{\"stripe_key\":\"pk_test_51KHQhKAmfDlh6wQq4srkOEY3FkivTCXmRSb7bJqr90q3ZkVWAR2AkRWfKBnegpmKAHea5cNVAToiy7yoa3Q075mR00jlhXsZTO\",\"stripe_secret\":\"sk_test_51KHQhKAmfDlh6wQqXfg4ZScnTRahxbdXV0mKw30nOI4f8gtB2v5rho7IyJtZqkf8SwwuNgLTO2WPGFyk9vnFl8gO00MhSe8Kbj\"}', '0', 1, NULL, '2022-11-13 01:46:46'),
(3, 'global/gateway/mollie.png', 'Mollie', 'mollie', '[\"EUR\", \"USD\", \"GBP\", \"CAD\", \"AUD\", \"CHF\", \"DKK\", \"NOK\", \"SEK\", \"PLN\", \"CZK\", \"HUF\", \"RON\", \"BGN\", \"HRK\", \"ISK\", \"ZAR\"]', '{\"api_key\":\"test_intSTCDEBaDSu28D6DUpn5wnQhTnzB\"}', '0', 1, NULL, '2022-10-28 09:43:50'),
(4, 'global/gateway/perfectmoney.png', 'Perfect Money', 'perfectmoney', '[\"USD\", \"EUR\", \"RUB\", \"UAH\"]', '{\"PM_ACCOUNTID\":\"********\",\"PM_PASSPHRASE\":\"77887848a\",\"PM_MARCHANTID\":\"*********\",\"PM_MARCHANT_NAME\":\"tdevs\"}', 'member_id', 1, NULL, '2023-06-19 07:01:24'),
(5, 'global/gateway/coinbase.png', 'Coinbase', 'coinbase', '[\"USD\", \"EUR\", \"GBP\", \"CAD\", \"AUD\", \"JPY\", \"BTC\", \"ETH\", \"LTC\", \"BCH\", \"XRP\", \"EOS\"]', '{\"apiKey\":\"8ef6c4ca-f5c7-4717-9d9a-002adf7e7590\",\"account_id\":\"\",\"private_key\":\"\",\"webhookSecret\":\"b789f547-8954-4880-89ae-5a0233006647\",\"apiVersion\":\"2018-03-22\"}', 'email_address', 1, NULL, '2022-10-28 13:16:15'),
(6, 'global/gateway/paystack.png', 'Paystack', 'paystack', '[\"NGN\", \"USD\", \"GBP\", \"EUR\", \"GHS\", \"KES\", \"ZAR\", \"UGX\", \"TZS\", \"RWF\"]', '{\"public_key\":\"pk_test_8e60e513e47ba5619ac0888c9fac99f2853641fa\",\"secret_key\":\"sk_test_e521a3c6d1c37897092868e02e0ddba8c3f0aa01\",\"merchant_email\":\"<EMAIL>\"}', '0', 1, NULL, '2022-12-17 03:18:45'),
(7, 'global/gateway/voguepay.png', 'Voguepay', 'voguepay', '[\"USD\", \"EUR\", \"GBP\", \"NGN\", \"GHS\", \"KES\", \"ZAR\", \"UGX\", \"TZS\", \"RWF\"]', '{\"merchant_id\":\"sandbox_760e43f202878f651659820234cad9\"}', '0', 1, NULL, '2022-11-13 20:08:13'),
(8, 'global/gateway/flutterwave.png', 'Flutterwave', 'flutterwave', '[\"USD\", \"EUR\", \"GBP\", \"NGN\", \"GHS\", \"KES\", \"ZAR\", \"UGX\", \"TZS\", \"RWF\", \"CAD\", \"AUD\", \"JPY\", \"INR\"]', '{\"public_key\":\"FLWPUBK_TEST-34d73043494d02b871d7ba051efa471f-X\",\"secret_key\":\"FLWSECK_TEST-efc192c9a48969fc259c517aef8bcc82-X\",\"encryption_key\":\"FLWSECK_TEST4a47bb501118\"}', 'account_bank,account_number', 1, NULL, '2022-12-16 17:06:06'),
(9, 'global/gateway/coingate.png', 'CoinGate', 'coingate', '[\"BTC\", \"ETH\", \"LTC\", \"XRP\", \"BCH\", \"EOS\", \"XLM\", \"XMR\", \"DASH\", \"DOGE\", \"ZEC\"]', '{\"api_token\":\"NPfn5eAGjha_PqfQmC6F_rMA6_zaGVLmVk6Uvsfu\"}', '0', 1, NULL, '2022-11-22 16:01:12'),
(10, 'global/gateway/monnify.svg', 'Monnify', 'monnify', '[\"NGN\"]', '{\"api_key\":\"MK_TEST_BDQKAE9QNX\",\"api_secret\":\"4PSEUWJJ1HAKEYPXTT375CR9Y1LLX369\",\"base_url\":\"https:\\/\\/sandbox.monnify.com\",\"contract_code\":\"**********\"}', '0', 1, NULL, '2022-12-05 03:02:39'),
(11, 'global/gateway/securionpay.png', 'SecurionPay', 'securionpay', '[\"USD\", \"EUR\", \"GBP\", \"JPY\", \"CAD\", \"AUD\", \"CHF\", \"SEK\", \"NOK\", \"DKK\"]', '{\"public_key\":\"pk_test_7bkvbNaJzb1Eh2Egtp9NQ9Os\",\"secret_key\":\"sk_test_7bkvbSEKpHWR4bJL8qdoq7c9\"}', '0', 1, NULL, '2022-12-07 05:11:19'),
(12, 'global/gateway/coinpayments.svg', 'CoinPayments', 'coinpayments', '[\"BTC\", \"BTC.LN\", \"BCH\", \"LTC\", \"VLX\", \"VLX.Native\", \"APL\", \"ASK\", \"BCN\", \"BEAM\", \"BIZZ.TRC20\", \"BNB\", \"BNB.BSC\", \"BTCV\", \"BTG\", \"BTT.OLD\", \"BTT.TRC20\", \"CELO\", \"CLOAK\", \"CRW\", \"CURE\", \"cUSD\", \"USD\", \"CAD\", \"EUR\", \"ARS\", \"AUD\", \"AZN\", \"BGN\", \"BRL\", \"BYN\", \"CHF\", \"CLP\", \"CNY\", \"COP\", \"CZK\",\"DKK\", \"GBP\", \"GIP\", \"HKD\", \"HUF\", \"IDR\", \"ILS\", \"INR\", \"IRR\", \"IRT\", \"ISK\",\"JPY\",\"KRW\",\"LAK\",\"MKD\",\"MXN\",\"ZAR\",\"MYR\", \"NGN\", \"NOK\", \"NZD\", \"PEN\", \"PHP\", \"PKR\", \"PLN\", \"RON\", \"RUB\", \"SEK\", \"SGD\", \"THB\", \"TRY\", \"TWD\", \"UAH\", \"VND\"]', '{\"buyer_email\":\"<EMAIL>\",\"public_key\":\"07de16d3d2100a2440dc81b8659e1b1e607501ca1a5d57f5dfeeedcee361d7a2\",\"private_key\":\"077D15c2C8F915efbbb054a97A3dcc35a7c994e5F6111deb20f38D3AfF34eD8D\",\"ipn_secret\":\"@tdevs\",\"marchant_id\":\"26dd12941806e2c5e8ecc30460c8b7fc\"}', '0', 1, NULL, '2023-07-07 20:18:04'),
(13, 'global/gateway/nowpayments.png', 'Nowpayments', 'nowpayments', '[\"BTC\", \"ETH\", \"LTC\", \"BCH\", \"BNB\", \"XRP\", \"USDT\", \"DOGE\", \"ADA\", \"DOT\", \"LINK\", \"XLM\", \"USDC\", \"TRX\", \"ATOM\", \"XTZ\", \"EOS\", \"XMR\", \"ZEC\", \"DASH\"]', '{\"api_key\":\"SZTR71W-Z48MZ8G-Q5YH6X5-91F72XK\",\"secret_key\":\"cX7FhXcLYX\\/KJPqFnGgRKbcXDzyVUccN\"}', '0', 1, NULL, '2023-07-06 21:33:31'),
(14, 'global/gateway/coinremitter.png', 'Coinremitter', 'coinremitter', '[\"BTC\",\"ETH\"]', '{\"api_key\":\"$2y$10$mTa7SwWG8k1hJ\\/LP3PLTrOclnliQSrC99fsc528mBeTqBsNlj4MvK\",\"password\":\"12345678\"}', 'to_address', 1, NULL, '2023-07-06 21:33:31'),
(15, 'global/gateway/cryptomus.png', 'Cryptomus', 'cryptomus', '[\"BCH\",\"BNB\",\"BTC\",\"BUSD\",\"CGPT\",\"DAI\",\"DASH\",\"DOFE\",\"ETH\",\"LTC\",\"MATIC\",\"TON\",\"TRX\",\"USDC\",\"USDT\",\"VERSE\",\"XMR\"]\r\n\r\n', '{\"payment_key\":\"uQ4LFWCBE3dT84uQnt7ycL7p9WcSwjkSPQaZbik3ChoWO0egw51f4EAaZQKmefhPP0F1cX8OpRcl2c3HexNedoR7FGEYGA1mTgMPI8lzKl7Ct2I43R6SSC3gVDS3rkGX\",\"payout_key\":\"qseRhcxu6wsxhygfhyidwrrgryrrgefhPP0F1cNedoR7FGEYGA1mTgMPX8OpRcl2c3HexNedoR7FGEYGA1mTgMPI8lzKl7Ct2I43R6S1f4EAaZQKmefhSC3gVDS3rkGX\",\"merchant_id\":\"c26b80a8-9549-4a66-bb53-774f12809249\"}', 'address', 1, NULL, '2023-07-08 04:20:55'),
(16, 'global/gateway/paymongo.png', 'Paymongo', 'paymongo', '[\"PHP\", \"USD\", \"AUD\", \"CAD\", \"EUR\", \"HKD\", \"JPY\", \"SGD\", \"GBP\"]\n', '{\"public_key\":\"pk_test_4dV3PdbbGie3BffowbHVSKCU\",\"secret_key\":\"sk_test_xA6gsPKtgESQY1YMndhufaCo\",\"password\":\"5k0HVZ0^%33n\"}', '0', 1, NULL, '2023-07-10 22:48:12'),
(17, 'global/gateway/btcpayserver.png', 'Btcpayserver', 'btcpayserver', '[\"BTC\", \"ETH\", \"XRP\", \"BCH\", \"LTC\", \"ADA\", \"DOT\", \"LINK\", \"XLM\", \"DOGE\"]', '{\"host\":\"https:\\/\\/btcpay.greenway.management\",\"api_key\":\"94f975f1af98d75df559b9d67e13993929ed2e01\",\"store_id\":\"8kP1mwvGdFpFd55pYkuA8B6fEomnUUvGAa8Ad4n4z91M\",\"webhook_secret\":\"****************************\"}', '0', 1, NULL, '2023-07-06 21:33:31'),
(18, 'global/gateway/binance.svg', 'Binance', 'binance', '[\"BTC\", \"ETH\", \"BNB\", \"XRP\", \"LTC\", \"BCH\", \"ADA\", \"DOT\", \"LINK\",\"XLM\",\"USDT\",\"USDC\",\"DOGE\",\"SOL\",\"UNI\",\"MATIC\",\"ICP\", \"ETC\", \"XMR\", \"THETA\"]', '{\"api_key\":\"nmgmcaz7oaqijdefee8okmoghsrxrnxzctxzkm7nsgqwjhxe2i7hhyncsgihgxiu\",\"api_secret\":\"birh9qjpyyg4jtt9zsok2ozvpaq3cc9gb1wrtn8ojcnfas71ulg9cxm4su6kttbx\"}', 'address', 1, NULL, '2023-08-17 11:42:51'),
(19, 'global/gateway/cashmaal.png', 'Cashmaal', 'cashmaal', '[\"USD\", \"PKR\"]', '{\"web_id\":\"9101\",\"secret_key\":\"\"}', 'to_email', 1, NULL, '2023-08-19 12:47:56'),
(20, 'global/gateway/blockio.png', 'Block.io', 'blockio', '[\"BTC\", \"LTC\",\"DOGE\"]', '{\"pin\":\"169725cdd2800148b029f804f8f681f102c69b3c\",\"api_key\":\"d4a4-399b-8b74-c73f\"}', 'to_address', 1, NULL, '2023-09-02 08:06:34'),
(21, 'global/gateway/blockchain.png', 'Blockchain', 'blockchain', '[\"BTC\"]', '{\"api_key\":\"21\",\"xpub_code\":\"12\"}', '0', 1, NULL, '2023-08-28 22:30:54'),
(22, 'global/gateway/instamojo.png', 'Instamojo', 'instamojo', '[\"INR\"]', '{\"api_key\":\"test_2241633c3bc44a3de84a3b33969\",\"auth_token\":\"test_279f083f7bebefd35217feef22d\",\"salt\":\"19d38908eeff4f58b2ddda2c6d86ca25\"}', '0', 1, NULL, '2023-08-28 22:30:54'),
(23, 'global/gateway/paytm.png', 'Paytm', 'paytm', '[\"AUD\", \"ARS\", \"BDT\", \"BRL\", \"BGN\", \"CAD\", \"CLP\", \"CNY\", \"COP\",\r\n    \"HRK\", \"CZK\", \"DKK\", \"EGP\", \"EUR\", \"GEL\", \"GHS\", \"HKD\", \"HUF\",\r\n    \"INR\", \"IDR\", \"ILS\", \"JPY\", \"KES\", \"MYR\", \"MXN\", \"MAD\", \"NPR\",\r\n    \"NZD\", \"NGN\", \"NOK\", \"PKR\", \"PEN\", \"PHP\", \"PLN\", \"RON\", \"RUB\",\r\n    \"SGD\", \"ZAR\", \"KRW\", \"LKR\", \"SEK\", \"CHF\", \"THB\", \"TRY\", \"UGX\",\r\n    \"UAH\", \"AED\", \"GBP\", \"USD\", \"VND\", \"XOF\"]', '{\"merchant_id\":\"DIY12386817555501617\",\"merchant_key\":\"bKMfNxPPf_QdZppa\",\"merchant_website\":\"DIYtestingweb\",\"channel\":\"WEB\",\"industry_type\":\"Retail\"}', '0', 1, NULL, '2023-08-30 01:47:57'),
(24, 'global/gateway/razorpay.png', 'Razorpay', 'razorpay', '[\"INR\"]', '{\"razorpay_key\":\"***********************\",\"razorpay_secret\":\"UBV0t5iSUUoU3rRccAZKnhfP\"}', '0', 1, NULL, '2024-02-07 03:38:46'),
(25, 'global/gateway/twocheckout.png', '2Checkout', 'twocheckout', '[\"AFN\", \"ALL\", \"DZD\", \"ARS\", \"AUD\", \"AZN\", \"BSD\", \"BDT\", \"BBD\", \"BZD\", \r\n    \"BMD\", \"BOB\", \"BWP\", \"BRL\", \"GBP\", \"BND\", \"BGN\", \"CAD\", \"CLP\", \"CNY\", \r\n    \"COP\", \"CRC\", \"HRK\", \"CZK\", \"DKK\", \"DOP\", \"XCD\", \"EGP\", \"EUR\", \"FJD\", \r\n    \"GTQ\", \"HKD\", \"HNL\", \"HUF\", \"INR\", \"IDR\", \"ILS\", \"JMD\", \"JPY\", \"KZT\", \r\n    \"KES\", \"LAK\", \"MMK\", \"LBP\", \"LRD\", \"MOP\", \"MYR\", \"MVR\", \"MRO\", \"MUR\", \r\n    \"MXN\", \"MAD\", \"NPR\", \"TWD\", \"NZD\", \"NIO\", \"NOK\", \"PKR\", \"PGK\", \"PEN\", \r\n    \"PHP\", \"PLN\", \"QAR\", \"RON\", \"RUB\", \"WST\", \"SAR\", \"SCR\", \"SGD\", \"SBD\", \r\n    \"ZAR\", \"KRW\", \"LKR\", \"SEK\", \"CHF\", \"SYP\", \"THB\", \"TOP\", \"TTD\", \"TRY\", \r\n    \"UAH\", \"AED\", \"USD\", \"VUV\", \"VND\", \"XOF\", \"YER\"]', '{\"seller_id\":\"250507228545\",\"secret_word\":\"=+0CNzfvTItqp*ygwiQE\"}', '0', 1, NULL, '2023-08-30 08:13:27');

-- --------------------------------------------------------

--
-- Table structure for table `holidays`
--

CREATE TABLE `holidays` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `kycs`
--

CREATE TABLE `kycs` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fields` json DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `landing_contents`
--

CREATE TABLE `landing_contents` (
  `id` bigint UNSIGNED NOT NULL,
  `theme` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `photo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `locale_id` int DEFAULT NULL,
  `locale` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `landing_contents`
--

INSERT INTO `landing_contents` (`id`, `theme`, `icon`, `title`, `description`, `photo`, `type`, `locale_id`, `locale`, `created_at`, `updated_at`) VALUES
(64, 'default', 'global/uploads/images/kOkZNxWW4ZRUO00zhwh2.webp', 'User Panel', 'Access your user panel to manage account settings', NULL, 'features', 64, 'en', '2025-04-08 08:37:39', '2025-04-24 09:54:31'),
(65, 'default', 'global/uploads/images/eTB5dpf8rOAIF3itS6qO.webp', 'Add Money', 'Access your personal user panel to manage account settings', NULL, 'features', 65, 'en', '2025-04-08 08:40:38', '2025-04-24 09:54:31'),
(66, 'default', 'global/uploads/images/8xz6P2rFSgcwq6Nh41vY.webp', 'Send Money', 'Access your personal user panel to manage account settings', NULL, 'features', 66, 'en', '2025-04-08 08:41:04', '2025-04-24 09:54:31'),
(67, 'default', 'global/uploads/images/JNOMrcYVP52XMzbgEHEk.webp', 'Exchange', 'Access your personal user panel to manage account settings', NULL, 'features', 67, 'en', '2025-04-08 08:41:21', '2025-04-24 09:54:31'),
(68, 'default', 'global/uploads/images/NcCTma3FicU3JuKYbKYh.webp', 'Request Money', 'Access your personal user panel to manage account settings', NULL, 'features', 68, 'en', '2025-04-08 08:41:48', '2025-04-24 09:54:31'),
(69, 'default', 'global/uploads/images/iXGuvH0vpgPYgLBCAnWc.webp', 'User To User', 'Access your personal user panel to manage account settings', NULL, 'features', 69, 'en', '2025-04-08 08:42:40', '2025-04-24 09:54:31'),
(70, 'default', 'global/uploads/images/Ah7jDHITfqqDi5Je4JpL.webp', 'Withdraw', 'Access your personal user panel to manage account settings', NULL, 'features', 70, 'en', '2025-04-08 08:42:52', '2025-04-24 09:54:31'),
(71, 'default', 'global/uploads/images/h7UcoXINwouAeuassGMP.webp', 'Referral', 'Access your personal user panel to manage account settings', NULL, 'features', 71, 'en', '2025-04-08 08:43:06', '2025-04-24 09:54:31'),
(73, 'default', 'global/uploads/images/UKbrLOHBz9sd8tM9ult0.webp', 'Affiliate System', 'Printing and typesetting. Lorem has been the standard.', NULL, 'about', 73, 'en', '2025-04-08 10:27:59', '2025-04-28 05:56:13'),
(74, 'default', 'global/uploads/images/uQhNABJIErhCKtCjBMuK.webp', 'Agent & Merchant', 'Printing and typesetting. Lorem has been the standard.', NULL, 'about', 74, 'en', '2025-04-08 10:28:13', '2025-04-28 05:55:33'),
(75, 'default', 'global/uploads/images/VGASg8ue9bc8qHxEqBAu.webp', 'Trusted platform', 'Printing and typesetting. Lorem has been the standard.', NULL, 'about', 75, 'en', '2025-04-08 10:28:27', '2025-04-24 09:54:31'),
(80, 'default', NULL, 'What is Cryptocurrency?', 'Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technolog', NULL, 'faqs', 80, 'en', '2025-04-09 04:18:58', '2025-04-24 09:54:31'),
(81, 'default', NULL, 'How can I track my order?', 'Cryptocurrency is a digital or virtual currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technology', NULL, 'faqs', 81, 'en', '2025-04-09 04:19:33', '2025-04-24 09:54:31'),
(82, 'default', NULL, 'I received the wrong item, what do I do?', 'Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technology', NULL, 'faqs', 82, 'en', '2025-04-09 04:19:55', '2025-04-24 09:54:31'),
(83, 'default', NULL, 'How promote the product?', 'Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technolog', NULL, 'faqs', 83, 'en', '2025-04-09 04:20:18', '2025-04-24 09:54:31'),
(84, 'default', 'global/uploads/images/vm4drlbJ5f8ukzxvwJKP.png', 'Multi-Currency Support', 'Accept payments in various cryptocurrencies', NULL, 'agent', 59, 'en', '2025-03-25 08:25:04', '2025-04-24 09:54:31'),
(85, 'default', 'global/uploads/images/xNDHdpJnaZOOAhsTvKoK.png', 'API & Plugins', 'Easy integration with major e-commerce platforms', NULL, 'agent', 61, 'en', '2025-03-25 08:25:25', '2025-04-24 09:54:31'),
(86, 'default', 'global/uploads/images/gtKZKJvQrrieXtuxrCB9.png', 'Custom Payment Links', 'Generate links for direct payments', NULL, 'agent', 62, 'en', '2025-03-25 08:25:48', '2025-04-24 09:54:31'),
(87, 'default', 'global/uploads/images/nPf1LVsXg9ieM1cAzSzi.png', 'Security & Compliance', 'Fraud detection and encryption', NULL, 'agent', 63, 'en', '2025-03-25 08:26:08', '2025-04-24 09:54:31'),
(88, 'default', 'global/uploads/images/q2Q2UwlOrv07rL9RLOO8.png', 'Multi-Currency Support', 'Accept payments in various cryptocurrencies', NULL, 'merchant', 29, 'en', '2025-03-25 05:37:23', '2025-04-24 09:54:31'),
(89, 'default', 'global/uploads/images/9w67M1zoGHZIOH1Sxcc5.png', 'API & Plugins', 'Easy integration with major e-commerce platforms', NULL, 'merchant', 55, 'en', '2025-03-25 05:38:01', '2025-04-24 09:54:31'),
(90, 'default', 'global/uploads/images/LwCXr7uyhbY24PBGZ3R3.png', 'Custom Payment Links', 'Generate links for direct payments', NULL, 'merchant', 56, 'en', '2025-03-25 05:38:20', '2025-04-24 09:54:31'),
(91, 'default', 'global/uploads/images/rKBGYqacBf4kDxHnePdb.png', 'Instant Payouts', 'Withdraw funds in real-time', NULL, 'merchant', 57, 'en', '2025-03-25 05:38:35', '2025-04-24 09:54:31'),
(92, 'default', 'global/uploads/images/oSNYjrvPkIkZpPmz6WJM.png', 'Security & Compliance', 'Fraud detection and encryption', NULL, 'merchant', 58, 'en', '2025-03-25 05:38:50', '2025-04-24 09:54:31'),
(101, 'default', 'global/uploads/images/XosUFvm6asjsXa45lu7y.png', 'Register Account', 'By Registering the website you will able to start your operation', NULL, 'howitworks', 101, 'en', '2025-04-28 05:36:07', '2025-04-28 05:36:07'),
(102, 'default', 'global/uploads/images/cuTgwRUhXtZEaUiN1WQb.png', 'Verify Email', 'After creating the account user need to verify the email for account purpose', NULL, 'howitworks', 102, 'en', '2025-04-28 05:36:20', '2025-04-28 05:36:20'),
(103, 'default', 'global/uploads/images/UMjprpq2WKD17WxSJSq4.png', 'Verify KYC', 'Users\' KYC needs to verify to before making any withdrawals', NULL, 'howitworks', 103, 'en', '2025-04-28 05:36:34', '2025-04-28 05:36:34'),
(104, 'default', 'global/uploads/images/tIski8sScfXarKXARa5V.png', 'Deposit Money', 'Users can deposit using any automatic or manual gateways', NULL, 'howitworks', 104, 'en', '2025-04-28 05:37:10', '2025-04-28 05:37:10'),
(105, 'default', 'global/uploads/images/MYPgwN0YLPk5smvXW0UE.png', 'Merchants & Agents', 'User can pay to the merchant and also cashout money from agent.', NULL, 'howitworks', 105, 'en', '2025-04-28 05:37:56', '2025-04-30 16:40:29'),
(106, 'default', 'global/uploads/images/nNWtj0GYodUU3kh9wYb1.png', 'Transfer, Gift, Invoice', 'The user can transfer money to another user. Also, gift creation for other users and redeeming the gift. Also, create an invoice and pay.', NULL, 'howitworks', 106, 'en', '2025-04-28 05:39:32', '2025-04-28 05:39:32'),
(107, 'default', 'global/uploads/images/KvzkrRuf32hqfaE7Jsgh.png', 'Refer To Friends', 'User can refer their friends and get a referral bonus.', NULL, 'howitworks', 107, 'en', '2025-04-28 05:40:40', '2025-04-28 05:40:40'),
(108, 'default', 'global/uploads/images/jZXxwNV06fFDukHUfLUn.png', 'Withdraw & Cash Out', 'User can withdraw their money or cash out from the agent.', NULL, 'howitworks', 108, 'en', '2025-04-28 05:41:30', '2025-04-28 05:41:30'),
(111, 'default', 'global/uploads/images/Sr8CPPBCuyy1cyCZFaFd.webp', 'Secure Transaction', 'Printing and typesetting. Lorem has been the standard.', NULL, 'about', 109, 'en', '2025-04-30 16:45:59', '2025-04-30 16:45:59'),
(116, 'default', NULL, 'Nostrud architecto a', 'Ipsam quas non volup', NULL, 'feature', 112, 'en', '2025-05-22 09:00:17', '2025-05-22 09:00:17'),
(117, 'default', NULL, 'Iste consequuntur la', 'Et molestias commodi', NULL, 'feature', 117, 'en', '2025-05-22 09:00:26', '2025-05-22 09:00:26'),
(119, 'default', NULL, 'sdf', 'sdf', NULL, 'howitworks', 119, 'en', '2025-05-22 09:31:26', '2025-05-22 09:31:26'),
(121, 'default', 'global/uploads/images/fArKjsQVEW7ySGq0HWTm.png', 'Our Mining [[color_text= Solutions ]]', 'Our Mining [[color_text= Solutions ]]', NULL, 'solutions', 121, 'en', '2025-05-22 09:46:40', '2025-05-22 09:46:40'),
(123, 'default', 'global/uploads/images/kOkZNxWW4ZRUO00zhwh2.webp', 'User Panel', 'Access your user panel to manage account settings', NULL, 'features', 64, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(124, 'default', 'global/uploads/images/eTB5dpf8rOAIF3itS6qO.webp', 'Add Money', 'Access your personal user panel to manage account settings', NULL, 'features', 65, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(125, 'default', 'global/uploads/images/8xz6P2rFSgcwq6Nh41vY.webp', 'Send Money', 'Access your personal user panel to manage account settings', NULL, 'features', 66, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(126, 'default', 'global/uploads/images/JNOMrcYVP52XMzbgEHEk.webp', 'Exchange', 'Access your personal user panel to manage account settings', NULL, 'features', 67, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(127, 'default', 'global/uploads/images/NcCTma3FicU3JuKYbKYh.webp', 'Request Money', 'Access your personal user panel to manage account settings', NULL, 'features', 68, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(128, 'default', 'global/uploads/images/iXGuvH0vpgPYgLBCAnWc.webp', 'User To User', 'Access your personal user panel to manage account settings', NULL, 'features', 69, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(129, 'default', 'global/uploads/images/Ah7jDHITfqqDi5Je4JpL.webp', 'Withdraw', 'Access your personal user panel to manage account settings', NULL, 'features', 70, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(130, 'default', 'global/uploads/images/h7UcoXINwouAeuassGMP.webp', 'Referral', 'Access your personal user panel to manage account settings', NULL, 'features', 71, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(131, 'default', 'global/uploads/images/UKbrLOHBz9sd8tM9ult0.webp', 'Affiliate System', 'Printing and typesetting. Lorem has been the standard.', NULL, 'about', 73, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(132, 'default', 'global/uploads/images/uQhNABJIErhCKtCjBMuK.webp', 'Agent & Merchant', 'Printing and typesetting. Lorem has been the standard.', NULL, 'about', 74, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(133, 'default', 'global/uploads/images/VGASg8ue9bc8qHxEqBAu.webp', 'Trusted platform', 'Printing and typesetting. Lorem has been the standard.', NULL, 'about', 75, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(134, 'default', NULL, 'ما هي العملة المشفرة؟', 'العملات المشفرة هي شكل رقمي أو افتراضي من العملات يستخدم التشفير لأغراض أمنية. على عكس العملات التقليدية التي تصدرها الحكومات (مثل الدولار الأمريكي أو اليورو)، تعمل العملات المشفرة على شبكات لامركزية تعتمد على تقنية البلوك تشين.', NULL, 'faqs', 80, 'ar', '2025-05-24 09:07:10', '2025-06-02 08:31:18'),
(135, 'default', NULL, 'كيف يمكنني تتبع طلبي؟', 'العملات المشفرة هي عملة رقمية أو افتراضية تستخدم التشفير لأغراض أمنية. على عكس العملات التقليدية التي تصدرها الحكومات (مثل الدولار الأمريكي أو اليورو)، تعمل العملات المشفرة على شبكات لامركزية تعتمد على تقنية البلوك تشين.', NULL, 'faqs', 81, 'ar', '2025-05-24 09:07:10', '2025-06-02 08:31:34'),
(136, 'default', NULL, 'لقد تلقيت العنصر الخطأ، ماذا أفعل؟', 'العملات المشفرة هي شكل رقمي أو افتراضي من العملات يستخدم التشفير لأغراض أمنية. على عكس العملات التقليدية التي تصدرها الحكومات (مثل الدولار الأمريكي أو اليورو)، تعمل العملات المشفرة على شبكات لامركزية تعتمد على تقنية البلوك تشين.', NULL, 'faqs', 82, 'ar', '2025-05-24 09:07:10', '2025-06-02 08:31:49'),
(137, 'default', NULL, 'How promote the product?', 'Cryptocurrency is a digital or virtual form of currency that uses cryptography for security. Unlike traditional currencies issued by governments (like the US dollar or euro), cryptocurrencies operate on decentralized networks based on blockchain technolog', NULL, 'faqs', 83, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(138, 'default', 'global/uploads/images/vm4drlbJ5f8ukzxvwJKP.png', 'Multi-Currency Support', 'Accept payments in various cryptocurrencies', NULL, 'agent', 59, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(139, 'default', 'global/uploads/images/xNDHdpJnaZOOAhsTvKoK.png', 'API & Plugins', 'Easy integration with major e-commerce platforms', NULL, 'agent', 61, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(140, 'default', 'global/uploads/images/gtKZKJvQrrieXtuxrCB9.png', 'Custom Payment Links', 'Generate links for direct payments', NULL, 'agent', 62, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(141, 'default', 'global/uploads/images/nPf1LVsXg9ieM1cAzSzi.png', 'Security & Compliance', 'Fraud detection and encryption', NULL, 'agent', 63, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(142, 'default', 'global/uploads/images/q2Q2UwlOrv07rL9RLOO8.png', 'Multi-Currency Support', 'Accept payments in various cryptocurrencies', NULL, 'merchant', 29, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(143, 'default', 'global/uploads/images/9w67M1zoGHZIOH1Sxcc5.png', 'API & Plugins', 'Easy integration with major e-commerce platforms', NULL, 'merchant', 55, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(144, 'default', 'global/uploads/images/LwCXr7uyhbY24PBGZ3R3.png', 'Custom Payment Links', 'Generate links for direct payments', NULL, 'merchant', 56, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(145, 'default', 'global/uploads/images/rKBGYqacBf4kDxHnePdb.png', 'Instant Payouts', 'Withdraw funds in real-time', NULL, 'merchant', 57, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(146, 'default', 'global/uploads/images/oSNYjrvPkIkZpPmz6WJM.png', 'Security & Compliance', 'Fraud detection and encryption', NULL, 'merchant', 58, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(147, 'default', 'global/uploads/images/XosUFvm6asjsXa45lu7y.png', 'Register Account', 'By Registering the website you will able to start your operation', NULL, 'howitworks', 101, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(148, 'default', 'global/uploads/images/cuTgwRUhXtZEaUiN1WQb.png', 'Verify Email', 'After creating the account user need to verify the email for account purpose', NULL, 'howitworks', 102, 'ar', '2025-05-24 09:07:10', '2025-05-24 09:07:10'),
(149, 'default', 'global/uploads/images/UMjprpq2WKD17WxSJSq4.png', 'Verify KYC', 'Users\' KYC needs to verify to before making any withdrawals', NULL, 'howitworks', 103, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(150, 'default', 'global/uploads/images/tIski8sScfXarKXARa5V.png', 'Deposit Money', 'Users can deposit using any automatic or manual gateways', NULL, 'howitworks', 104, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(151, 'default', 'global/uploads/images/MYPgwN0YLPk5smvXW0UE.png', 'Merchants & Agents', 'User can pay to the merchant and also cashout money from agent.', NULL, 'howitworks', 105, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(152, 'default', 'global/uploads/images/nNWtj0GYodUU3kh9wYb1.png', 'Transfer, Gift, Invoice', 'The user can transfer money to another user. Also, gift creation for other users and redeeming the gift. Also, create an invoice and pay.', NULL, 'howitworks', 106, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(153, 'default', 'global/uploads/images/KvzkrRuf32hqfaE7Jsgh.png', 'Refer To Friends', 'User can refer their friends and get a referral bonus.', NULL, 'howitworks', 107, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(154, 'default', 'global/uploads/images/jZXxwNV06fFDukHUfLUn.png', 'Withdraw & Cash Out', 'User can withdraw their money or cash out from the agent.', NULL, 'howitworks', 108, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(155, 'default', 'global/uploads/images/Sr8CPPBCuyy1cyCZFaFd.webp', 'Secure Transaction', 'Printing and typesetting. Lorem has been the standard.', NULL, 'about', 109, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(156, 'default', NULL, 'Nostrud architecto a', 'Ipsam quas non volup', NULL, 'feature', 112, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(157, 'default', NULL, 'Iste consequuntur la', 'Et molestias commodi', NULL, 'feature', 117, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(158, 'default', NULL, 'sdf', 'sdf', NULL, 'howitworks', 119, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(160, 'default', 'global/uploads/images/fArKjsQVEW7ySGq0HWTm.png', 'Our Mining [[color_text= Solutions ]]', 'Our Mining [[color_text= Solutions ]]', NULL, 'solutions', 121, 'ar', '2025-05-24 09:07:11', '2025-05-24 09:07:11'),
(163, 'default', 'global/uploads/images/cI9OJAqan52IBRJfZSIY.png', 'Exploration & Extraction', 'We are ready to answer all your questions and advise you 24/7.', NULL, 'mining-solutions', 163, 'en', '2025-05-27 04:54:25', '2025-05-27 04:54:25'),
(164, 'default', 'global/uploads/images/UDUE3B3c9FrZdlcSpUq1.png', 'Drilling & Blasting', 'We are ready to answer all your questions and advise you 24/7.', NULL, 'mining-solutions', 164, 'en', '2025-05-27 04:54:40', '2025-06-02 08:35:56'),
(165, 'default', 'global/uploads/images/G2XLFuuWK5ZUqAfTRCAD.png', 'Mineral Processing', 'We are ready to answer all your questions and advise you 24/7.', NULL, 'mining-solutions', 165, 'en', '2025-05-27 04:54:55', '2025-05-27 04:54:55'),
(166, 'default', 'global/uploads/images/3Yy7Az2vqTtTLxksA61T.png', 'Environmental Sustainability', 'We are ready to answer all your questions and advise you 24/7.', NULL, 'mining-solutions', 166, 'en', '2025-05-27 04:55:07', '2025-06-02 08:37:06'),
(167, 'default', 'global/uploads/images/gmm645zu83Pmm4gpcB5Y.png', 'Advanced Technology', 'We are ready to answer all your questions and advise you', NULL, 'why-work-with-us', 167, 'en', '2025-05-27 05:18:23', '2025-05-27 05:18:23'),
(181, 'default', NULL, 'Set up your account', 'Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!', NULL, 'how-it-operate', 170, 'en', '2025-05-30 12:05:50', '2025-05-30 12:05:50'),
(182, 'default', NULL, 'Mining-Focused', 'Launch your cryptocurrency mining operation in minutes! Choose from our powerful mining schemes.', NULL, 'how-it-operate', 182, 'en', '2025-05-30 12:05:57', '2025-06-03 09:58:45'),
(183, 'default', NULL, 'Investment-Focused', 'Invest Smart, Mine Smarter\r\nTransform your investment into a passive income powerhouse!', NULL, 'how-it-operate', 183, 'en', '2025-05-30 12:06:06', '2025-06-03 09:59:24'),
(184, 'default', NULL, 'Technology-Focused', 'Experience next-generation cloud mining without the hardware hassle!', NULL, 'how-it-operate', 184, 'en', '2025-05-30 12:06:12', '2025-06-03 09:59:50'),
(185, 'default', NULL, 'إعداد حسابك', 'احصل على مكافآت ضخمة بنسبة 324% وقم بزيادة مكاسبك من العملات المشفرة إلى أقصى حد باستخدام نظام المراهنة المبتكر لدينا!', NULL, 'how-it-operate', 170, 'ar', '2025-05-30 12:05:50', '2025-06-02 08:16:42'),
(186, 'default', NULL, 'إعداد حسابك', 'احصل على مكافآت ضخمة بنسبة 324% وقم بزيادة مكاسبك من العملات المشفرة إلى أقصى حد باستخدام نظام المراهنة المبتكر لدينا!', NULL, 'how-it-operate', 182, 'ar', '2025-05-30 12:05:57', '2025-06-03 09:58:45'),
(187, 'default', NULL, 'إعداد حسابك', 'احصل على مكافآت ضخمة بنسبة 324% وقم بزيادة مكاسبك من العملات المشفرة إلى أقصى حد باستخدام نظام المراهنة المبتكر لدينا!', NULL, 'how-it-operate', 183, 'ar', '2025-05-30 12:06:06', '2025-06-03 09:59:24'),
(188, 'default', NULL, 'إعداد حسابك', 'احصل على مكافآت ضخمة بنسبة 324% وقم بزيادة مكاسبك من العملات المشفرة إلى أقصى حد باستخدام نظام المراهنة المبتكر لدينا!', NULL, 'how-it-operate', 184, 'ar', '2025-05-30 12:06:12', '2025-06-03 09:59:50'),
(189, 'default', 'global/uploads/images/gmm645zu83Pmm4gpcB5Y.png', 'التكنولوجيا المتقدمة', 'نحن على استعداد للإجابة على جميع أسئلتكم وتقديم المشورة لكم', NULL, 'why-work-with-us', 167, 'ar', '2025-05-27 05:18:23', '2025-06-02 08:30:12'),
(192, 'default', 'global/uploads/images/cI9OJAqan52IBRJfZSIY.png', 'الاستكشاف والاستخراج', 'نحن على استعداد للإجابة على جميع أسئلتك وتقديم المشورة لك على مدار الساعة طوال أيام الأسبوع.', NULL, 'mining-solutions', 163, 'ar', '2025-05-27 04:54:25', '2025-06-02 08:35:31'),
(193, 'default', 'global/uploads/images/UDUE3B3c9FrZdlcSpUq1.png', 'الحفر والتفجير', 'نحن على استعداد للإجابة على جميع أسئلتك وتقديم المشورة لك على مدار الساعة طوال أيام الأسبوع.', NULL, 'mining-solutions', 164, 'ar', '2025-05-27 04:54:40', '2025-06-02 08:36:09'),
(194, 'default', 'global/uploads/images/G2XLFuuWK5ZUqAfTRCAD.png', 'معالجة المعادن', 'نحن على استعداد للإجابة على جميع أسئلتك وتقديم المشورة لك على مدار الساعة طوال أيام الأسبوع.', NULL, 'mining-solutions', 165, 'ar', '2025-05-27 04:54:55', '2025-06-02 08:36:33'),
(195, 'default', 'global/uploads/images/3Yy7Az2vqTtTLxksA61T.png', 'الاستدامة البيئية', 'نحن على استعداد للإجابة على جميع أسئلتك وتقديم المشورة لك على مدار الساعة طوال أيام الأسبوع.', NULL, 'mining-solutions', 166, 'ar', '2025-05-27 04:55:07', '2025-06-02 08:37:06'),
(196, 'default', 'global/uploads/images/wL51xAEwQmK5BU4L6lxd.png', 'High Efficiency', 'We are ready to answer all your questions and advise you', NULL, 'why-work-with-us', 196, 'en', '2025-06-03 09:22:07', '2025-06-03 09:22:07'),
(197, 'default', 'global/uploads/images/6OeD4zScCUNG02uWhYp1.png', 'Experienced Team', 'We are ready to answer all your questions and advise you', NULL, 'why-work-with-us', 197, 'en', '2025-06-03 09:22:28', '2025-06-03 09:22:28'),
(198, 'default', 'global/uploads/images/sCE2vpOkHUeMxcqFT0zb.png', 'Sustainable Practices', 'We are ready to answer all your questions and advise you', NULL, 'why-work-with-us', 198, 'en', '2025-06-03 09:23:01', '2025-06-03 09:23:01'),
(199, 'default', 'global/uploads/images/gOO9XsbSqnEMPLKnmxFJ.png', 'Logistics & Transportation', 'We are ready to answer all your questions and advise you 24/7.', NULL, 'mining-solutions', 199, 'en', '2025-06-03 09:40:53', '2025-06-03 09:40:53'),
(200, 'default', 'global/uploads/images/RgJ7EFW4eSgMIbU3msWF.png', 'Mine Safety & Compliance', 'We are ready to answer all your questions and advise you 24/7.', NULL, 'mining-solutions', 200, 'en', '2025-06-03 09:41:04', '2025-06-03 09:41:04'),
(201, 'default', 'global/uploads/images/ltiR2XkS0z2jPHpPAKeP.png', '95% Waste Recycled', 'We are a leading mining company committed to the responsible.', NULL, 'mining-responsibly-about-us-page', 201, 'en', '2025-06-03 11:22:38', '2025-06-03 11:22:38'),
(202, 'default', 'global/uploads/images/UV6yQ3T0SKZBafSHiN4B.png', '95% Waste Recycled', 'We are a leading mining company committed to the responsible.', NULL, 'mining-responsibly-about-us-page', 202, 'en', '2025-06-03 11:22:58', '2025-06-03 11:22:58'),
(203, 'default', 'global/uploads/images/FuVhzMCSlOEzDqsAdheI.png', '95% Waste Recycled', 'We are a leading mining company committed to the responsible.', NULL, 'mining-responsibly-about-us-page', 203, 'en', '2025-06-03 11:23:16', '2025-06-03 11:23:16');

-- --------------------------------------------------------

--
-- Table structure for table `landing_pages`
--

CREATE TABLE `landing_pages` (
  `id` bigint UNSIGNED NOT NULL,
  `theme` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `status` tinyint DEFAULT NULL,
  `sort` int DEFAULT NULL,
  `locale` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'en',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `landing_pages`
--

INSERT INTO `landing_pages` (`id`, `theme`, `name`, `code`, `data`, `status`, `sort`, `locale`, `created_at`, `updated_at`) VALUES
(13, 'default', 'Hero Section', 'hero', '{\"hero_title\":\"Powering Your Profits with Next-Gen [[color_text= Mining ]]\",\"hero_description\":\"Innovative Mining Solutions for a Sustainable Future.Innovative Mining Solutions for a Sustainable Future.Innovativ\",\"bonus_text\":\"Sign Up now & get up to $5000\",\"bubble_text\":\"Our client satisfy\",\"bubble_counter\":\"80+\",\"user_photos\":\"[\\\"global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/9Ss9it1vd3siHXWpAavF.webp\\\",\\\"global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/VPte0717hGisVP6kuYjB.webp\\\",\\\"global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/GJChfQzQtoqC4vXXgMrJ.webp\\\",\\\"global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/fdE6x693e32c7dIqsdcu.webp\\\",\\\"global\\\\\\/uploads\\\\\\/landing-contents\\\\\\/user_photos\\\\\\/LNELt5577nubXEmHNqiT.webp\\\"]\",\"hero_subtitle\":\"Sign Up now & get up to $5,000\",\"image\":\"global\\/uploads\\/landing-contents\\/image\\/whyFsUktRePHqgp7GM7e.webp\",\"button_label\":\"Start Mining\",\"button_url\":\"\\/\",\"button_target\":\"_self\",\"right_button_label\":\"Explore Plans\",\"right_button_url\":\"\\/register\",\"right_button_target\":\"_self\"}', 1, 1, 'en', NULL, '2025-06-02 09:06:30'),
(14, 'default', 'How It Operate Section', 'how-it-operate', '{\"title\":\"How does [[color_text= Orixcoin ]] operate\",\"subtitle\":\"Choose the right plan for efficient and cost-effective mining operations.\"}', 1, 2, 'en', NULL, '2025-06-03 09:56:26'),
(16, 'default', 'Scheme Section', 'scheme', '{\"title\":\"How It [[color_text= Works]]\",\"subtitle\":\"Discover how it all works\",\"hero_title\":\"[[color_text= Flexible Mining]] Solutions\",\"sub_title\":\"Choose the right plan for efficient and cost-effective mining operations.\",\"hero_description\":\"We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.\",\"cta_title\":\"324 % staking rewards\",\"cta_description\":\"Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!\",\"button_label\":\"Learn more\",\"button_url\":\"\\/\",\"button_target\":\"_self\",\"image\":\"global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png\"}', 1, 4, 'en', '2025-05-24 03:07:11', '2025-06-02 08:25:01'),
(17, 'default', 'Payment Record Section', 'payment-record', '{\"title\":\"How It [[color_text= Works]]\",\"subtitle\":\"Discover how it all works\",\"hero_title\":\"[[color_text= Payment ]] Records\",\"sub_title\":\"Track all your mining transactions with complete transparency and security.\",\"hero_description\":\"We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.\",\"cta_title\":\"324 % staking rewards\",\"cta_description\":\"Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!\",\"button_label\":\"Learn more\",\"button_url\":\"\\/\",\"button_target\":\"_self\",\"image\":\"global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png\"}', 1, 5, 'en', '2025-05-24 03:07:11', '2025-06-02 08:24:25'),
(18, 'default', 'Mining Solutions Section', 'mining-solutions', '{\"solutions_title\":\"Our Mining [[color_text= Solutions ]]\",\"solutions_subtitle\":\"A crypto wallet system offers secure storage, transaction management, private key protection, multi-currency support, and seamless integration\"}', 1, 6, 'en', NULL, '2025-06-02 05:52:04'),
(19, 'default', 'Future Of Mining Section', 'feature-of-mining', '{\"title\":\"How It [[color_text= Works]]\",\"subtitle\":\"Discover how it all works\",\"hero_title\":\"[[color_text= Flexible Mining]] Solutions Tailored to Your Needs\",\"sub_title\":\"Choose the right plan for efficient and cost-effective mining operations.\",\"hero_description\":\"We are a leading mining company dedicated to responsible resource extraction, using cutting-edge technology and sustainable practices. With years of experience in mineral exploration, extraction, and processing, we provide high-quality raw materials that power industries worldwide.\",\"cta_title\":\"324 % staking rewards\",\"cta_description\":\"Earn massive 324% staking rewards and maximize your crypto gains with our game-changing staking system!\",\"button_label\":\"Learn more\",\"button_url\":\"\\/\",\"button_target\":\"_self\",\"image\":\"global\\/uploads\\/landing-contents\\/image\\/t7CerDCTAqXsYnWhawH2.png\"}', 1, 7, 'en', NULL, '2025-06-02 05:52:04'),
(20, 'default', 'Supported Currencies Section', 'supported-currencies', '{\"hero_title\":\"View and Manage your Assets Across all Supported [[color_text= Currencies ]]\",\"button_label\":\"Start Mining\",\"button_url\":\"\\/\",\"button_target\":\"_self\",\"image\":\"global\\/uploads\\/landing-contents\\/image\\/xivYoTKThIXOmeCeqgd1.png\",\"logo\":\"global\\/uploads\\/landing-contents\\/logo\\/N6p4FlLeQDjw2iCPllik.svg\"}', 1, 8, 'en', NULL, '2025-06-04 09:56:05'),
(21, 'default', 'Why Work With Us', 'why-work-with-us', '{\"features_title\":\"[[color_text= Why Work]] With Us\",\"features_subtitle\":\"A crypto wallet system offers secure storage, transaction management, private key protection, multi-currency support, and seamless integration\",\"button_label\":\"Read more\",\"button_url\":\"\\/\",\"button_target\":\"_self\"}', 1, 9, 'en', NULL, '2025-06-02 05:52:04'),
(22, 'default', 'FAQ Section', 'faqs', '{\"faq_title\":\"Frequently Asked [[color_text=Questions]]\",\"faq_subtitle\":\"Frequently asked questions cover topics like wallet setup, security features, transaction processes, private key management, and multi-currency compatibility within the crypto wallet system.\",\"image\":\"global\\/uploads\\/landing-contents\\/image\\/DI3ZPukmzjjbejgPxuuL.png\"}', 1, 10, 'en', NULL, '2025-06-02 05:52:04'),
(23, 'default', 'Testimonial Section', 'testimonial', '{\"testimonial_title\":\"What Our [[color_text=Client  Say]]\",\"sub_title\":\"Clients praise the crypto wallet system for its ease of use, robust security, seamless transactions, and reliable multi-currency support.\"}', 1, 11, 'en', '2025-03-20 02:07:55', '2025-06-03 09:38:16'),
(24, 'default', 'Blog Section', 'blog', '{\"blog_title\":\"Our [[color_text=Blogs]]\",\"blog_subtitle\":\"It is long established fact that a reader will be Distracted\"}', 1, 12, 'en', '2025-03-20 02:07:55', '2025-06-02 05:52:04'),
(26, 'default', 'Footer Section\r\n', 'footer', '{\"copyright_text\":\"OreXcoin\\u00a9 2025 . All rights reserved.\",\"widget_title_1\":\"Quick Links\",\"widget_title_2\":\"Useful Links\",\"widget_title_3\":\"Contact\",\"newsletter_description\":\"Never miss anything crypto when you\\u2019re on go\",\"left_description\":\"At OreXcoin, we\\u2019re reshaping the future of crypto mining. Built with cutting-edge technology\",\"phone\":\"\",\"email\":\"<EMAIL>\",\"location\":\"Ko\\u0144cowa 72, Szczecin 70-871\",\"first_contact_label\":\"Call\",\"first_contact_link_label\":\"(+33)7 75 55 9375\",\"first_contact_link\":\"tel:+337 75 55 9375\",\"second_contact_label\":\"Email\",\"second_contact_link_label\":\"<EMAIL>\",\"second_contact_link\":\"mailto:<EMAIL>\",\"third_contact_label\":\"Location\",\"third_contact_link_label\":\"ul. Ko\\u0144cowa 72, Szczecin 70-871\",\"third_contact_link\":\"https:\\/\\/www.google.com\\/maps?q=ul. Ko\\u0144cowa 72, Szczecin 70-871\",\"first_contact_icon\":\"global\\/uploads\\/landing-contents\\/first_contact_icon\\/y2JLh2nQuxxtHdonWYnd.svg\",\"second_contact_icon\":\"global\\/uploads\\/landing-contents\\/second_contact_icon\\/aULegd4XoCg1KA8itoWo.svg\",\"third_contact_icon\":\"global\\/uploads\\/landing-contents\\/third_contact_icon\\/4f0I5luaOquAsqsgOwcV.svg\"}', 1, 15, 'en', '2022-10-22 07:54:48', '2025-05-27 06:58:30'),
(28, 'default', 'Newsletter Section\r\n', 'newsletter', '{\"title\":\"Subscribe to our Newsletter!\",\"sub_title\":\"By subscribing you agree to with our Privacy Policy and provide consent to receive updates from our company.\",\"image\":\"global\\/uploads\\/landing-contents\\/image\\/GAJP88S1oigb3GIGBji1.png\",\"button_label\":\"Join Us\",\"subtitle\":\"Get weekly update about our product on your email, no spam guaranteed we promise \\u270c\\ufe0f\"}', 1, 15, 'en', '2022-10-22 07:54:48', '2025-06-02 10:59:22'),
(29, 'default', 'Earning Calculation Section\r\n', 'earning-calculation', '{\"title\":\"[[color_text= Calculate ]] your possible Earnings\",\"subtitle\":\"Explore different outcomes based on your choices and see where the path could lead.\"}', 1, 4, 'en', '2022-10-22 07:54:48', '2025-06-02 05:59:30');

-- --------------------------------------------------------

--
-- Table structure for table `languages`
--

CREATE TABLE `languages` (
  `id` bigint UNSIGNED NOT NULL,
  `flag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `locale` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_rtl` tinyint(1) NOT NULL DEFAULT '0',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `languages`
--

INSERT INTO `languages` (`id`, `flag`, `name`, `locale`, `is_rtl`, `is_default`, `status`, `created_at`, `updated_at`) VALUES
(1, NULL, 'English', 'en', 0, 0, 1, NULL, '2025-04-13 11:56:52');

-- --------------------------------------------------------

--
-- Table structure for table `level_referrals`
--

CREATE TABLE `level_referrals` (
  `id` bigint UNSIGNED NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `the_order` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `bounty` double DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `login_activities`
--

CREATE TABLE `login_activities` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` int DEFAULT NULL,
  `ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `messages`
--

CREATE TABLE `messages` (
  `id` bigint UNSIGNED NOT NULL,
  `model` enum('user','admin') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user',
  `user_id` bigint UNSIGNED NOT NULL,
  `ticket_id` bigint UNSIGNED NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attach` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(6, '2024_08_18_061828_create_settings_table', 1),
(7, '2024_08_18_063514_create_admins_table', 1),
(8, '2024_08_18_071630_create_permission_tables', 1),
(10, '2024_08_18_083253_create_gateways_table', 1),
(11, '2024_08_18_090031_create_notifications_table', 1),
(12, '2024_08_18_095609_create_kycs_table', 1),
(13, '2024_08_18_101453_create_user_kycs_table', 1),
(14, '2024_08_19_041604_create_plugins_table', 1),
(16, '2024_08_19_043611_create_tickets_table', 1),
(18, '2024_08_19_044159_create_navigations_table', 1),
(19, '2024_08_19_044603_create_themes_table', 1),
(22, '2024_08_19_044845_create_custom_csses_table', 1),
(23, '2024_08_19_045416_create_level_referrals_table', 1),
(25, '2024_08_19_050813_create_withdrawal_schedules_table', 1),
(27, '2024_08_19_051434_create_login_activities_table', 1),
(29, '2024_08_20_091013_create_cron_jobs_table', 1),
(30, '2024_08_20_091118_create_cron_job_logs_table', 1),
(31, '2024_08_20_091341_create_blogs_table', 1),
(32, '2024_08_20_092532_create_set_tunes_table', 1),
(33, '2024_08_20_092845_create_page_settings_table', 1),
(35, '2024_08_20_093138_create_testimonials_table', 1),
(37, '2024_09_08_121409_create_withdraw_accounts_table', 1),
(38, '2024_09_19_054431_create_referrals_table', 1),
(39, '2024_11_14_040813_create_messages_table', 1),
(42, '2018_08_29_205156_create_translations_table', 2),
(44, '2018_08_29_200844_create_languages_table', 3),
(45, '2024_11_23_084815_create_languages_table', 4),
(51, '2024_11_20_045313_create_merchants_table', 5),
(52, '2024_11_24_060620_create_merchant_forms_table', 5),
(53, '2024_11_30_094656_create_agents_table', 6),
(54, '2024_11_30_094702_create_agent_forms_table', 6),
(60, '2025_01_04_091256_create_personal_access_tokens_table', 10),
(64, '2024_11_17_061922_create_currencies_table', 12),
(66, '0001_01_01_000000_create_users_table', 13),
(67, '2024_08_18_082613_create_transactions_table', 13),
(69, '2024_08_19_050624_create_withdraw_methods_table', 13),
(70, '2024_08_19_050945_create_deposit_methods_table', 13),
(71, '2024_11_17_105848_create_user_wallets_table', 13),
(73, '2024_12_29_114936_create_invoices_table', 13),
(75, '2025_01_08_092924_create_sandbox_transactions_table', 13),
(76, '2024_12_30_035108_create_money_requests_table', 14),
(77, '2024_12_10_110437_create_gifts_table', 15),
(80, '2024_08_20_053202_create_templates_table', 16),
(81, '2024_08_19_043507_create_subscribers_table', 17),
(84, '2024_08_19_044604_create_landing_pages_table', 18),
(85, '2024_08_19_044605_create_landing_contents_table', 18),
(86, '2024_08_19_044052_create_pages_table', 19),
(88, '2024_08_20_093008_create_socials_table', 20),
(90, '2024_08_20_093321_create_user_navigations_table', 21);

-- --------------------------------------------------------

--
-- Table structure for table `miners`
--

CREATE TABLE `miners` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `coin_id` bigint UNSIGNED NOT NULL,
  `renewable_energy` int NOT NULL DEFAULT '100',
  `uptime` int NOT NULL DEFAULT '100',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `network_hashrate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `network_hashrate_amount` decimal(10,5) NOT NULL DEFAULT '0.00000',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\Admin', 1);

-- --------------------------------------------------------

--
-- Table structure for table `navigations`
--

CREATE TABLE `navigations` (
  `id` bigint UNSIGNED NOT NULL,
  `page_id` bigint UNSIGNED DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` json DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `footer_position` int DEFAULT NULL,
  `header_position` int DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `translate` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `navigations`
--

INSERT INTO `navigations` (`id`, `page_id`, `name`, `type`, `url`, `footer_position`, `header_position`, `status`, `created_at`, `updated_at`, `translate`) VALUES
(2, 2, 'Contact', '[\"footer_widget_one\", \"header\"]', 'contact', 6, 5, 1, '2022-10-24 08:02:26', '2025-06-02 08:03:04', '{\"en\":\"Contact\", \"ar\":\"اتصال\"}'),
(4, 14, 'Privacy Policy', '[\"footer_widget_two\"]', '/page/privacy-policy', 7, NULL, 1, '2022-10-24 08:05:24', '2025-06-02 04:22:26', '{\"en\":\"Privacy Policy\", \"ar\":\"سياسة الخصوصية\"}'),
(11, 3, 'About Us', '[\"footer_widget_one\", \"header\"]', 'about', 3, 2, 1, '2022-10-27 09:38:03', '2025-06-02 04:22:26', '{\"en\":\"About Us\", \"ar\":\"من نحن\"}'),
(14, NULL, 'Home', '[\"header\", \"footer_widget_one\"]', '/', 2, 1, 1, '2022-10-28 02:34:49', '2025-06-02 04:22:26', '{\"en\":\"Home\", \"ar\":\"الصفحة الرئيسية\"}'),
(16, 1, 'Blog', '[\"header\", \"footer_widget_one\"]', 'blog', 5, 4, 1, '2022-11-16 20:35:57', '2025-06-02 04:22:26', '{\"en\":\"Blog\", \"ar\":\"المدونة\"}'),
(18, 209, 'Account Login', '[\"widget_one\"]', '/login', 16, NULL, 1, '2022-11-16 18:01:03', '2025-02-04 03:56:16', '{\"en\":\"Account Login\", \"ar\":\"تسجيل الدخول\"}'),
(19, 13, 'Terms and Conditions', '[\"widget_two\"]', '/page/terms-conditions', 15, NULL, 1, '2022-11-16 18:03:30', '2025-04-09 09:37:56', '{\"en\":\"Terms and Conditions\", \"ar\":\"الشروط والأحكام\"}'),
(38, 28, 'Mining Plan', '[\"header\", \"footer_widget_one\"]', 'pricing', 4, 3, 1, '2025-05-29 05:26:23', '2025-06-02 04:22:26', '{\"en\":\"Mining Plan\", \"ar\":\"خطة التعدين\"}'),
(41, 13, 'Terms of Service', '[\"footer_widget_two\"]', '/page/terms-conditions', 1, NULL, 1, '2025-06-02 04:18:02', '2025-06-02 04:22:26', '{\"en\":\"Terms of Service\", \"ar\":\"شروط الخدمة\"}'),
(42, 53, 'Usage Policy', '[\"footer_widget_two\"]', '/page/usage-policy', 8, NULL, 1, '2025-06-02 04:22:01', '2025-06-02 04:22:26', '{\"en\":\"Usage Policy\", \"ar\":\"سياسة الاستخدام\"}');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint UNSIGNED NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `for` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `action_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pages`
--

CREATE TABLE `pages` (
  `id` bigint UNSIGNED NOT NULL,
  `theme` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `type` enum('static','dynamic') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'dynamic',
  `status` tinyint DEFAULT NULL,
  `locale` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'en',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pages`
--

INSERT INTO `pages` (`id`, `theme`, `title`, `code`, `data`, `type`, `status`, `locale`, `url`, `created_at`, `updated_at`) VALUES
(1, 'default', 'Blog ', 'blog', '{\"title\":\"All Blogs\",\"title_small\":\"Blog Title Smallss\",\"title_big\":\"Our Latest News\",\"sidebar_widget_title\":\"Recent Posts\",\"sidebar_widget_title_2\":\"Recent Blogss\",\"meta_keywords\":\"All Blogs\",\"meta_description\":\"All Blogs\",\"header\":\"Our [[color_text= Blog ]]\",\"sub_title\":\"Track all your mining transactions with complete transparency and security.\",\"section_id\":\"[]\"}', 'static', 1, 'en', 'blog', '2025-01-13 05:10:19', '2025-05-27 04:40:35'),
(2, 'default', 'Contact', 'contact', '{\"title\":\"Contact\",\"right_title\":\"Send Us a [[color_text= Message ]]\",\"right_subtitle\":\"Say something to start a live chat!\",\"phone_no\":\"+1012 3456 789\",\"email\":\"<EMAIL>\",\"address\":\"2972 Westheimer Rd. Santa Ana, Illinois 85486\",\"meta_keywords\":\"contact us, email\",\"meta_description\":\"Contact\",\"phone_icon\":\"global\\/uploads\\/images\\/RN5nP3Iabg33IYBstCgM.png\",\"email_icon\":\"global\\/uploads\\/images\\/5RyH5ASEt3pvhMjSNWF4.png\",\"address_icon\":\"global\\/uploads\\/images\\/PvkFcZ3PoYzz607vnrMN.png\",\"section_id\":\"[]\",\"left_title\":\"[[color_text= Let\\u2019s Talk ]] Mining Solutions\",\"left_subtitle\":\"Have questions? Need a quote? We\'re here to help\",\"left_image\":\"global\\/uploads\\/images\\/UK4tkULFNGBMAnvIHxs3.png\"}', 'static', 1, 'en', 'contact', '2025-01-13 05:10:19', '2025-05-27 04:27:31'),
(3, 'default', 'About', 'about', '{\"title\":\"About Us\",\"content\":\"<p>Your trusted gateway to the world of cryptocurrencies. Our mission is to empower individuals and businesses with secure, innovative, and user-friendly crypto solutions.<\\/p>\\r\\n\\r\\n<p>Who We Are<\\/p>\\r\\n\\r\\n<p>we are a team of blockchain enthusiasts, financial experts, and tech innovators dedicated to simplifying digital asset management. We believe in the power of decentralized finance (DeFi) and blockchain technology to revolutionize global transactions and investment opportunities.<\\/p>\\r\\n\\r\\n<p>What We Offer<\\/p>\\r\\n\\r\\n<ul><li><p><strong>Secure Crypto Trading:<\\/strong> Buy, sell, and exchange cryptocurrencies with ease and security.<\\/p><\\/li><li><p><strong>Reliable Wallet Services:<\\/strong> Store and manage your digital assets with our cutting-edge, secure wallet solutions.<\\/p><\\/li><li><p><strong>Real-Time Market Insights:<\\/strong> Stay updated with live price tracking, news, and in-depth market analysis.<\\/p><\\/li><li><p><strong>Educational Resources:<\\/strong> Learn about blockchain, crypto investments, and security best practices.<\\/p><\\/li><\\/ul>\\r\\n\\r\\n<p>Why Choose Us?<\\/p>\\r\\n\\r\\n<ul><li><p><strong>Security First:<\\/strong> We prioritize the safety of your assets with advanced encryption and multi-layer protection.<\\/p><\\/li><li><p><strong>User-Friendly Interface:<\\/strong> Our platform is designed for both beginners and experienced traders.<\\/p><\\/li><li><p><strong>24\\/7 Support:<\\/strong> Our dedicated support team is always available to assist you with any queries.<\\/p><\\/li><li><p><strong>Transparency &amp; Trust:<\\/strong> We operate with full transparency, ensuring a fair and trustworthy crypto experience.<\\/p><\\/li><\\/ul>\\r\\n\\r\\n<p>Join us on this journey as we unlock the full potential of cryptocurrencies and reshape the financial landscape. Whether you are a beginner or an expert, Moneychain is here to help you navigate the digital economy with confidence.<\\/p>\",\"banner_image\":\"global\\/uploads\\/images\\/UIJs6Wx1pMJPOewMsr15.webp\",\"meta_keywords\":\"About Us\",\"meta_description\":\"About Us\",\"short_content\":\"<div><b><i>Our Mission:<\\/i><\\/b><\\/div><div><br><\\/div><div>Solana is a blockchain platform which uses a proof-of-stake mechanism to provide smart contract functionality. Its native cryptocurrency is SOL. Solana was launched in 2020 by Solana Labs, which was founded by Anatoly Yakovenko<\\/div><div><br><\\/div><div><b>Blockchain Development:<\\/b> Building and maintaining blockchain networks.<\\/div><div><b>Smart Contract Development:<\\/b> Writing, testing, and deploying smart contracts.<\\/div><div><b>Cryptocurrency Wallet Development:<\\/b> Creating s\\\\ecure digital wallets for storing cryptocurrencies.<\\/div><div><b>Token Creation:<\\/b> Developing and launching new cryptocurrencies or tokens..<\\/div><div><b>Blockchain Consulting:<\\/b> Advising on blockchain technology and its implementation.<\\/div><div><b>Crypto Trading Bots Development:<\\/b> Developing automated trading bots for cryptocurrency<\\/div>\",\"about_title\":\"About Us\",\"section_id\":\"[\\\"19\\\"]\",\"who_title\":\"[[color_text=Who ]]We Are\",\"who_description\":\"We are a leading mining company committed to the responsible extraction of natural resources, combining advanced technology with sustainable practices. With decades of industry experience and global reach, we deliver high-quality minerals while minimizing environmental impact\",\"button_label\":\"Learn more\",\"button_link\":\"https:\\/\\/orexcoin.test\\/about\",\"button_target\":\"_blank\",\"mission_title\":\"Our Mission:\",\"mission_description\":\"To extract and deliver resources in a that fuels industries and protects the planet.To extract and deliver resources in a way that fuels industries and protects the planet.\",\"vision_title\":\"Our Vision:\",\"vision_description\":\"To be the world\\u2019s most trusted and sustainable mining solutions provider.To be the world\\u2019s most trusted and sustainable mining solutions provider.To be the world\\u2019s most trusted and sustainable mining solutions provider.To be the world\\u2019s most trusted and sustainable mining solutions provider.\",\"core_value_titles\":\"[\\\"Sustainability\\\",\\\"Safety\\\",\\\"Innovation\\\",\\\"Integrity\\\",\\\"Community\\\"]\",\"mining_title\":\"[[color_text= Minings  ]] Responsibly\",\"mining_subtitle\":\"We are a leading mining company committed to the responsible.\",\"mining_description\":\"We invest in environmental preservation, workforce safety, and community engagement. Our projects adhere to global sustainability standards and aim for long-term environmental restoration.\",\"who_image\":\"global\\/uploads\\/images\\/C1r3uUaBxzPbT4wWXcnu.png\",\"core_value_title\":\"Our Core Values\"}', 'static', 1, 'en', 'about', '2025-01-13 05:10:19', '2025-06-03 05:29:29'),
(9, 'default', 'Login', 'login', '{\"title\":\"Welcome  Back!\",\"image\":\"global\\/uploads\\/images\\/yUz8lNaSxAVWHbz2dNd5.png\",\"section_id\":\"[]\"}', 'static', 1, 'en', 'login', '2025-01-13 05:10:19', '2025-06-03 04:59:52'),
(10, 'default', 'Register', 'register', '{\"title\":\"Get Started\",\"image\":\"global\\/uploads\\/images\\/yUz8lNaSxAVWHbz2dNd5.png\",\"section_id\":\"[]\"}', 'static', 1, 'en', 'register', '2025-01-13 05:10:19', '2025-06-03 04:55:36'),
(11, 'default', 'Forgot Password', 'forgetpassword', '{\"title\":\"Forget password\",\"image\":\"global\\/uploads\\/images\\/yUz8lNaSxAVWHbz2dNd5.png\",\"section_id\":\"[]\"}', 'static', 1, 'en', 'forgot', '2025-01-13 05:10:19', '2025-06-03 04:55:31'),
(12, 'default', 'Reset Password', 'resetpassword', '{\"title\":\"Reset password\",\"image\":\"global\\/uploads\\/images\\/fS4ohhEfcNed9L55good.png\",\"section_id\":\"[]\"}', 'static', 1, 'en', 'resetpassword', '2025-01-13 05:10:19', '2025-06-03 04:55:51'),
(13, 'default', 'Terms & Conditions', 'terms-conditions', '{\"section_id\":\"[]\",\"meta_keywords\":\"Terms & Conditions\",\"meta_description\":\"Terms & Conditions\",\"content\":\"<p><b>Last updated:<\\/b> 17 May, 2025<\\/p>\\r\\n\\r\\n\\r\\n<p><b>Introduction<\\/b><\\/p>\\r\\n\\r\\n<p>Welcome to <b>Newisty<\\/b> (\\\"Company\\\", \\\"we\\\", \\\"our\\\", \\\"us\\\")!<\\/p>\\r\\n\\r\\n<p>These Terms of Service (\\\"Terms\\\", \\\"Terms of Service\\\") govern your use of our website located at <b>https:\\/\\/newisty.com\\/<\\/b> (together or individually \\\"Service\\\") operated by <b>Newisty<\\/b>.<\\/p>\\r\\n\\r\\n<p>Our <a href=\\\"https:\\/\\/newisty.com\\\" title=\\\"Privacy Policy\\\">Privacy Policy<\\/a> also governs your use of our Service and explains how we collect, safeguard and disclose information that results from your use of our web pages.<\\/p>\\r\\n\\r\\n<p>Your agreement with us includes these Terms and our <a href=\\\"https:\\/\\/newisty.com\\\" title=\\\"Privacy Policy\\\">Privacy Policy<\\/a> (\\\"Agreements\\\"). You acknowledge that you have read and understood Agreements, and agree to be bound of them.<\\/p>\\r\\n\\r\\n<p>If you do not agree with (or cannot comply with) Agreements, then you may not use the Service, but please let us know by submitting this <a href=\\\"https:\\/\\/newisty.com\\/contact-us\\\">Contact Us<\\/a> so we can try to find a solution. These Terms apply to all visitors, users and others who wish to access or use Service.<\\/p>\\r\\n\\r\\n<p><b>Communications<\\/b><\\/p>\\r\\n\\r\\n<p>By using our Service, you agree to subscribe to newsletters, marketing or promotional materials and other information we may send. However, you may opt-out of receiving any, or all, of these communications from us by following the unsubscribe link or by submitting this <a href=\\\"https:\\/\\/newisty.com\\/contact-us\\\">Contact Us<\\/a>.<\\/p>\\r\\n\\r\\n<p><b>Contests, Sweepstakes and Promotions<\\/b><\\/p>\\r\\n\\r\\n<p>Any contests, sweepstakes or other promotions (collectively, \\\"Promotions\\\") made available through Service may be governed by rules that are separate from these Terms of Service. If you participate in any Promotions, please review the applicable rules as well as our Privacy Policy. If the rules for a Promotion conflict with these Terms of Service, Promotion rules will apply.<\\/p>\\r\\n\\r\\n<p><b>Content<\\/b><\\/p>\\r\\n\\r\\n<p>The content\\/material found on or through this Service is the property of Newisty or used with permission. You may not distribute, redistribute, modify, transmit, use, reuse, download, reproduce, repost, copy, or use said Content\\/material, whether in whole or in part, for commercial purposes or for personal gain, without express advance written permission from us.<\\/p>\\r\\n\\r\\n<p><b>Prohibited Uses<\\/b><\\/p>\\r\\n\\r\\n<p>You may use Service only for lawful purposes and in accordance with Terms. You agree not to use Service:<\\/p>\\r\\n\\r\\n<p>0.1. In any way that violates any applicable national or international law or regulation.<\\/p>\\r\\n\\r\\n<p>0.2. For the purpose of exploiting, harming, or attempting to exploit or harm minors in any way by exposing them to inappropriate content or otherwise.<\\/p>\\r\\n\\r\\n<p>0.3. To transmit, or procure the sending of, any advertising or promotional material, including any \\\"junk mail\\\", \\\"chain letter,\\\" \\\"spam,\\\" or any other similar solicitation.<\\/p>\\r\\n\\r\\n<p>0.4. To impersonate or attempt to impersonate Company, a Company employee, another user, or any other person or entity.<\\/p>\\r\\n\\r\\n<p>0.5. In any way that infringes upon the rights of others, or in any way is illegal, threatening, fraudulent, or harmful, or in connection with any unlawful, illegal, fraudulent, or harmful purpose or activity.<\\/p>\\r\\n\\r\\n<p>0.6. To engage in any other conduct that restricts or inhibits anyone\\u2019s use or enjoyment of Service, or which, as determined by us, may harm or offend Company or users of Service or expose them to liability.<\\/p>\\r\\n\\r\\n<p>Additionally, you agree not to:<\\/p>\\r\\n\\r\\n<p>0.1. Use Service in any manner that could disable, overburden, damage, or impair Service or interfere with any other party\\u2019s use of Service, including their ability to engage in real time activities through Service.<\\/p>\\r\\n\\r\\n<p>0.2. Use any robot, spider, or other automatic devices, process, or means to access Service for any purpose, including monitoring or copying any of the material on Service.<\\/p>\\r\\n\\r\\n<p>0.3. Use any manual process to monitor or copy any of the material on Service or for any other unauthorized purpose without our prior written consent.<\\/p>\\r\\n\\r\\n<p>0.4. Use any device, software, or routine that interferes with the proper working of Service.<\\/p>\\r\\n\\r\\n<p>0.5. Introduce any viruses, trojan horses, worms, logic bombs, or other material which is malicious or technologically harmful.<\\/p>\\r\\n\\r\\n<p>0.6. Attempt to gain unauthorized access to, interfere with, damage, or disrupt any parts of Service, the server on which Service is stored, or any server, computer, or database connected to Service.<\\/p>\\r\\n\\r\\n<p>0.7. Attack Service via a denial-of-service attack or a distributed denial-of-service attack.<\\/p>\\r\\n\\r\\n<p>0.8. Take any action that may damage or falsify the Company rating.<\\/p>\\r\\n\\r\\n<p>0.9. Otherwise attempt to interfere with the proper working of Service.<\\/p>\\r\\n\\r\\n<p><b>Analytics<\\/b><\\/p>\\r\\n\\r\\n<p>Analytics collected on this site is collected by Google Analytics. Google Analytics tracks where you came from by your IP address location. Google Analytics privacy policy: <a href=\\\"https:\\/\\/www.google.com\\/analytics\\/terms\\/us.html\\\">https:\\/\\/www.google.com\\/analytics\\/terms\\/us.html<\\/a><\\/p>\\r\\n<p><b>No Use By Minors<\\/b><\\/p>\\r\\n\\r\\n<p>Service is intended only for access and use by individuals at least eighteen (18) years old. By accessing or using Service, you warrant and represent that you are at least eighteen (18) years of age and with the full authority, right, and capacity to enter into this agreement and abide by all of the terms and conditions of Terms. If you are not at least eighteen (18) years old, you are prohibited from both the access and usage of Service.<\\/p>\\r\\n\\r\\n<p><b>Accounts<\\/b><\\/p>\\r\\n\\r\\n<p>When you create an account with us, you guarantee that you are above the age of 18 and that the information you provide us is accurate, complete, and current at all times. Inaccurate, incomplete, or obsolete information may result in the immediate termination of your account on Service.<\\/p>\\r\\n\\r\\n<p>You are responsible for maintaining the confidentiality of your account and password, including but not limited to the restriction of access to your computer and\\/or account. You agree to accept responsibility for any and all activities or actions that occur under your account and\\/or password, whether your password is with our Service or a third-party service. You must notify us immediately upon becoming aware of any breach of security or unauthorized use of your account.<\\/p>\\r\\n\\r\\n<p>You may not use as a username the name of another person or entity or that is not lawfully available for use, a name or trademark that is subject to any rights of another person or entity other than you, without appropriate authorization. You may not use as a username any name that is offensive, vulgar or obscene.<\\/p>\\r\\n\\r\\n<p>We reserve the right to refuse service, terminate accounts, remove or edit content, or cancel orders at our sole discretion.<\\/p>\\r\\n\\r\\n<p><b>Intellectual Property<\\/b><\\/p>\\r\\n\\r\\n<p>Service and its original content (excluding Content provided by users), features and functionality are and will remain the exclusive property of Newisty and its licensors. Service is protected by copyright, trademark, and other laws of and foreign countries. Our trademarks may not be used in connection with any product or service without the prior written consent of Newisty.<\\/p>\\r\\n\\r\\n<p><b>Copyright Policy<\\/b><\\/p>\\r\\n\\r\\n<p>We respect the intellectual property rights of others. It is our policy to respond to any claim that Content posted on Service infringes on the copyright or other intellectual property rights (\\\"Infringement\\\") of any person or entity.<\\/p>\\r\\n\\r\\n<p>If you are a copyright owner or authorized on behalf of one, and you believe that the copyrighted work has been copied in a way that constitutes copyright infringement, please submit your claim via submitting this <a href=\\\"https:\\/\\/newisty.com\\/contact-us\\\">Contact Us<\\/a>, with the subject line: \\\"Copyright Infringement\\\" and include in your claim a detailed description of the alleged Infringement as detailed below, under \\\"DMCA Notice and Procedure for Copyright Infringement Claims\\\"<\\/p>\\r\\n\\r\\n<p>You may be held accountable for damages (including costs and attorneys\\u2019 fees) for misrepresentation or bad-faith claims on the infringement of any Content found on and\\/or through Service on your copyright.<\\/p>\\r\\n\\r\\n<p><b>DMCA Notice and Procedure for Copyright Infringement Claims<\\/b><\\/p>\\r\\n\\r\\n<p>You may submit a notification pursuant to the Digital Millennium Copyright Act (DMCA) by providing our Copyright Agent with the following information in writing (see 17 U.S.C 512(c)(3) for further detail):<\\/p>\\r\\n\\r\\n<p>0.1. an electronic or physical signature of the person authorized to act on behalf of the owner of the copyright\\u2019s interest;<\\/p>\\r\\n\\r\\n<p>0.2. a description of the copyrighted work that you claim has been infringed, including the URL (i.e., web page address) of the location where the copyrighted work exists or a copy of the copyrighted work;<\\/p>\\r\\n\\r\\n<p>0.3. identification of the URL or other specific location on Service where the material that you claim is infringing is located;<\\/p>\\r\\n\\r\\n<p>0.4. your address, telephone number, and email address;<\\/p>\\r\\n\\r\\n<p>0.5. a statement by you that you have a good faith belief that the disputed use is not authorized by the copyright owner, its agent, or the law;<\\/p>\\r\\n\\r\\n<p>0.6. a statement by you, made under penalty of perjury, that the above information in your notice is accurate and that you are the copyright owner or authorized to act on the copyright owner\\u2019s behalf.<\\/p>\\r\\n\\r\\n<p>You can contact our Copyright Agent via submitting this <a href=\\\"https:\\/\\/newisty.com\\/contact-us\\\">Contact Us<\\/a><\\/p>\\r\\n\\r\\n<p><b>Error Reporting and Feedback<\\/b><\\/p>\\r\\n\\r\\n<p>You may provide us either directly submitting this <a href=\\\"https:\\/\\/newisty.com\\/contact-us\\\">Contact Us<\\/a> or via third-party sites and tools with information and feedback concerning errors, suggestions for improvements, ideas, problems, complaints, and other matters related to our Service (\\\"Feedback\\\"). You acknowledge and agree that: (i) you shall not retain, acquire or assert any intellectual property right or other rights, title or interest in or to the Feedback; (ii) Company may have developed ideas similar to the Feedback; (iii) Feedback does not contain confidential information or proprietary information from you or any third party, and (iv) Company is not under any obligation of confidentiality with respect to the Feedback. In the event the transfer of the ownership to the Feedback is not possible due to applicable mandatory laws, you grant Company and its affiliates an exclusive, transferable, irrevocable, free-of-charge, sub-licensable, unlimited and perpetual right to use (including copy, modify, create derivative works, publish, distribute and commercialize) Feedback in any manner and for any purpose.<\\/p>\\r\\n\\r\\n<p><b>Links To Other Web Sites<\\/b><\\/p>\\r\\n\\r\\n<p>Our Service may contain links to third-party websites or services that are not owned or controlled by Newisty.<\\/p>\\r\\n\\r\\n<p>Newisty has no control over and assumes no responsibility for the content, privacy policies, or practices of any third-party websites or services. We do not warrant the offerings of any of these entities\\/individuals or their websites.<\\/p>\\r\\n\\r\\n<p><strong>13.1 Reservation of Rights<\\/strong><\\/p>\\r\\n\\r\\n<p>We reserve the right to request that you remove all links or any particular link to our Website. You approve to immediately remove all links to our Website upon request. We also reserve the right to amend these terms and conditions and its linking policy at any time. By continuously linking to our Website, you agree to be bound to and follow these linking terms and conditions.<\\/p>\\r\\n\\r\\n<p><strong>13.2 Removal of links from our website<\\/strong><\\/p>\\r\\n\\r\\n<p>If you find any link on our Website that is offensive for any reason, you are free to <a href=\\\"https:\\/\\/newisty.com\\/contact-us\\\">Contact Us<\\/a> and inform us at any moment. We will consider requests to remove links but we are not obligated to or so or to respond to you directly.<\\/p>\\r\\n\\r\\n<p>We do not ensure that the information on this website is correct, we do not warrant its completeness or accuracy; nor do we promise to ensure that the website remains available or that the material on the website is kept up to date.<\\/p>\\r\\n\\r\\n\\r\\n\\r\\n<p>YOU ACKNOWLEDGE AND AGREE THAT COMPANY SHALL NOT BE RESPONSIBLE OR LIABLE, DIRECTLY OR INDIRECTLY, FOR ANY DAMAGE OR LOSS CAUSED OR ALLEGED TO BE CAUSED BY OR IN CONNECTION WITH USE OF OR RELIANCE ON ANY SUCH CONTENT, GOODS OR SERVICES AVAILABLE ON OR THROUGH ANY SUCH THIRD-PARTY WEB SITES OR SERVICES.<\\/p>\\r\\n\\r\\n<p>WE STRONGLY ADVISE YOU TO READ THE TERMS OF SERVICE AND PRIVACY POLICIES OF ANY THIRD-PARTY WEBSITES OR SERVICES THAT YOU VISIT.<\\/p>\\r\\n\\r\\n<p><b>Disclaimer Of Warranty<\\/b><\\/p>\\r\\n\\r\\n<p>THESE SERVICES ARE PROVIDED BY COMPANY ON AN \\\"AS IS\\\" AND \\\"AS AVAILABLE\\\" BASIS. COMPANY MAKES NO REPRESENTATIONS OR WARRANTIES OF ANY KIND, EXPRESS OR IMPLIED, AS TO THE OPERATION OF THEIR SERVICES, OR THE INFORMATION, CONTENT OR MATERIALS INCLUDED THEREIN. YOU EXPRESSLY AGREE THAT YOUR USE OF THESE SERVICES, THEIR CONTENT, AND ANY SERVICES OR ITEMS OBTAINED FROM US IS AT YOUR SOLE RISK.<\\/p>\\r\\n\\r\\n<p>NEITHER COMPANY NOR ANY PERSON ASSOCIATED WITH COMPANY MAKES ANY WARRANTY OR REPRESENTATION WITH RESPECT TO THE COMPLETENESS, SECURITY, RELIABILITY, QUALITY, ACCURACY, OR AVAILABILITY OF THE SERVICES. WITHOUT LIMITING THE FOREGOING, NEITHER COMPANY NOR ANYONE ASSOCIATED WITH COMPANY REPRESENTS OR WARRANTS THAT THE SERVICES, THEIR CONTENT, OR ANY SERVICES OR ITEMS OBTAINED THROUGH THE SERVICES WILL BE ACCURATE, RELIABLE, ERROR-FREE, OR UNINTERRUPTED, THAT DEFECTS WILL BE CORRECTED, THAT THE SERVICES OR THE SERVER THAT MAKES IT AVAILABLE ARE FREE OF VIRUSES OR OTHER HARMFUL COMPONENTS OR THAT THE SERVICES OR ANY SERVICES OR ITEMS OBTAINED THROUGH THE SERVICES WILL OTHERWISE MEET YOUR NEEDS OR EXPECTATIONS.<\\/p>\\r\\n\\r\\n<p>COMPANY HEREBY DISCLAIMS ALL WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, STATUTORY, OR OTHERWISE, INCLUDING BUT NOT LIMITED TO ANY WARRANTIES OF MERCHANTABILITY, NON-INFRINGEMENT, AND FITNESS FOR PARTICULAR PURPOSE.<\\/p>\\r\\n\\r\\n<p>THE FOREGOING DOES NOT AFFECT ANY WARRANTIES WHICH CANNOT BE EXCLUDED OR LIMITED UNDER APPLICABLE LAW.<\\/p>\\r\\n\\r\\n<p><b>Limitation Of Liability<\\/b><\\/p>\\r\\n\\r\\n<p>EXCEPT AS PROHIBITED BY LAW, YOU WILL HOLD US AND OUR OFFICERS, DIRECTORS, EMPLOYEES, AND AGENTS HARMLESS FOR ANY INDIRECT, PUNITIVE, SPECIAL, INCIDENTAL, OR CONSEQUENTIAL DAMAGE, HOWEVER IT ARISES (INCLUDING ATTORNEYS\\u2019 FEES AND ALL RELATED COSTS AND EXPENSES OF LITIGATION AND ARBITRATION, OR AT TRIAL OR ON APPEAL, IF ANY, WHETHER OR NOT LITIGATION OR ARBITRATION IS INSTITUTED), WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE, OR OTHER TORTIOUS ACTION, OR ARISING OUT OF OR IN CONNECTION WITH THIS AGREEMENT, INCLUDING WITHOUT LIMITATION ANY CLAIM FOR PERSONAL INJURY OR PROPERTY DAMAGE, ARISING FROM THIS AGREEMENT AND ANY VIOLATION BY YOU OF ANY FEDERAL, STATE, OR LOCAL LAWS, STATUTES, RULES, OR REGULATIONS, EVEN IF COMPANY HAS BEEN PREVIOUSLY ADVISED OF THE POSSIBILITY OF SUCH DAMAGE. EXCEPT AS PROHIBITED BY LAW, IF THERE IS LIABILITY FOUND ON THE PART OF THE COMPANY, IT WILL BE LIMITED TO THE AMOUNT PAID FOR THE PRODUCTS AND\\/OR SERVICES, AND UNDER NO CIRCUMSTANCES WILL THERE BE CONSEQUENTIAL OR PUNITIVE DAMAGES. SOME STATES DO NOT ALLOW THE EXCLUSION OR LIMITATION OF PUNITIVE, INCIDENTAL OR CONSEQUENTIAL DAMAGES, SO THE PRIOR LIMITATION OR EXCLUSION MAY NOT APPLY TO YOU.<\\/p>\\r\\n\\r\\n<p><b>Termination<\\/b><\\/p>\\r\\n\\r\\n<p>We may terminate or suspend your account and bar access to Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of Terms.<\\/p>\\r\\n\\r\\n<p>If you wish to terminate your account, you may simply discontinue using Service.<\\/p>\\r\\n\\r\\n<p>All provisions of Terms which by their nature should survive termination shall survive termination, including, without limitation, ownership provisions, warranty disclaimers, indemnity and limitations of liability.<\\/p>\\r\\n\\r\\n<p><b>Governing Law<\\/b><\\/p>\\r\\n\\r\\n<p>These Terms shall be governed and construed in accordance with the laws of Bangladesh, which governing law applies to the agreement without regard to its conflict of law provisions.<\\/p>\\r\\n\\r\\n<p>Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights. If any provision of these Terms is held to be invalid or unenforceable by a court, the remaining provisions of these Terms will remain in effect. These Terms constitute the entire agreement between us regarding our Service and supersede and replace any prior agreements we might have had between us regarding Service.<\\/p>\\r\\n\\r\\n<p><b>Changes To Service<\\/b><\\/p>\\r\\n\\r\\n<p>We reserve the right to withdraw or amend our Service, and any service or material we provide via Service, in our sole discretion without notice. We will not be liable if for any reason all or any part of Service is unavailable at any time or for any period. From time to time, we may restrict access to some parts of Service, or the entire Service, to users, including registered users.<\\/p>\\r\\n\\r\\n<p><b>Amendments To Terms<\\/b><\\/p>\\r\\n\\r\\n<p>We may amend Terms at any time by posting the amended terms on this site. It is your responsibility to review these Terms periodically.<\\/p>\\r\\n\\r\\n<p>Your continued use of the Platform following the posting of revised Terms means that you accept and agree to the changes. You are expected to check this page frequently so you are aware of any changes, as they are binding on you.<\\/p>\\r\\n\\r\\n<p>By continuing to access or use our Service after any revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, you are no longer authorized to use Service.<\\/p>\\r\\n\\r\\n<p><b>Waiver And Severability<\\/b><\\/p>\\r\\n\\r\\n<p>No waiver by Company of any term or condition set forth in Terms shall be deemed a further or continuing waiver of such term or condition or a waiver of any other term or condition, and any failure of Company to assert a right or provision under Terms shall not constitute a waiver of such right or provision.<\\/p>\\r\\n\\r\\n<p>If any provision of Terms is held by a court or other tribunal of competent jurisdiction to be invalid, illegal or unenforceable for any reason, such provision shall be eliminated or limited to the minimum extent such that the remaining provisions of Terms will continue in full force and effect.<\\/p>\\r\\n\\r\\n<p><b>Acknowledgement<\\/b><\\/p>\\r\\n\\r\\n<p>BY USING SERVICE OR OTHER SERVICES PROVIDED BY US, YOU ACKNOWLEDGE THAT YOU HAVE READ THESE TERMS OF SERVICE AND AGREE TO BE BOUND BY THEM.<\\/p>\\r\\n\\r\\n<p><b>Contact Us<\\/b><\\/p>\\r\\n\\r\\n<p>Please send your feedback, comments, requests for technical support by submitting this <a href=\\\"https:\\/\\/newisty.com\\/contact-us\\\">Contact Us<\\/a><\\/p>\\r\\n\\r\\n<p>This Terms and Condition page has been generated by Newisty using <b><a href=\\\"https:\\/\\/newisty.com\\/terms-and-condition-generator\\\">Free Terms and Conditions Generator<\\/a><\\/b> tool<\\/p>\"}', 'dynamic', 1, 'en', '/page/terms-conditions', '2025-04-05 02:48:43', '2025-05-17 04:35:45'),
(14, 'default', 'Privacy & Policy', 'privacy-policy', '{\"section_id\":\"[]\",\"meta_keywords\":\"Privacy & Policy\",\"meta_description\":\"Privacy & Policy\",\"content\":\"<p><b>Last updated:<\\/b> 17 May, 2025<\\/p>\\r\\n\\r\\n<h2><strong>Privacy Policy for Newisty<\\/strong><\\/h2>\\r\\n\\r\\n<p>At Newisty, accessible from https:\\/\\/newisty.com\\/, one of our main priorities is the privacy of our visitors. This Privacy Policy document contains types of information that is collected and recorded by Newisty and how we use it.<\\/p>\\r\\n\\r\\n<p>If you have additional questions or require more information about our Privacy Policy, do not hesitate to <a href=\\\"https:\\/\\/newisty.com\\\">Contact Us<\\/a>.<\\/p>\\r\\n\\r\\n<p>This Privacy Policy applies only to our online activities and is valid for visitors to our website with regards to the information that they shared and\\/or collect in Newisty. This policy does not applicable to any information collected offline or via channels other than this website.<\\/p>\\r\\n\\r\\n<h2><strong>Consent<\\/strong><\\/h2>\\r\\n\\r\\n<p>By using our website, you hereby consent to our Privacy Policy and agree to its <a href=\\\"https:\\/\\/newisty.com\\\" title=\\\"https:\\/\\/newisty.com\\\">https:\\/\\/newisty.com<\\/a>.<\\/p>\\r\\n\\r\\n<h2><strong>Information we collect<\\/strong><\\/h2>\\r\\n\\r\\n<p>The personal information that you are asked to provide, and the reasons why you are asked to provide it, will be made clear to you at the point we ask you to provide your personal information.<\\/p>\\r\\n\\r\\n<p>If you contact us directly, we may receive additional information about you such as your name, email address, phone number, the contents of the message and\\/or attachments you may send us, and any other information you may choose to provide.<\\/p>\\r\\n\\r\\n<p>When you register for an Account, we may ask for your contact information, including items such as name, company name, address, email address, and telephone number.<\\/p>\\r\\n\\r\\n<h2><strong>How we use your information<\\/strong><\\/h2>\\r\\n\\r\\n<p>We use the information we collect in various ways, including:<\\/p>\\r\\n\\r\\n<p>Provide, operate, and maintain our website<\\/p>\\r\\n\\r\\n<p>Improve, personalize, and expand our website<\\/p>\\r\\n\\r\\n<p>Understand and analyze how you use our website<\\/p>\\r\\n\\r\\n<p>Develop new products, services, features, and functionality Communicate with you, either directly or through one of our partners, including for customer service.<\\/p>\\r\\n\\r\\n<p>To provide you with updates and other information relating to the website, and for marketing and promotional purposes<\\/p>\\r\\n\\r\\n<p>Send you emails.<\\/p>\\r\\n\\r\\n<p>Find and prevent fraud.<\\/p>\\r\\n\\r\\n<h2><strong>Log Files<\\/strong><\\/h2>\\r\\n\\r\\n<p>Newisty follows a standard procedure of using log files. These files log visitors when they visit websites. All hosting companies do this and are a part of hosting services\' analytics. The information collected by log files includes internet protocol (IP) addresses, browser type, Internet Service Provider (ISP), date and time stamp, referring\\/exit pages, and possibly the number of clicks. These are not linked to any information that is personally identifiable. The purpose of the information is for analyzing trends, administering the site, tracking users\' movement on the website, and gathering demographic information.<\\/p>\\r\\n\\r\\n<h2><strong>Cookies and Web Beacons<\\/strong><\\/h2>\\r\\n\\r\\n<p>Like any other website, Newisty uses \'cookies\'. These cookies are used to store information including visitor\'s preferences, and the pages on the website that the visitor accessed or visited. The information is used to optimize the users\' experience by customizing our web page content based on visitors\' browser type and\\/or other information.<\\/p>\\r\\n\\r\\n<p>Advertising Partners Privacy Policies You may consult this list to find the Privacy Policy for each of the advertising partners of Newisty.<\\/p>\\r\\n\\r\\n<p>Third-party ad servers or ad networks uses technologies like cookies, JavaScript, or Web Beacons that are used in their respective advertisements and links that appear on Newisty, which are sent directly to users\' browser. They automatically receive your IP address when this occurs. These technologies are used to measure the effectiveness of their advertising campaigns and\\/or to personalize the advertising content that you see on websites that you visit.<\\/p>\\r\\n\\r\\n<p>Note that Newisty has no access to or control over these cookies that are used by third-party advertisers.<\\/p>\\r\\n\\r\\n<h2><strong>Third-Party Privacy Policies<\\/strong><\\/h2>\\r\\n\\r\\n<p>Newisty\'s Privacy Policy does not apply to other advertisers or websites. Thus, we are advising you to consult the respective Privacy Policies of these third-party ad servers for more detailed information. It may include their practices and instructions about how to opt out of certain options.<\\/p>\\r\\n\\r\\n<p>You can choose to disable cookies through your individual browser options. To know more detailed information about cookie management with specific web browsers, it can be found on the browsers\' respective websites.<\\/p>\\r\\n\\r\\n<p>Here is our Advertising Partners list, privacy policies, and terms<\\/p>\\r\\n\\r\\n<p>\\r\\n\\t\\t\\r\\n\\t\\t\\t\\r\\n\\t\\t\\t\\tAdvertising Partners\\r\\n\\t\\t\\t\\tAdvertising Partners Policies\\r\\n\\t\\t\\t\\r\\n\\t\\t\\r\\n\\t\\t\\r\\n\\t\\t\\t\\r\\n\\t\\t\\t\\tAdvertising Partner Name\\r\\n\\t\\t\\t\\t<a href=\\\"#\\\">Advertising Partner Policy<\\/a>\\r\\n\\t\\t\\t\\r\\n\\t\\t\\r\\n\\t<\\/p>\\r\\n\\r\\n<h2><strong>CCPA Privacy Rights (Do Not Sell My Personal Information)<\\/strong><\\/h2>\\r\\n\\r\\n<p>Under the CCPA, among other rights, California consumers have the right to:<\\/p>\\r\\n\\r\\n<p>Request that a business that collects a consumer\'s personal data disclose the categories and specific pieces of personal data that a business has collected about consumers.<\\/p>\\r\\n\\r\\n<p>Request that a business delete any personal data about the consumer that a business has collected.<\\/p>\\r\\n\\r\\n<p>Request that a business that sells a consumer\'s personal data, not sell the consumer\'s personal data.<\\/p>\\r\\n\\r\\n<p>If you make a request, we have 30 Days to respond to you.<\\/p>\\r\\n\\r\\n<p>If you would like to exercise any of these rights, please <a href=\\\"https:\\/\\/newisty.com\\\">Contact Us<\\/a>.<\\/p>\\r\\n\\r\\n<h2><strong>GDPR Data Protection Rights<\\/strong><\\/h2>\\r\\n\\r\\n<p>We would like to make sure you are fully aware of all of your data protection rights. Every user is entitled to the following:<\\/p>\\r\\n\\r\\n<p>The right to access: You have the right to request copies of your personal data. We may charge you a small fee for this service.<\\/p>\\r\\n\\r\\n<p>The right to rectification: You have the right to request that we correct any information you believe is inaccurate. You also have the right to request that we complete the information you believe is incomplete.<\\/p>\\r\\n\\r\\n<p>The right to erasure: You have the right to request that we erase your personal data, under certain conditions.<\\/p>\\r\\n\\r\\n<p>The right to restrict processing: You have the right to request that we restrict the processing of your personal data, under certain conditions.<\\/p>\\r\\n\\r\\n<p>The right to object to processing: You have the right to object to our processing of your personal data, under certain conditions.<\\/p>\\r\\n\\r\\n<p>The right to data portability: You have the right to request that we transfer the data that we have collected to another organization, or directly to you, under certain conditions.<\\/p>\\r\\n\\r\\n<p>If you make a request, we have 30 Days to respond to you. If you would like to exercise any of these rights, please <a href=\\\"https:\\/\\/newisty.com\\\">Contact Us<\\/a>.<\\/p>\\r\\n\\r\\n<h2><strong>Children\'s Information<\\/strong><\\/h2>\\r\\n\\r\\n<p>Another part of our priority is adding protection for children while using the internet. We encourage parents and guardians to observe, participate in, and\\/or monitor and guide their online activity.<\\/p>\\r\\n\\r\\n<p>Newisty does not knowingly collect any Personal Identifiable Information from children under the age of 13. If you think that your child provided this kind of information on our website, we strongly encourage you to contact us immediately and we will do our best efforts to promptly remove such information from our records.<\\/p>\\r\\n\\r\\n<h2><strong>Revisions to our Privacy Policy<\\/strong><\\/h2>\\r\\n\\r\\n<p>We reserve the right to edit this privacy policy or any part of it from time to time. We may notify you or not. Please review the policy periodically for changes.<\\/p>\\r\\n\\r\\n<p>This Privacy Policy page has been generated by Newisty using <b><a href=\\\"https:\\/\\/newisty.com\\/privacy-policy-generator\\\">Privacy Policy Generator<\\/a><\\/b> tool<\\/p>\"}', 'dynamic', 1, 'en', '/page/privacy-policy', '2025-04-05 03:02:29', '2025-05-17 04:36:23'),
(28, 'default', 'Pricing', 'pricing', '{\"meta_keywords\":\"How It Works\",\"meta_description\":\"How It Works\",\"section_id\":\"[\\\"16\\\",\\\"22\\\"]\",\"content\":\"\",\"title\":\"[[color_text= Flexible Mining]]  Solutions Tailored to Your Needs\",\"sub_title\":\"Choose the right plan for efficient and cost-effective mining operations.\"}', 'dynamic', 1, 'en', 'pricing', '2025-04-27 04:13:11', '2025-05-28 23:21:49'),
(53, 'default', 'Usage Policy', 'usage-policy', '{\"meta_keywords\":\"\",\"meta_description\":\"\",\"section_id\":\"[]\",\"content\":\"\\r\\n\\r\\n\\r\\n\\r\\n<div>\\r\\n        <h1>Contact Us - OreXcoin<\\/h1>\\r\\n        <p>If you have any questions, comments, or concerns related to OreXcoin, we are here to help. Our customer support team is available to assist you through multiple channels, so you can choose the one that works best for you.<\\/p>\\r\\n\\r\\n        <div>\\r\\n            <div>\\r\\n                <div>\\r\\n                    \\r\\n                    <span>Email Support<\\/span>\\r\\n                    <div>You can send us an email at <a href=\\\"mailto:<EMAIL>\\\"><EMAIL><\\/a> and we\\u2019ll get back to you as soon as possible. Our team is available to answer your questions and provide assistance with any issues you may be experiencing on OreXcoin.<\\/div>\\r\\n                <\\/div>\\r\\n            <\\/div> <p>We value your feedback and are committed to providing you with the best possible service on OreXcoin. If you have any suggestions on how we can improve our website or services, please let us know. Our team is always looking for ways to enhance the customer experience on OreXcoin and we appreciate any feedback you can provide.<\\/p>\\r\\n\\r\\n        <p>Thank you for choosing OreXcoin. We look forward to hearing from you!<\\/p><\\/div><\\/div>\"}', 'dynamic', 1, 'en', '/page/usage-policy', '2025-06-01 22:21:27', '2025-06-01 22:21:27');

-- --------------------------------------------------------

--
-- Table structure for table `page_settings`
--

CREATE TABLE `page_settings` (
  `id` bigint UNSIGNED NOT NULL,
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `page_settings`
--

INSERT INTO `page_settings` (`id`, `key`, `value`, `created_at`, `updated_at`) VALUES
(1, 'username_show', '1', '2025-04-06 04:43:02', '2025-04-06 04:43:02'),
(2, 'username_validation', '1', '2025-04-06 04:43:02', '2025-04-30 09:21:29'),
(3, 'phone_show', '1', '2025-04-06 04:43:02', '2025-04-06 04:43:02'),
(4, 'phone_validation', '1', '2025-04-06 04:43:02', '2025-04-06 05:04:11'),
(5, 'country_show', '1', '2025-04-06 04:43:02', '2025-04-09 10:52:39'),
(6, 'country_validation', '0', '2025-04-06 04:43:02', '2025-06-03 04:27:41'),
(7, 'referral_code_show', '1', '2025-04-06 04:43:02', '2025-04-06 04:43:02'),
(8, 'referral_code_validation', '0', '2025-04-06 04:43:02', '2025-04-06 04:43:02'),
(9, 'gender_show', '1', '2025-04-06 04:43:02', '2025-04-06 04:43:02'),
(10, 'gender_validation', '0', '2025-04-06 04:43:02', '2025-04-06 04:59:25'),
(11, 'merchant_username_show', '0', '2025-04-10 03:44:59', '2025-04-10 04:12:26'),
(12, 'merchant_username_validation', '0', '2025-04-10 03:44:59', '2025-04-10 03:44:59'),
(13, 'merchant_phone_show', '1', '2025-04-10 03:44:59', '2025-04-10 03:44:59'),
(14, 'merchant_phone_validation', '0', '2025-04-10 03:44:59', '2025-04-10 03:44:59'),
(15, 'merchant_country_show', '1', '2025-04-10 03:44:59', '2025-04-10 03:44:59'),
(16, 'merchant_country_validation', '0', '2025-04-10 03:44:59', '2025-04-10 03:44:59'),
(17, 'merchant_gender_show', '1', '2025-04-10 03:44:59', '2025-04-10 03:53:40'),
(18, 'merchant_gender_validation', '0', '2025-04-10 03:44:59', '2025-04-10 03:44:59'),
(19, 'agent_username_show', '1', '2025-04-10 04:53:43', '2025-04-10 04:53:43'),
(20, 'agent_username_validation', '0', '2025-04-10 04:53:43', '2025-04-10 04:53:43'),
(21, 'agent_phone_show', '1', '2025-04-10 04:53:43', '2025-04-10 04:53:43'),
(22, 'agent_phone_validation', '0', '2025-04-10 04:53:43', '2025-04-10 04:53:43'),
(23, 'agent_country_show', '1', '2025-04-10 04:53:43', '2025-04-10 04:53:43'),
(24, 'agent_country_validation', '0', '2025-04-10 04:53:43', '2025-04-10 04:53:43'),
(25, 'agent_gender_show', '1', '2025-04-10 04:53:43', '2025-04-10 04:53:43'),
(26, 'agent_gender_validation', '0', '2025-04-10 04:53:43', '2025-04-10 04:53:43');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`email`, `token`, `created_at`) VALUES
('<EMAIL>', 'gGVmo5zRw71SeAsJE5d7rXqMiIyZlL4eWGkXlnhs7YKev0dH6wfKkHkIVhiPlOoB', '2025-04-05 08:34:30'),
('<EMAIL>', 'QEFcll5k2rhSNBRsvhAbLNHWnDEaDrRcafjCyjcJupNtYT32KIr2UvY29cNVL6wy', '2025-04-05 08:36:14'),
('<EMAIL>', 'VyMx7cdZvfNgPlAfXqzYdk6xw0C8CO8ajL0KnYaigQlF2Hy3MDx2FnBMlhWdFAuy', '2025-04-05 08:37:46'),
('<EMAIL>', 'X60kQovDRM0FAksZgxiYNfEJhuoM9PDzUm97zkAINbarIxqVBYCEONoJF0KpWYmo', '2025-04-05 08:41:42');

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint UNSIGNED NOT NULL,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `permissions`
--

INSERT INTO `permissions` (`id`, `category`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'Statistics Management', 'total-users', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(2, 'Statistics Management', 'total-agents', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(3, 'Statistics Management', 'total-merchants', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(4, 'Statistics Management', 'all-deposits', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(5, 'Statistics Management', 'all-currencies', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(6, 'Statistics Management', 'total-staff', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(7, 'Statistics Management', 'total-withdraw', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(8, 'Statistics Management', 'total-referral', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(9, 'Statistics Management', 'total-automatic-gateway', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(10, 'Statistics Management', 'total-ticket', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(11, 'Statistics Management', 'total-transfer', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(12, 'Statistics Management', 'total-cashout', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(13, 'Statistics Management', 'total-payments', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(14, 'Statistics Management', 'site-statistics-chart', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(15, 'Statistics Management', 'top-country-statistics', 'admin', '2025-04-30 04:08:17', '2025-04-30 04:08:17'),
(16, 'Statistics Management', 'top-browser-statistics', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(17, 'Statistics Management', 'top-os-statistics', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(18, 'Statistics Management', 'latest-users', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(19, 'Statistics Management', 'latest-merchants', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(20, 'Statistics Management', 'latest-agents', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(21, 'Customer Management', 'customer-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(22, 'Customer Management', 'customer-login', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(23, 'Customer Management', 'customer-mail-send', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(24, 'Customer Management', 'customer-basic-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(25, 'Customer Management', 'customer-balance-add-or-subtract', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(26, 'Customer Management', 'customer-change-password', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(27, 'Customer Management', 'all-type-status', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(28, 'Merchant Management', 'merchant-requests', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(29, 'Merchant Management', 'merchant-form', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(30, 'Merchant Management', 'merchant-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(31, 'Merchant Management', 'merchant-login', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(32, 'Merchant Management', 'merchant-mail-send', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(33, 'Merchant Management', 'merchant-basic-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(34, 'Merchant Management', 'merchant-balance-add-or-subtract', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(35, 'Merchant Management', 'merchant-change-password', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(36, 'Merchant Management', 'merchant-all-type-status', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(37, 'Merchant Management', 'merchant-delete', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(38, 'Merchant Management', 'merchant-kyc-info', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(39, 'Agent Management', 'agent-requests', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(40, 'Agent Management', 'agent-form', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(41, 'Agent Management', 'agent-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(42, 'Agent Management', 'agent-login', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(43, 'Agent Management', 'agent-mail-send', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(44, 'Agent Management', 'agent-basic-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(45, 'Agent Management', 'agent-balance-add-or-subtract', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(46, 'Agent Management', 'agent-change-password', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(47, 'Agent Management', 'agent-all-type-status', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(48, 'Agent Management', 'agent-delete', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(49, 'Agent Management', 'agent-kyc-info', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(50, 'Kyc Management', 'kyc-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(51, 'Kyc Management', 'kyc-action', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(52, 'Kyc Management', 'kyc-form-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(53, 'Role Management', 'role-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(54, 'Role Management', 'role-create', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(55, 'Role Management', 'role-edit', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(56, 'Staff Management', 'staff-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(57, 'Staff Management', 'staff-create', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(58, 'Staff Management', 'staff-edit', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(59, 'Transaction Management', 'transaction-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(60, 'Deposit Management', 'automatic-gateway-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(61, 'Deposit Management', 'manual-gateway-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(62, 'Deposit Management', 'deposit-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(63, 'Deposit Management', 'deposit-action', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(64, 'Withdraw Management', 'withdraw-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(65, 'Withdraw Management', 'withdraw-method-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(66, 'Withdraw Management', 'withdraw-action', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(67, 'Withdraw Management', 'withdraw-schedule', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(68, 'Referral Management', 'manage-referral', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(69, 'Referral Management', 'referral-create', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(70, 'Referral Management', 'referral-edit', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(71, 'Referral Management', 'referral-delete', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(72, 'Frontend Management', 'landing-page-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(73, 'Frontend Management', 'footer-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(74, 'Frontend Management', 'navigation-manage', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(75, 'Frontend Management', 'custom-css', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(76, 'Subscriber Management', 'subscriber-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(77, 'Subscriber Management', 'subscriber-mail-send', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(78, 'Support Ticket Management', 'support-ticket-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(79, 'Support Ticket Management', 'support-ticket-action', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(80, 'Setting Management', 'site-setting', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(81, 'Setting Management', 'email-setting', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(82, 'Setting Management', 'plugin-setting', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(83, 'Setting Management', 'currencies-setting', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(84, 'Setting Management', 'language-setting', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(85, 'Setting Management', 'page-setting', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(86, 'Setting Management', 'notification-tune-setting', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(87, 'Template Management', 'template-list', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(88, 'Template Management', 'template-edit', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(89, 'Template Management', 'template-update', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(90, 'Template Management', 'template-delete', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(91, 'System Management', 'manage-cron-job', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(92, 'System Management', 'cron-job-create', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(93, 'System Management', 'cron-job-edit', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(94, 'System Management', 'cron-job-delete', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(95, 'System Management', 'cron-job-logs', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(96, 'System Management', 'cron-job-run', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(97, 'System Management', 'clear-cache', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18'),
(98, 'System Management', 'application-details', 'admin', '2025-04-30 04:08:18', '2025-04-30 04:08:18');

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plugins`
--

CREATE TABLE `plugins` (
  `id` bigint UNSIGNED NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'system',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(196) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plugins`
--

INSERT INTO `plugins` (`id`, `icon`, `type`, `name`, `description`, `data`, `status`, `created_at`, `updated_at`) VALUES
(1, 'global/plugin/tawk.png', 'system', 'Tawk Chat', 'Free Instant Messaging system\n\n', '{\"widget_id\":\"1ggi9vm2o\",\"property_id\":\"635d5805b0d6371309cc36aa\"}', 0, NULL, '2023-09-02 11:27:02'),
(2, 'global/plugin/reCaptcha.png', 'system', 'Google reCaptcha', 'reCAPTCHA protects your website from fraud and abuse without creating friction\r\n\r\n\r\n\r\n', '{\"site_key\":\"6LdY0AgjAAAAAIe6cwoa8ReDAv-J0gCGMnwF9rDu\",\"secret_key\":\"6LdY0AgjAAAAAF6yK-wkguwwRVQB6AJmCS_QTl0P\"}', 0, NULL, '2024-06-09 00:46:55'),
(3, 'global/plugin/analytics.png', 'system', 'Google Analytics', 'Analytics will help you to collect data for your website\r\n\r\n\r\n\r\n', '{\"app_id\":\"G-XXXXX\"}', 0, NULL, '2025-03-18 05:00:42'),
(4, 'global/plugin/fb.png', 'system', 'Facebook Messenger', 'Messenger is a proprietary instant messaging app and platform developed by Meta\n\n\n', '{\"page_id\":\"990335491009901\"}', 0, NULL, '2023-02-09 16:53:13'),
(5, 'global/plugin/vonage.png', 'sms', 'Vonage', 'Vonage API (formerly Nexmo) provides cloud-based SMS API for seamless communication integration.\n\n', '{\"from\":\"8801887094529\",\"api_key\":\"d67bcc94\",\"api_secret\":\"Gu5QVwrCZRSORjOs\"}', 0, NULL, '2023-12-26 00:56:45'),
(6, 'global/plugin/twilio.png', 'sms', 'Twillo', 'Build agility into your customer engagement\r\n\r\n\r\n', '{\"twilio_sid\":\"**********************************\",\"twilio_auth_token\":\"7b97976578e99b93d1dd1a0f2fbf0849\",\"twilio_phone\":\"+19292426081\"}', 0, NULL, '2023-12-31 04:26:09'),
(7, 'global/plugin/pusher.png', 'notification', 'Pusher', 'Leader In Realtime Technologies\r\n\r\n\r\n', '{\"pusher_app_id\":\"\",\"pusher_app_key\":\"\",\"pusher_app_secret\":\"\",\"pusher_app_cluster\":\"ap1\"}', 0, NULL, '2025-04-29 05:40:00');

-- --------------------------------------------------------

--
-- Table structure for table `portfolios`
--

CREATE TABLE `portfolios` (
  `id` bigint UNSIGNED NOT NULL,
  `level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `portfolio_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `minimum_transactions` int NOT NULL,
  `bonus` int NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `portfolio_features`
--

CREATE TABLE `portfolio_features` (
  `id` bigint UNSIGNED NOT NULL,
  `portfolio_id` bigint UNSIGNED NOT NULL,
  `discount_maintenance_type` enum('fixed','percentage') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `discount_amount_maintenance` float(10,2) NOT NULL DEFAULT '0.00',
  `discount_plan_purchase_type` enum('fixed','percentage') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `discount_amount_plan_purchase` float(10,2) NOT NULL DEFAULT '0.00',
  `boost_return_amount_type` enum('fixed','percentage') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `boost_amount_return_amount` float(10,2) NOT NULL DEFAULT '0.00',
  `boost_referral_bonus_type` enum('fixed','percentage') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `boost_amount_referral_bonus` float(10,2) NOT NULL DEFAULT '0.00',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'Super-Admin', 'admin', '2024-11-19 22:45:34', '2024-11-19 22:45:34');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint UNSIGNED NOT NULL,
  `role_id` bigint UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(1, 1),
(2, 1),
(3, 1),
(4, 1),
(5, 1),
(6, 1),
(7, 1),
(8, 1),
(9, 1),
(10, 1),
(11, 1),
(12, 1),
(13, 1),
(14, 1),
(15, 1),
(16, 1),
(17, 1),
(18, 1),
(19, 1),
(20, 1),
(21, 1),
(22, 1),
(23, 1),
(24, 1),
(25, 1),
(26, 1),
(27, 1),
(28, 1),
(29, 1),
(30, 1),
(31, 1),
(32, 1),
(33, 1),
(34, 1),
(35, 1),
(36, 1),
(37, 1),
(38, 1),
(39, 1),
(40, 1),
(41, 1),
(42, 1),
(43, 1),
(44, 1),
(45, 1),
(46, 1),
(47, 1),
(48, 1),
(49, 1),
(50, 1),
(51, 1),
(52, 1),
(53, 1),
(54, 1),
(55, 1),
(56, 1),
(57, 1),
(58, 1),
(59, 1),
(60, 1),
(61, 1),
(62, 1),
(63, 1),
(64, 1),
(65, 1),
(66, 1),
(67, 1),
(68, 1),
(69, 1);

-- --------------------------------------------------------

--
-- Table structure for table `schedules`
--

CREATE TABLE `schedules` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `time` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `schemes`
--

CREATE TABLE `schemes` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `miner_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `price` decimal(20,8) NOT NULL,
  `return_amount_type` enum('fixed','min_max') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `return_amount_value` decimal(20,8) DEFAULT NULL,
  `return_min_amount` decimal(20,8) DEFAULT NULL,
  `return_max_amount` decimal(20,8) DEFAULT NULL,
  `return_period_type` enum('period','lifetime') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `return_period` int UNSIGNED DEFAULT NULL,
  `return_period_hours` int NOT NULL DEFAULT '0',
  `return_period_max_number` int UNSIGNED DEFAULT NULL,
  `speed` enum('hash/s','Khash/s','Mhash/s','Ghash/s','Thash/s','Phash/s','Ehash/s','Zhash/s','Yhash/s','Rhash/s','Qhash/s') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `speed_amount` decimal(20,8) DEFAULT '0.********',
  `max_mining_amount` decimal(20,8) DEFAULT '0.********',
  `is_featured` tinyint(1) DEFAULT '0',
  `features` json DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `holidays` json DEFAULT NULL,
  `maintenance_fee_type` enum('percentage','fixed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'fixed',
  `maintenance_fee_amount` double(20,8) NOT NULL DEFAULT '0.********',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('RtqIOF0N1LFBxfglDvadpT6cdcJkp6W2m29uHR3h', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoialFydGtEdjlldXpQb0h0Q3BTVXlIZDB5UzV1b01XVzRQNDVUdmF5QiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzg6Imh0dHBzOi8vb3JleGNvaW4tdGVtcC50ZXN0L21haW4vRk9MREVSIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czoxMDoidGhlbWVfbW9kZSI7czo1OiJsaWdodCI7fQ==', 1749893739),
('w4Q6HVUfZYacjoUu3vxeRQe2Yap3shFdKkm848qm', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiaW1zanI3aUloUWRHWDdvZjZBeU5zd2x1TjF5NktKN2ptVjAwUTlaciI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzg6Imh0dHBzOi8vb3JleGNvaW4tdGVtcC50ZXN0L21haW4vRk9MREVSIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1749893200);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `val` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'string',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `name`, `val`, `type`, `created_at`, `updated_at`) VALUES
(1, 'site_logo', 'global/uploads/global/uploads/settings//UpUP9iVJ2oHtSMj2M9r0.png', 'string', '2024-11-19 22:45:36', '2025-05-19 03:44:41'),
(2, 'site_logo_height', '35px', 'string', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(3, 'site_logo_width', '35px', 'string', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(4, 'site_favicon', 'global/uploads/global/uploads/settings//KkDGl9zKPmYVE5DeVwgl.png', 'string', '2024-11-19 22:45:36', '2025-05-19 03:43:47'),
(5, 'login_bg', 'global/uploads/global/uploads/settings//aAD3se0DN8DVckXkGBEk.jpg', 'string', '2024-11-19 22:45:36', '2025-03-24 02:36:51'),
(6, 'site_admin_prefix', 'admin', 'string', '2024-11-19 22:45:36', '2025-04-16 11:58:11'),
(7, 'site_title', 'OreXcoin', 'string', '2024-11-19 22:45:36', '2025-05-19 03:42:12'),
(8, 'account_number_prefix', 'DGB', 'string', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(9, 'site_currency', 'USD', 'string', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(10, 'currency_symbol', '$', 'string', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(11, 'site_timezone', 'UTC', 'string', '2024-11-19 22:45:36', '2025-04-26 10:10:51'),
(12, 'referral_code_limit', '6', 'integer', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(13, 'account_no_limit', '10', 'integer', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(14, 'home_redirect', '/', 'string', '2024-11-19 22:45:36', '2025-06-02 09:05:36'),
(15, 'site_email', '<EMAIL>', 'string', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(16, 'support_email', '<EMAIL>', 'string', '2024-11-19 22:45:36', '2024-11-21 04:58:29'),
(17, 'referral_rules_visibility', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(18, 'deposit_level', '1', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(19, 'multiple_currency', '1', 'boolean', '2024-11-19 22:45:36', '2025-04-17 05:24:33'),
(20, 'transfer_status', '1', 'boolean', '2024-11-19 22:45:36', '2024-11-25 03:24:02'),
(21, 'email_verification', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:21:20'),
(22, 'kyc_verification', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-03 04:18:37'),
(23, 'fa_verification', '0', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:43:17'),
(24, 'otp_verification', '1', 'boolean', '2024-11-19 22:45:36', '2024-11-25 03:24:02'),
(25, 'account_creation', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(26, 'user_deposit', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(27, 'user_portfolio', '1', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(28, 'user_withdraw', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(29, 'user_pay_bill', '1', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(30, 'sign_up_referral', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(31, 'referral_signup_bonus', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(32, 'site_animation', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(33, 'back_to_top', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(34, 'language_switcher', '1', 'boolean', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(35, 'default_mode', 'light', 'string', '2024-11-19 22:45:36', '2025-06-02 11:35:50'),
(36, 'debug_mode', '0', 'boolean', '2024-11-19 22:45:36', '2025-06-03 08:53:31'),
(37, 'session_lifetime', '120', 'string', '2024-11-19 22:45:36', '2025-04-24 05:02:00'),
(38, 'referral_bonus', '20', 'double', '2024-11-19 22:45:36', '2024-11-21 05:56:54'),
(39, 'signup_bonus', '20', 'double', '2024-11-19 22:45:36', '2024-11-21 05:56:54'),
(40, 'transfer_min_amount', '10', 'double', '2024-11-19 22:45:36', '2024-11-21 05:56:54'),
(41, 'transfer_max_amount', '20000', 'double', '2024-11-19 22:45:36', '2024-11-21 05:56:54'),
(42, 'fund_transfer_charge', '4', 'double', '2024-11-19 22:45:36', '2024-11-21 05:56:54'),
(43, 'fund_transfer_charge_type', 'percentage', 'double', '2024-11-19 22:45:36', '2024-11-21 05:56:54'),
(44, 'withdraw_day_limit', '20', 'double', '2024-11-19 22:45:36', '2024-11-21 05:56:54'),
(45, 'kyc_deposit', '1', 'boolean', '2024-11-19 22:45:36', '2024-11-25 03:17:07'),
(46, 'kyc_fund_transfer', '1', 'boolean', '2024-11-19 22:45:36', '2024-11-25 03:21:54'),
(47, 'kyc_dps', '0', 'boolean', '2024-11-19 22:45:36', '2024-11-25 03:17:07'),
(48, 'kyc_fdr', '0', 'boolean', '2024-11-19 22:45:36', '2024-11-25 03:17:07'),
(49, 'kyc_loan', '0', 'boolean', '2024-11-19 22:45:36', '2024-11-25 03:17:07'),
(50, 'kyc_pay_bill', '1', 'boolean', '2024-11-19 22:45:36', '2024-12-01 02:45:56'),
(51, 'kyc_withdraw', '1', 'boolean', '2024-11-19 22:45:36', '2025-04-24 05:02:32'),
(52, 'deposit_passcode_status', '0', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(53, 'fund_transfer_passcode_status', '0', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(54, 'dps_passcode_status', '0', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(55, 'fdr_passcode_status', '0', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(56, 'loan_passcode_status', '0', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(57, 'pay_bill_passcode_status', '0', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(58, 'withdraw_passcode_status', '0', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(59, 'inactive_account_disabled', '0', 'string', '2024-11-19 22:45:36', '2025-04-24 05:02:08'),
(60, 'inactive_days', '30', 'string', '2024-11-19 22:45:36', '2025-04-24 05:01:16'),
(61, 'inactive_account_fees', '1', 'switch', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(62, 'fee_amount', '5', 'double', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(63, 'email_from_name', 'Tdevs', 'string', '2024-11-19 22:45:36', '2025-03-04 05:54:45'),
(64, 'email_from_address', '<EMAIL>', 'string', '2024-11-19 22:45:36', '2025-03-04 05:55:48'),
(65, 'mailing_driver', 'smtp', 'string', '2024-11-19 22:45:36', '2025-03-04 05:54:45'),
(66, 'mail_username', 'bb9ee8c66e312a', 'string', '2024-11-19 22:45:36', '2025-03-04 05:54:45'),
(67, 'mail_password', 'a03043c580754b', 'string', '2024-11-19 22:45:36', '2025-03-04 05:54:45'),
(68, 'mail_host', 'localhost', 'string', '2024-11-19 22:45:36', '2025-05-21 05:26:46'),
(69, 'mail_port', '1025', 'integer', '2024-11-19 22:45:36', '2025-05-21 05:26:46'),
(70, 'mail_secure', 'tls', 'string', '2024-11-19 22:45:36', '2025-03-04 05:54:45'),
(71, 'suggested_regular_license_price_from', '10', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(72, 'suggested_regular_license_price_to', '15', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(73, 'suggested_extended_license_price_from', '20', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(74, 'suggested_extended_license_price_to', '50', 'checkbox', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(75, 'maintenance_mode', '0', 'boolean', '2024-11-19 22:45:36', '2025-05-17 09:58:07'),
(76, 'secret_key', 'secret', 'string', '2024-11-19 22:45:36', '2025-04-15 11:20:34'),
(77, 'maintenance_title', 'Site is not under maintenance', 'string', '2024-11-19 22:45:36', '2025-04-30 06:10:04'),
(78, 'maintenance_text', 'Sorry for interrupt! Site will live soon.', 'string', '2024-11-19 22:45:36', '2025-04-15 11:20:34'),
(79, 'gdpr_status', '1', 'boolean', '2024-11-19 22:45:36', '2024-11-21 02:33:49'),
(80, 'gdpr_text', 'Please allow us to collect data about how you use our website. We will use it to improve our website, make your browsing experience and our business decisions better.', 'string', '2024-11-19 22:45:36', '2025-03-27 03:50:12'),
(81, 'gdpr_button_label', 'Learn More', 'string', '2024-11-19 22:45:36', '2024-11-21 02:33:49'),
(82, 'gdpr_button_url', '/page/privacy-policy', 'string', '2024-11-19 22:45:36', '2025-04-24 05:16:21'),
(83, 'meta_description', 'OrexCoin is a modern cryptocurrency mining and earnings system designed to help users generate passive income through digital assets. Our platform offers secure mining solutions, investment opportunities, and comprehensive cryptocurrency management tools.', 'string', '2024-11-19 22:45:36', '2025-05-30 04:48:24'),
(84, 'meta_keywords', 'orexcoin, cryptocurrency mining, crypto earnings, bitcoin mining, ethereum mining, passive income, digital assets, blockchain technology, crypto investment, mining platform, crypto wallet', 'string', '2024-11-19 22:45:36', '2025-05-30 04:48:24'),
(85, 'affiliate_commission_charge', '4', 'text', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(86, 'affiliate_commission_charge_type', 'percentage', 'text', '2024-11-19 22:45:36', '2024-11-19 22:45:36'),
(87, 'exchange_charge', '2', 'double', '2024-11-27 02:49:27', '2025-02-27 12:03:06'),
(88, 'exchange_charge_type', 'percentage', 'double', '2024-11-27 02:49:27', '2025-02-27 12:03:06'),
(89, 'cashout_charge', '5', 'double', '2025-02-20 03:37:57', '2025-02-27 09:14:04'),
(90, 'cashout_minimum', '100', 'double', '2025-02-20 03:37:58', '2025-02-27 09:12:02'),
(91, 'cashout_maximum', '10000', 'double', '2025-02-20 03:37:58', '2025-02-20 03:37:58'),
(92, 'cashout_daily_limit', '1000', 'double', '2025-02-20 03:37:58', '2025-02-27 10:02:29'),
(93, 'cashout_monthly_limit', '10000', 'double', '2025-02-20 03:37:58', '2025-02-27 09:44:40'),
(94, 'cashout_charge_type', 'percentage', 'double', '2025-02-20 03:40:15', '2025-02-27 09:14:54'),
(95, 'gift_charge', '10', 'double', '2025-02-22 04:20:22', '2025-02-22 04:20:51'),
(96, 'gift_charge_type', 'fixed', 'double', '2025-02-22 04:20:22', '2025-02-26 08:41:30'),
(97, 'gift_minimum', '100', 'double', '2025-02-22 04:20:22', '2025-02-22 04:20:22'),
(98, 'gift_maximum', '1000', 'double', '2025-02-22 04:20:22', '2025-02-22 04:20:39'),
(99, 'gift_daily_limit', '10', 'double', '2025-02-22 04:20:22', '2025-02-22 08:56:39'),
(100, 'invoice_charge', '2', 'double', '2025-02-22 08:29:54', '2025-02-22 08:45:24'),
(101, 'invoice_charge_type', 'fixed', 'double', '2025-02-22 08:29:54', '2025-02-22 08:45:19'),
(102, 'invoice_daily_limit', '10', 'double', '2025-02-22 08:29:54', '2025-02-22 08:30:13'),
(103, 'exchange_daily_limit', '20', 'double', '2025-02-22 08:45:12', '2025-02-22 08:45:12'),
(104, 'cashout_agent_commission', '2', 'double', '2025-02-22 09:16:37', '2025-02-22 09:16:46'),
(105, 'cashout_agent_commission_type', 'percentage', 'double', '2025-02-22 09:16:37', '2025-02-22 09:16:40'),
(106, 'api_payment_charge', '5', 'double', '2025-02-23 04:37:37', '2025-02-23 04:37:37'),
(107, 'api_payment_charge_type', 'fixed', 'double', '2025-02-23 04:37:37', '2025-02-23 04:37:37'),
(108, 'user_make_payment_charge', '5', 'double', '2025-02-24 09:11:25', '2025-02-24 09:11:25'),
(109, 'user_make_payment_charge_type', 'percentage', 'double', '2025-02-24 09:11:25', '2025-02-24 09:37:26'),
(110, 'merchant_make_payment_charge', '2', 'double', '2025-02-24 09:11:25', '2025-02-24 10:05:50'),
(111, 'merchant_make_payment_charge_type', 'fixed', 'double', '2025-02-24 09:11:25', '2025-02-24 10:05:50'),
(112, 'request_money_charge', '5', 'double', '2025-02-26 04:53:12', '2025-02-26 04:53:12'),
(113, 'request_money_charge_type', 'percentage', 'double', '2025-02-26 04:53:12', '2025-02-26 04:53:12'),
(114, 'request_money_daily_limit', '10', 'double', '2025-02-26 04:53:12', '2025-02-26 04:53:12'),
(115, 'transfer_charge', '5', 'double', '2025-02-27 04:49:05', '2025-02-27 04:49:05'),
(116, 'transfer_charge_type', 'percentage', 'double', '2025-02-27 04:49:06', '2025-02-27 04:49:06'),
(117, 'transfer_minimum', '10', 'double', '2025-02-27 04:49:06', '2025-02-27 04:49:06'),
(118, 'transfer_maximum', '1000', 'double', '2025-02-27 04:49:06', '2025-02-27 04:49:06'),
(119, 'transfer_daily_limit', '10000', 'double', '2025-02-27 04:49:06', '2025-02-27 04:49:09'),
(120, 'site_currency_decimals', '4', 'string', '2025-03-06 03:06:55', '2025-05-31 05:25:44'),
(121, 'deposit', '1', 'boolean', '2025-03-06 06:09:34', '2025-03-06 06:59:30'),
(122, 'transfer', '1', 'boolean', '2025-03-06 06:11:00', '2025-03-06 06:41:06'),
(123, 'cash_out', '1', 'boolean', '2025-03-06 06:11:02', '2025-03-06 06:11:02'),
(124, 'invoice_pay', '1', 'boolean', '2025-03-06 06:11:04', '2025-03-06 06:58:52'),
(125, 'exchange', '1', 'boolean', '2025-03-06 06:11:05', '2025-03-06 06:58:54'),
(126, 'create_gift', '1', 'boolean', '2025-03-06 06:11:06', '2025-03-06 06:58:56'),
(127, 'request_money_accept', '1', 'boolean', '2025-03-06 06:11:08', '2025-03-06 06:58:51'),
(128, 'payment', '1', 'boolean', '2025-03-06 06:11:11', '2025-03-06 06:58:50'),
(129, 'referral_rules', '[{\"icon\":\"tick\",\"rule\":\"Referrer gets a reward when the referred user makes a successful first deposit.\"},{\"icon\":\"cross\",\"rule\":\"No reward if the referred user fails KYC or cancels the deposit.\"}]', 'string', '2025-03-06 06:44:52', '2025-04-15 11:45:50'),
(130, 'preloader', '0', 'boolean', '2025-04-06 05:33:36', '2025-04-28 06:51:20'),
(131, 'kyc_cashout', '1', 'boolean', '2025-04-10 06:08:59', '2025-04-10 06:08:59'),
(132, 'kyc_exchange', '1', 'boolean', '2025-04-10 06:08:59', '2025-04-10 06:08:59'),
(133, 'kyc_payment', '1', 'boolean', '2025-04-10 06:08:59', '2025-04-10 06:08:59'),
(134, 'kyc_request_money', '1', 'boolean', '2025-04-10 06:08:59', '2025-04-10 06:08:59'),
(135, 'kyc_create_gift', '1', 'boolean', '2025-04-10 06:08:59', '2025-04-10 06:08:59'),
(136, 'kyc_invoice', '1', 'boolean', '2025-04-10 06:08:59', '2025-04-10 06:08:59'),
(137, 'kyc_gift', '1', 'boolean', '2025-04-10 06:09:33', '2025-04-10 06:09:33'),
(138, 'user_transfer', '1', 'boolean', '2025-04-10 06:14:17', '2025-04-10 06:14:17'),
(139, 'user_cashout', '1', 'boolean', '2025-04-10 06:14:17', '2025-04-10 06:14:17'),
(140, 'user_gift', '1', 'boolean', '2025-04-10 06:14:17', '2025-04-10 06:14:17'),
(141, 'user_payment', '1', 'boolean', '2025-04-10 06:14:17', '2025-04-10 06:14:17'),
(142, 'user_invoice', '1', 'boolean', '2025-04-10 06:14:17', '2025-04-17 05:06:15'),
(143, 'user_request_money', '1', 'boolean', '2025-04-10 06:14:17', '2025-04-10 06:16:42'),
(144, 'user_exchange', '1', 'boolean', '2025-04-10 06:14:17', '2025-04-10 06:19:18'),
(145, 'user_ticket', '0', 'boolean', '2025-04-10 06:14:17', '2025-06-03 05:46:53'),
(146, 'kyc_wallet', '1', 'boolean', '2025-04-10 06:30:30', '2025-04-10 09:55:02'),
(147, 'merchant_system', '1', 'boolean', '2025-04-12 05:48:35', '2025-04-17 03:48:00'),
(148, 'agent_system', '1', 'boolean', '2025-04-12 06:20:27', '2025-04-12 10:55:13'),
(149, 'preloader_logo', 'global/uploads/global/uploads/settings//B9z3oV89FRgOrapcKdVc.png', 'string', '2025-04-15 10:40:25', '2025-05-19 03:43:47'),
(150, 'merchant_verification', '0', 'boolean', '2025-04-16 05:57:19', '2025-04-16 06:31:15'),
(151, 'agent_verification', '0', 'boolean', '2025-04-16 05:57:19', '2025-04-16 06:31:15'),
(152, 'cashin_charge', '1', 'double', '2025-04-17 06:12:12', '2025-04-17 06:12:12'),
(153, 'cashin_charge_type', 'percentage', 'double', '2025-04-17 06:12:12', '2025-04-17 06:12:12'),
(154, 'cashin_agent_commission', '2', 'double', '2025-04-17 06:12:12', '2025-04-17 06:12:12'),
(155, 'cashin_agent_commission_type', 'percentage', 'double', '2025-04-17 06:12:12', '2025-04-17 06:12:12'),
(156, 'cashin_minimum', '10', 'double', '2025-04-17 06:12:12', '2025-04-17 06:12:12'),
(157, 'cashin_maximum', '10000', 'double', '2025-04-17 06:12:12', '2025-04-17 06:12:12'),
(158, 'cashin_daily_limit', '1000', 'double', '2025-04-17 06:12:12', '2025-04-17 06:52:54'),
(159, 'cashin_monthly_limit', '1000', 'double', '2025-04-17 06:12:12', '2025-04-17 06:12:12'),
(160, 'payment_minimum', '1', 'double', '2025-04-27 04:05:03', '2025-04-27 04:05:07'),
(161, 'payment_maximum', '1000', 'double', '2025-04-27 04:05:03', '2025-04-27 04:05:11'),
(162, 'exchange_minimum', '10', 'double', '2025-04-27 05:50:34', '2025-04-27 05:50:34'),
(163, 'exchange_maximum', '100000', 'double', '2025-04-27 05:50:34', '2025-04-27 05:50:34'),
(164, 'request_money_minimum', '10', 'double', '2025-04-27 06:16:47', '2025-04-27 06:16:47'),
(165, 'request_money_maximum', '10000', 'double', '2025-04-27 06:16:47', '2025-04-27 06:16:47'),
(166, 'purchase', '1', 'boolean', '2025-05-20 08:21:16', '2025-05-20 08:21:20'),
(167, 'plan_purchase', '1', 'boolean', '2025-05-20 11:32:06', '2025-06-02 11:35:50'),
(168, 'kyc_plan_purchase', '1', 'boolean', '2025-05-22 08:23:37', '2025-05-22 08:23:37'),
(169, 'site_dark_logo', 'global/uploads/global/uploads/settings//RlbDNZs9kEfJ67hfnZx8.png', 'string', '2025-05-26 05:01:52', '2025-05-26 05:01:52');

-- --------------------------------------------------------

--
-- Table structure for table `set_tunes`
--

CREATE TABLE `set_tunes` (
  `id` bigint UNSIGNED NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tune` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `set_tunes`
--

INSERT INTO `set_tunes` (`id`, `icon`, `name`, `tune`, `status`, `created_at`, `updated_at`) VALUES
(1, 'global/tune-icon/bewitched.png', 'Bewitched', 'global/tune/bewitched.mp3', 1, NULL, '2023-05-26 11:37:38'),
(2, 'global/tune-icon/crunchy.png', 'Crunchy', 'global/tune/crunchy.mp3', 0, NULL, '2023-05-26 11:37:38'),
(3, 'global/tune-icon/expert_notification.png', 'Expert Notification', 'global/tune/expert_notification.mp3', 0, NULL, '2023-05-26 11:37:38'),
(4, 'global/tune-icon/knock_knock.png', 'knock knock', 'global/tune/knock_knock.mp3', 0, NULL, '2023-05-26 11:37:38'),
(5, 'global/tune-icon/silencer.png', 'Silencer', 'global/tune/silencer.mp3', 0, NULL, '2023-05-26 11:37:38'),
(6, 'global/tune-icon/sticky.png', 'Sticky', 'global/tune/sticky.mp3', 0, NULL, '2023-05-26 11:37:38'),
(7, 'global/tune-icon/vopvoopvooop.png', 'Vopvoopvooop', 'global/tune/vopvoopvooop.mp3', 0, NULL, '2023-05-26 11:37:38');

-- --------------------------------------------------------

--
-- Table structure for table `socials`
--

CREATE TABLE `socials` (
  `id` bigint UNSIGNED NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `position` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `socials`
--

INSERT INTO `socials` (`id`, `icon`, `url`, `position`, `created_at`, `updated_at`) VALUES
(2, 'global/uploads/socials/qbEnd0NxxcO7dhhYeXN0.svg', 'https://facebook.com', 0, '2025-03-24 05:47:16', '2025-06-03 09:32:47'),
(3, 'global/uploads/socials/DQlcCsbEeLdde6tT2WTZ.svg', 'https://x.com', 1, '2025-03-24 05:47:37', '2025-06-03 09:32:47'),
(4, 'global/uploads/socials/pDRFkGG9gMqmGTnpSKf2.svg', 'https://youtube.com', 2, '2025-03-24 05:47:54', '2025-06-03 09:32:47');

-- --------------------------------------------------------

--
-- Table structure for table `subscribers`
--

CREATE TABLE `subscribers` (
  `id` bigint UNSIGNED NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `templates`
--

CREATE TABLE `templates` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `for` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'User',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notification_status` tinyint(1) NOT NULL DEFAULT '1',
  `email_status` tinyint(1) NOT NULL DEFAULT '1',
  `sms_status` tinyint(1) NOT NULL DEFAULT '1',
  `sms_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `email_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `notification_body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `short_codes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `banner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `salutation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `button_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `button_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `footer_status` tinyint(1) NOT NULL DEFAULT '1',
  `footer_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `templates`
--

INSERT INTO `templates` (`id`, `name`, `code`, `for`, `icon`, `notification_status`, `email_status`, `sms_status`, `sms_body`, `email_body`, `notification_body`, `short_codes`, `banner`, `title`, `subject`, `salutation`, `button_level`, `button_link`, `footer_status`, `footer_body`, `created_at`, `updated_at`) VALUES
(1, 'User Mail Send', 'user_mail', 'User', 'mail', 1, 1, 1, 'Thanks for joining us [[site_title]]. Find out more at [[site_url]].', 'Thanks for joining us [[site_title]]<br /><br />[[message]]<br /><br />Find out more about in - [[site_url]]', 'Thanks for joining us [[site_title]]. Find out more at [[site_url]].', '[\"[[full_name]]\",\"[[site_url]]\",\"[[site_title]]\",\"[[subject]]\",\"[[message]]\"]', 'global/images/Uxp3vfYFFi4GuO95lyZn.jpg', 'Sample Email', '[[subject]] for [[full_name]]', 'Hi [[full_name]],', 'Login Your Account', NULL, 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(2, 'Subscriber Mail Send', 'subscriber_mail', 'Subscriber', 'mail', 1, 1, 1, 'Welcome to [[site_title]]! Manage your account, trade crypto, and earn profits. Visit [[site_url]].', 'Thanks for joining our platform! ---  [[site_title]]<br /><br />[[message]]<br /><br />As a member of our platform, you can manage your account, buy or sell cryptocurrency, invest and earn profits.<br /><br />Find out more about in - [[site_url]]', 'Welcome to [[site_title]]! Manage your account, trade crypto, and earn profits. Visit [[site_url]].', '[\"[[full_name]]\",\"[[site_url]]\",\"[[site_title]]\",\"[[subject]]\",\"[[message]]\"]', NULL, 'Welcome to [[site_title]]', '[[subject]] for [[full_name]]', 'Hi [[full_name]],', 'Login Your Account', NULL, 1, 'Thanks for joining our platform! ---  [[site_title]]<br /><br />[[message]]<br /><br />As a member of our platform, you can manage your account, buy or sell cryptocurrency, invest and earn profits.<br /><br />Find out more about in - [[site_url]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(3, 'Manual Deposit Request', 'admin_manual_deposit', 'Admin', 'dollar-sign', 1, 1, 1, 'New manual deposit request of [[amount]] [[currency]]. Please review and approve.', 'A new manual deposit request has been submitted.<br /><br />\n                Amount: [[amount]] [[currency]]<br />\n                Charge: [[charge]] [[currency]]<br />\n                Wallet: [[wallet]]<br />\n                Gateway: [[gateway]]<br />\n                Requested At: [[request_at]]<br />\n                Total Amount: [[total_amount]] [[currency]]<br /><br />\n                Please review and approve it.', 'New manual deposit request of [[amount]] [[currency]]. Please review and approve.', '[\"[[amount]]\",\"[[charge]]\",\"[[wallet]]\",\"[[gateway]]\",\"[[request_at]]\",\"[[total_amount]]\",\"[[request_link]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/deposit_request.jpg', 'New Manual Deposit Request', 'New Deposit Request of [[amount]] [[currency]]', 'Hello Admin,', 'View Request', '[[request_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(4, 'Manual Deposit Request Approved', 'user_manual_deposit_approved', 'User', 'check-circle', 1, 1, 1, 'Your deposit request of [[amount]] [[currency]] has been approved. Funds have been credited to your account.', 'We are pleased to inform you that your deposit request has been approved.<br /><br />\n        Amount: [[amount]] [[currency]]<br />\n        Charge: [[charge]] [[currency]]<br />\n        Wallet: [[wallet]]<br />\n        Gateway: [[gateway]]<br />\n        Requested At: [[request_at]]<br />\n        Total Amount: [[total_amount]] [[currency]]<br /><br />\n        The funds have been credited to your account. Thank you for using our services!', 'Your deposit request of [[amount]] [[currency]] has been approved. Funds have been credited to your account.', '[\"[[full_name]]\",\"[[amount]]\",\"[[charge]]\",\"[[wallet]]\",\"[[gateway]]\",\"[[request_at]]\",\"[[total_amount]]\",\"[[transaction_link]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/deposit_approved.jpg', 'Deposit Request Approved', 'Your Deposit Request of [[amount]] [[currency]] has been Approved', 'Hi [[full_name]],', 'View Transaction', '[[transaction_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(5, 'Manual Deposit Request Rejected', 'user_manual_deposit_rejected', 'User', 'x-circle', 1, 1, 1, 'Your deposit request of [[amount]] [[currency]] has been rejected. Reason: [[rejection_reason]].', 'We regret to inform you that your deposit request has been rejected.<br /><br />\n        Amount: [[amount]] [[currency]]<br />\n        Charge: [[charge]] [[currency]]<br />\n        Wallet: [[wallet]]<br />\n        Gateway: [[gateway]]<br />\n        Requested At: [[request_at]]<br />\n        Total Amount: [[total_amount]] [[currency]]<br /><br />\n        Reason for Rejection: [[rejection_reason]]<br /><br />', 'Your deposit request of [[amount]] [[currency]] has been rejected. Reason: [[rejection_reason]].', '[\"[[full_name]]\",\"[[amount]]\",\"[[charge]]\",\"[[wallet]]\",\"[[gateway]]\",\"[[request_at]]\",\"[[total_amount]]\",\"[[rejection_reason]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/deposit_rejected.jpg', 'Deposit Request Rejected', 'Your Deposit Request of [[amount]] [[currency]] has been Rejected', 'Hi [[full_name]],', 'Contact Support', '[[support_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(6, 'Withdraw Request', 'admin_withdraw_request', 'Admin', 'arrow-up', 1, 1, 1, 'New withdrawal request of [[amount]] [[currency]]. Please review and approve.', 'A new withdrawal request has been submitted.<br /><br />\n                Amount: [[amount]] [[currency]]<br />\n                Charge: [[charge]] [[currency]]<br />\n                Wallet: [[wallet]]<br />\n                Gateway: [[gateway]]<br />\n                Requested At: [[request_at]]<br />\n                Total Amount: [[total_amount]] [[currency]]<br /><br />\n                Please review and approve it.', 'New withdrawal request of [[amount]] [[currency]]. Please review and approve.', '[\"[[amount]]\",\"[[charge]]\",\"[[wallet]]\",\"[[gateway]]\",\"[[request_at]]\",\"[[total_amount]]\",\"[[request_link]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/withdraw_request.jpg', 'New Withdraw Request', 'Withdraw Request of [[amount]] [[currency]]', 'Hello Admin,', 'View Request', '[[request_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(7, 'Ticket Reply', 'admin_ticket_reply', 'Admin', 'message-circle', 1, 1, 1, 'New reply received for ticket: [[title]]. Message: [[message]].', 'A new reply has been received for the support ticket.<br /><br />\n                Ticket Title: [[title]]<br />\n                Message: [[message]]<br /><br />\n                Click the button below to view and respond.', 'New reply received for ticket: [[title]]. Message: [[message]].', '[\"[[title]]\",\"[[message]]\",\"[[reply_link]]\",\"[[site_title]]\"]', 'global/images/ticket_reply.jpg', 'New Ticket Reply', 'New Reply for Ticket: [[title]]', 'Hello Admin,', 'View Ticket', '[[reply_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(8, 'Invoice Payment Received', 'user_invoice_payment', 'User', 'file-text', 1, 1, 1, 'Payment received for Invoice #[[invoice_number]]. Amount: [[amount]] [[currency]]. Thank you!', 'We have received your payment for Invoice #[[invoice_number]].<br /><br />Amount: [[amount]] [[currency]]<br />Charge: [[charge]] [[currency]]<br />Total: [[total_amount]] [[currency]]<br /><br />Thank you for your payment!', 'Payment received for Invoice #[[invoice_number]]. Amount: [[amount]] [[currency]]. Thank you!', '[\"[[full_name]]\",\"[[invoice_number]]\",\"[[amount]]\",\"[[charge]]\",\"[[total_amount]]\",\"[[invoice_link]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/invoice_payment.jpg', 'Invoice Payment Received', 'Payment received for Invoice #[[invoice_number]]', 'Hi [[full_name]],', 'View Invoice', '[[invoice_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(9, 'Request Money', 'user_request_money', 'User', 'dollar-sign', 1, 1, 1, 'You received a money request from [[sender_name]]. Amount: [[amount]] [[currency]].', 'You have received a money request from [[sender_name]].<br /><br />Amount: [[amount]] [[currency]]<br />Charge: [[charge]] [[currency]]<br />Total: [[total_amount]] [[currency]]<br />Sender Note: [[sender_note]]<br />Sender Wallet: [[sender_wallet]]<br />Sender Account No: [[sender_account_no]]', 'You received a money request from [[sender_name]]. Amount: [[amount]] [[currency]].', '[\"[[full_name]]\",\"[[amount]]\",\"[[charge]]\",\"[[total_amount]]\",\"[[sender_name]]\",\"[[sender_note]]\",\"[[sender_wallet]]\",\"[[sender_account_no]]\",\"[[request_money_link]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/request_money.jpg', 'Money Request Received', 'You received a money request from [[sender_name]]', 'Hi [[full_name]],', 'View Request', '[[request_money_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(11, 'Money Received', 'user_receive_money', 'User', 'arrow-down', 1, 1, 1, 'You have received [[amount]] [[currency]] from [[sender_name]]. Check your account.', 'You have received a money transfer.<br /><br />\n        Amount: [[amount]] [[currency]]<br />\n        Sender Name: [[sender_name]]<br />\n        Sender Account No: [[sender_account_no]]<br /><br />\n        The funds have been successfully credited to your account.', 'You have received [[amount]] [[currency]] from [[sender_name]].', '[\"[[full_name]]\",\"[[amount]]\",\"[[currency]]\",\"[[sender_name]]\",\"[[sender_account_no]]\",\"[[transaction_link]]\",\"[[site_title]]\"]', 'global/images/receive_money.jpg', 'Money Received Successfully', 'You have received money from [[sender_name]]', 'Hi [[full_name]],', 'View Transaction', '[[transaction_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(12, 'Referral Joining', 'user_referral_join', 'User', 'users', 1, 1, 1, 'A new referral, [[referred_name]], has joined. Joined at: [[joined_at]].', 'You have received a referral bonus.<br /><br />Referred Name: [[referred_name]]<br />Referred Account No: [[referred_account_no]]<br />Joined At: [[joined_at]]', 'Your referral [[referred_name]] has successfully joined. Joined at: [[joined_at]].', '[\"[[full_name]]\",\"[[referred_name]]\",\"[[referred_account_no]]\",\"[[joined_at]]\",\"[[referral_link]]\",\"[[site_title]]\"]', 'global/images/referral_join.jpg', 'Referral Joining', 'Your referral [[referred_name]] has successfully joined', 'Hi [[full_name]],', 'View Referral', '[[referral_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(13, 'Ticket Reply', 'user_ticket_reply', 'User', 'message-circle', 1, 1, 1, 'New reply received for ticket: [[title]]. Message: [[message]].', 'A new reply has been received on your support ticket \"<b>[[title]]</b>\".<br /><br />Message: [[message]].<br /><br />Click the button below to view the reply.', 'New reply received for ticket: [[title]]. Message: [[message]].', '[\"[[full_name]]\",\"[[title]]\",\"[[message]]\",\"[[reply_link]]\",\"[[site_title]]\"]', 'global/images/ticket_reply.jpg', 'New Ticket Reply', 'Reply received for Ticket: [[title]]', 'Hi [[full_name]],', 'View Ticket', '[[reply_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(15, 'Ticket Reply', 'ticket_reply', 'User', 'message-circle', 1, 1, 1, 'New reply received for ticket: [[title]]. Message: [[message]].', 'A new reply has been received on a support ticket \"<b>[[title]]</b>\".<br /><br />Message: [[message]].<br /><br />Click the button below to view the reply.', 'New reply received for ticket: [[title]]. Message: [[message]].', '[\"[[user_name]]\",\"[[title]]\",\"[[message]]\",\"[[reply_link]]\",\"[[site_title]]\"]', 'global/images/ticket_reply.jpg', 'New Ticket Reply', 'Reply received for Ticket: [[title]]', 'Hi [[user_name]],', 'View Ticket', '[[reply_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(16, 'Withdraw Request Approved', 'withdraw_approved', 'User', 'check-circle', 1, 1, 1, 'Your withdrawal request of [[amount]] [[currency]] has been approved. Funds have been transferred.', 'We are pleased to inform you that your withdrawal request has been approved.<br /><br />\n        Amount: [[amount]] [[currency]]<br />\n        Charge: [[charge]] [[currency]]<br />\n        Wallet: [[wallet]]<br />\n        Gateway: [[gateway]]<br />\n        Requested At: [[request_at]]<br />\n        Total Amount: [[total_amount]] [[currency]]<br /><br />\n        The funds have been successfully transferred. Thank you for using our services!', 'Your withdrawal request of [[amount]] [[currency]] has been approved. Funds have been transferred.', '[\"[[full_name]]\",\"[[amount]]\",\"[[charge]]\",\"[[wallet]]\",\"[[gateway]]\",\"[[request_at]]\",\"[[total_amount]]\",\"[[transaction_link]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/withdraw_approved.jpg', 'Withdraw Request Approved', 'Your Withdraw Request of [[amount]] [[currency]] has been Approved', 'Hi [[full_name]],', 'View Transaction', '[[transaction_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(17, 'Withdraw Request Rejected', 'withdraw_rejected', 'User', 'x-circle', 1, 1, 1, 'Your withdrawal request of [[amount]] [[currency]] has been rejected. Reason: [[rejection_reason]].', 'We regret to inform you that your withdrawal request has been rejected.<br /><br />\n        Amount: [[amount]] [[currency]]<br />\n        Charge: [[charge]] [[currency]]<br />\n        Wallet: [[wallet]]<br />\n        Gateway: [[gateway]]<br />\n        Requested At: [[request_at]]<br />\n        Total Amount: [[total_amount]] [[currency]]<br /><br />\n        Reason for Rejection: [[rejection_reason]]<br /><br />', 'Your withdrawal request of [[amount]] [[currency]] has been rejected. Reason: [[rejection_reason]].', '[\"[[full_name]]\",\"[[amount]]\",\"[[charge]]\",\"[[wallet]]\",\"[[gateway]]\",\"[[request_at]]\",\"[[total_amount]]\",\"[[rejection_reason]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/withdraw_rejected.jpg', 'Withdraw Request Rejected', 'Your Withdraw Request of [[amount]] [[currency]] has been Rejected', 'Hi [[full_name]],', 'Contact Support', '[[support_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(18, 'Cash In Successful', 'user_cash_in', 'User', 'arrow-down-circle', 1, 1, 1, 'Cash-in successful! Amount: [[amount]] [[currency]], Wallet: [[wallet]], Total received: [[total_amount]] [[currency]].', 'Your cash-in request has been successfully processed.<br /><br />\n    Amount: [[amount]] [[currency]]<br />\n    Charge: [[charge]] [[currency]]<br />\n    Total Amount: [[total_amount]] [[currency]]<br />\n    Wallet: [[wallet]]<br />\n    Agent Name: [[agent_name]]<br />\n    Agent Account No: [[agent_account_no]]<br /><br />\n    Click the button below to view your transaction details.', 'You have successfully cashed in [[amount]] [[currency]] to [[wallet]]. Total received: [[total_amount]] [[currency]].', '[\"[[full_name]]\",\"[[amount]]\",\"[[charge]]\",\"[[total_amount]]\",\"[[wallet]]\",\"[[agent_name]]\",\"[[agent_account_no]]\",\"[[transaction_link]]\",\"[[site_title]]\",\"[[currency]]\"]', 'global/images/cash_in.jpg', 'Cash In Successful', 'You have successfully cashed in [[amount]] [[currency]]', 'Hi [[full_name]],', 'View Transaction', '[[transaction_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(19, 'Agent Commission Earned', 'agent_commission', 'Agent', 'dollar-sign', 1, 1, 1, 'You have received a commission of [[amount]]. Wallet: [[wallet]], Transaction ID: [[txn_id]].', 'You have earned a new commission.<br /><br />\n    <b>Amount:</b> [[amount]]<br />\n    <b>Wallet:</b> [[wallet]]<br />\n    <b>Transaction ID:</b> [[txn_id]]<br />\n    <b>Commission Date:</b> [[commission_at]]<br /><br />\n    Click the button below to view details.', 'You earned a commission of [[amount]]. Wallet: [[wallet]], Transaction ID: [[txn_id]].', '[\"[[full_name]]\",\"[[amount]]\",\"[[wallet]]\",\"[[commission_at]]\",\"[[txn_id]]\",\"[[transaction_link]]\",\"[[site_title]]\"]', 'global/images/agent_commission.jpg', 'Commission Earned', 'You have received a commission of [[amount]]', 'Hi [[full_name]],', 'View Commission', '[[transaction_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(20, 'Ticket Reply', 'agent_ticket_reply', 'Agent', 'message-circle', 1, 1, 1, 'New reply received for ticket: [[title]]. Message: [[message]].', 'A new reply has been received on a support ticket \"<b>[[title]]</b>\".<br /><br />\n    Message: [[message]].<br /><br />\n    Click the button below to view the reply.', 'New reply received for ticket: [[title]]. Message: [[message]].', '[\"[[agent_name]]\",\"[[title]]\",\"[[message]]\",\"[[reply_link]]\",\"[[site_title]]\"]', 'global/images/ticket_reply.jpg', 'New Ticket Reply', 'Reply received for Ticket: [[title]]', 'Hi [[agent_name]],', 'View Ticket', '[[reply_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(21, 'KYC Request', 'admin_kyc_request', 'Admin', 'check-circle', 1, 1, 1, 'New KYC request received from [[full_name]]. Review now.', 'A new KYC verification request has been submitted.<br /><br />\n    <b>Full Name:</b> [[full_name]]<br />\n    <b>Email:</b> [[email]]<br />\n    <b>KYC Type:</b> [[kyc_type]]<br /><br />\n    Click the button below to review the request.', 'New KYC request received from [[full_name]]. Click to review.', '[\"[[full_name]]\",\"[[email]]\",\"[[kyc_type]]\",\"[[kyc_review_link]]\",\"[[site_title]]\"]', 'global/images/kyc_request.jpg', 'New KYC Request from [[full_name]]', 'KYC request received from [[full_name]]', 'Hello Admin,', 'Review KYC', '[[kyc_review_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(22, 'KYC Action', 'kyc_action', 'User', 'check-circle', 1, 1, 1, 'Your KYC request is [[status]].', 'Your KYC verification request has been [[status]].<br /><br />\n    If you have any questions or need further assistance, please contact support.<br /><br />\n    Click the button below to view your KYC status.', 'Your KYC request has been [[status]]. Click to view.', '[\"[[full_name]]\",\"[[status]]\",\"[[kyc_status_link]]\",\"[[site_title]]\"]', 'global/images/kyc_action.jpg', 'Your KYC request is [[status]]', 'Your KYC request status update', 'Hi [[full_name]],', 'View KYC Status', '[[kyc_status_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(23, 'Forgot Password', 'forgot_password', 'User', 'key-round', 1, 1, 1, 'Reset your password here: [[token]] - [[site_title]]', 'We received a request to reset your password.<br /><br />\n    To reset your password, please click the button below or use the link provided.<br /><br />\n    If you didn’t request this, you can safely ignore this email.<br /><br />\n    Link: <a href=\"[[token]]\">[[token]]</a><br /><br />\n    Visit our site for more info: <a href=\"[[site_url]]\">[[site_url]]</a>', 'A password reset was requested. Click the link to proceed: [[token]]', '[\"[[token]]\",\"[[site_title]]\",\"[[site_url]]\"]', 'global/images/forgot_password.jpg', 'Reset Your Password', 'Reset Your Password - [[site_title]]', 'Hello,', 'Reset Password', '[[token]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(24, 'Email Verification', 'email_verification', 'User', 'check-circle', 1, 1, 1, 'Please verify your email address here: [[token]] - [[site_title]]', 'Hello!<br /><br />\n        Please click the button below to verify your email address.<br /><br />\n        If you didn’t request this, you can safely ignore this email.<br /><br />\n        <a href=\"[[token]]\">Verify Email Address</a><br /><br />\n        Visit our site for more info: <a href=\"[[site_url]]\">[[site_url]]</a>', 'Please verify your email address. Click the link to proceed: [[token]]', '[\"[[token]]\",\"[[full_name]]\",\"[[site_title]]\",\"[[site_url]]\"]', NULL, 'Verify Email Address', 'Verify Email Address - [[site_title]]', 'Hi [[full_name]],', 'Verify Email Address', '[[token]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(25, 'Contact Mail Send', 'contact_mail', 'Admin', 'mail', 0, 1, 0, NULL, 'Thanks for joining our platform! --- [[site_title]]<br /><br />\n[[message]]<br />\n[[full_name]]<br />\n[[email]]<br /><br />\nAs a member of our platform, you can mange your account, buy or sell cryptocurrency, invest and earn profits.<br /><br />\nFind out more about in - [[site_url]]', NULL, '[\"[[site_url]]\",\"[[site_title]]\",\"[[full_name]]\",\"[[email]]\",\"[[subject]]\",\"[[message]]\"]', NULL, 'Welcome to [[site_title]]', '[[subject]] for [[full_name]]', 'Hi [[full_name]],', 'Login Your Account', NULL, 1, 'Thanks for joining our platform! --- [[site_title]]<br /><br />\n[[message]]<br /><br /><br />\nAs a member of our platform, you can mange your account, buy or sell cryptocurrency, invest and earn profits.<br /><br />\nFind out more about in - [[site_url]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46'),
(26, 'Plan Purchase Admin Notification', 'plan_purchase_admin', 'Admin', 'credit-card', 1, 1, 0, 'A new crypto plan purchase has been made. Final amount: [[final_amount]], Payable Amount: [[payable_amount]].', '<p>Hi [[admin_name]],</p>\n<p>A new crypto plan ([[scheme_name]]) purchase has been completed.</p>\n<ul>\n  <li><strong>Conversion Rate:</strong> [[conversion_rate]]</li>\n  <li><strong>Charge:</strong> [[charge]]</li>\n  <li><strong>Final Amount:</strong> [[final_amount]]</li>\n  <li><strong>Payable Amount:</strong> [[payable_amount]]</li>\n</ul>\n<p>Click the button below to view details.</p>', 'A new crypto plan purchase has been made. Final amount: [[final_amount]], Payable Amount: [[payable_amount]].', '[\"[[admin_name]]\",\"[[conversion_rate]]\",\"[[scheme_name]]\",\"[[charge]]\",\"[[final_amount]]\",\"[[payable_amount]]\",\"[[purchase_link]]\",\"[[site_title]]\"]', 'global/images/plan_purchase_admin.jpg', 'Crypto Plan Purchase Notification', 'New Crypto Plan Purchase - [[final_amount]]', 'Hi [[admin_name]],', 'View Purchase', '[[purchase_link]]', 1, 'Regards,<br />[[site_title]]', '2025-04-09 08:38:46', '2025-04-09 08:38:46');

-- --------------------------------------------------------

--
-- Table structure for table `testimonials`
--

CREATE TABLE `testimonials` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `designation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `picture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `themes`
--

CREATE TABLE `themes` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` enum('landing','site') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'landing',
  `status` tinyint DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `themes`
--

INSERT INTO `themes` (`id`, `name`, `type`, `status`, `created_at`, `updated_at`) VALUES
(1, 'default', 'site', 1, '2024-11-19 22:45:36', '2025-05-25 03:46:28');

-- --------------------------------------------------------

--
-- Table structure for table `tickets`
--

CREATE TABLE `tickets` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `attachments` json DEFAULT NULL,
  `priority` enum('low','medium','high') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'low',
  `status` enum('open','closed','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'open',
  `is_resolved` tinyint(1) DEFAULT '0',
  `is_locked` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `from_user_id` bigint UNSIGNED DEFAULT NULL,
  `from_model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'User',
  `target_id` bigint UNSIGNED DEFAULT NULL,
  `invoice_id` bigint UNSIGNED DEFAULT NULL,
  `target_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `wallet_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_level` tinyint(1) DEFAULT '0',
  `tnx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(28,8) NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `charge` decimal(28,8) NOT NULL,
  `final_amount` decimal(28,8) NOT NULL,
  `method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pay_currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pay_amount` decimal(28,8) DEFAULT NULL,
  `callback_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `manual_field_data` json DEFAULT NULL,
  `approval_cause` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `scheme_id` int DEFAULT NULL,
  `user_mining_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint UNSIGNED NOT NULL,
  `role` enum('User','Agent','Merchant') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'User',
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `account_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `kyc` tinyint DEFAULT '0',
  `phone_verified` tinyint DEFAULT '0',
  `otp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `provider_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `balance` decimal(28,8) DEFAULT '0.********',
  `country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` enum('male','female','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `zip_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `close_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ref_id` int DEFAULT NULL,
  `referral_code` varchar(511) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `google2fa_secret` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `two_fa` tinyint DEFAULT '0',
  `withdraw_status` tinyint DEFAULT '1',
  `otp_status` tinyint DEFAULT '1',
  `deposit_status` tinyint DEFAULT '1',
  `plan_purchase_status` tinyint DEFAULT '1',
  `referral_status` tinyint DEFAULT '1',
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `portfolio_id` int DEFAULT NULL,
  `portfolios` json NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_kycs`
--

CREATE TABLE `user_kycs` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `kyc_id` bigint UNSIGNED DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_valid` tinyint DEFAULT '0',
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_minings`
--

CREATE TABLE `user_minings` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `scheme_id` int NOT NULL,
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `plan_price` decimal(18,8) NOT NULL,
  `mining_count` int NOT NULL DEFAULT '0',
  `total_mined_amount` decimal(18,8) DEFAULT '0.********',
  `last_mining_time` timestamp NULL DEFAULT NULL,
  `next_mining_time` timestamp NULL DEFAULT NULL,
  `status` enum('ongoing','completed','pending','canceled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'ongoing',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_navigations`
--

CREATE TABLE `user_navigations` (
  `id` bigint UNSIGNED NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `route` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `position` int DEFAULT '0',
  `translation` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_navigations`
--

INSERT INTO `user_navigations` (`id`, `icon`, `route`, `url`, `type`, `name`, `position`, `translation`, `created_at`, `updated_at`) VALUES
(1, 'tabler:layout-dashboard', 'user.dashboard', 'user/dashboard', 'dashboard', 'Dashboard', 1, NULL, '2025-05-30 06:16:57', '2025-05-30 06:31:32'),
(2, 'tabler:hammer', 'user.mining.', 'user/mining', 'mining', 'My Mining', 2, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57'),
(3, 'tabler:moneybag-plus', 'user.addMoney', 'user/add-money', 'add_money', 'Add Money', 3, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57'),
(4, 'tabler:wallet', 'user.userWallet.index', 'user/user-wallet', 'wallet', 'My Wallets', 4, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57'),
(5, 'tabler:circle-arrow-down', 'user.withdrawMoney.index', 'user/withdraw-money', 'withdraw', 'Withdraw', 5, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57'),
(6, 'tabler:gift', 'user.portfolio', 'user/portfolio', 'portfolio', 'Portfolio', 6, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57'),
(7, 'tabler:history', 'user.transactions', 'user/transactions', 'transactions', 'Transaction History', 7, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57'),
(8, 'tabler:user-share', 'user.referral', 'user/referral', 'referral', 'Referral Program', 8, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57'),
(9, 'tabler:headset', 'user.tickets', 'user/tickets', 'support', 'Support Ticket', 9, NULL, '2025-05-30 06:16:57', '2025-05-30 06:19:35'),
(10, 'tabler:settings', 'user.setting.index', 'user/setting', 'settings', 'Settings', 10, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57'),
(11, 'tabler:lock', 'logout', 'logout', 'settings', 'Logout', 10, NULL, '2025-05-30 06:16:57', '2025-05-30 06:16:57');

-- --------------------------------------------------------

--
-- Table structure for table `user_wallets`
--

CREATE TABLE `user_wallets` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `coin_id` bigint UNSIGNED DEFAULT NULL,
  `balance` decimal(28,8) NOT NULL DEFAULT '0.********',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `withdrawal_schedules`
--

CREATE TABLE `withdrawal_schedules` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `withdraw_accounts`
--

CREATE TABLE `withdraw_accounts` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `user_wallet_id` bigint UNSIGNED NOT NULL,
  `withdraw_method_id` bigint UNSIGNED DEFAULT NULL,
  `method_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `credentials` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `withdraw_methods`
--

CREATE TABLE `withdraw_methods` (
  `id` bigint UNSIGNED NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'manual',
  `gateway_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `rate` decimal(28,8) DEFAULT NULL,
  `required_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `required_time_format` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `charge` decimal(28,8) DEFAULT NULL,
  `charge_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `min_withdraw` decimal(28,8) DEFAULT NULL,
  `max_withdraw` decimal(28,8) DEFAULT NULL,
  `fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admins_email_unique` (`email`);

--
-- Indexes for table `blogs`
--
ALTER TABLE `blogs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `coins`
--
ALTER TABLE `coins`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cron_jobs`
--
ALTER TABLE `cron_jobs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cron_job_logs`
--
ALTER TABLE `cron_job_logs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `currencies`
--
ALTER TABLE `currencies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `custom_csses`
--
ALTER TABLE `custom_csses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `deposit_methods`
--
ALTER TABLE `deposit_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `gateways`
--
ALTER TABLE `gateways`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `gateways_gateway_code_unique` (`gateway_code`);

--
-- Indexes for table `holidays`
--
ALTER TABLE `holidays`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `kycs`
--
ALTER TABLE `kycs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `kycs_name_unique` (`name`);

--
-- Indexes for table `landing_contents`
--
ALTER TABLE `landing_contents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `landing_pages`
--
ALTER TABLE `landing_pages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `languages`
--
ALTER TABLE `languages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `level_referrals`
--
ALTER TABLE `level_referrals`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `login_activities`
--
ALTER TABLE `login_activities`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `messages_user_id_foreign` (`user_id`),
  ADD KEY `messages_ticket_id_foreign` (`ticket_id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `miners`
--
ALTER TABLE `miners`
  ADD PRIMARY KEY (`id`),
  ADD KEY `coin_id` (`coin_id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `navigations`
--
ALTER TABLE `navigations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `pages`
--
ALTER TABLE `pages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `page_settings`
--
ALTER TABLE `page_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `page_settings_key_unique` (`key`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `plugins`
--
ALTER TABLE `plugins`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `portfolios`
--
ALTER TABLE `portfolios`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `portfolio_features`
--
ALTER TABLE `portfolio_features`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `schedules`
--
ALTER TABLE `schedules`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `schemes`
--
ALTER TABLE `schemes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `settings_name_unique` (`name`);

--
-- Indexes for table `set_tunes`
--
ALTER TABLE `set_tunes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `socials`
--
ALTER TABLE `socials`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `subscribers`
--
ALTER TABLE `subscribers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `subscribers_email_unique` (`email`);

--
-- Indexes for table `templates`
--
ALTER TABLE `templates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `templates_code_unique` (`code`);

--
-- Indexes for table `testimonials`
--
ALTER TABLE `testimonials`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `themes`
--
ALTER TABLE `themes`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tickets`
--
ALTER TABLE `tickets`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_username_unique` (`username`),
  ADD UNIQUE KEY `users_account_number_unique` (`account_number`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `user_kycs`
--
ALTER TABLE `user_kycs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user_minings`
--
ALTER TABLE `user_minings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user_navigations`
--
ALTER TABLE `user_navigations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user_wallets`
--
ALTER TABLE `user_wallets`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `withdrawal_schedules`
--
ALTER TABLE `withdrawal_schedules`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `withdraw_accounts`
--
ALTER TABLE `withdraw_accounts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `withdraw_methods`
--
ALTER TABLE `withdraw_methods`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `blogs`
--
ALTER TABLE `blogs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `coins`
--
ALTER TABLE `coins`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `cron_jobs`
--
ALTER TABLE `cron_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `cron_job_logs`
--
ALTER TABLE `cron_job_logs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `currencies`
--
ALTER TABLE `currencies`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `custom_csses`
--
ALTER TABLE `custom_csses`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `deposit_methods`
--
ALTER TABLE `deposit_methods`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `gateways`
--
ALTER TABLE `gateways`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `holidays`
--
ALTER TABLE `holidays`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `kycs`
--
ALTER TABLE `kycs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `landing_contents`
--
ALTER TABLE `landing_contents`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=206;

--
-- AUTO_INCREMENT for table `landing_pages`
--
ALTER TABLE `landing_pages`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `languages`
--
ALTER TABLE `languages`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `level_referrals`
--
ALTER TABLE `level_referrals`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `login_activities`
--
ALTER TABLE `login_activities`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `messages`
--
ALTER TABLE `messages`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=91;

--
-- AUTO_INCREMENT for table `miners`
--
ALTER TABLE `miners`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `navigations`
--
ALTER TABLE `navigations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pages`
--
ALTER TABLE `pages`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `page_settings`
--
ALTER TABLE `page_settings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=99;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `plugins`
--
ALTER TABLE `plugins`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `portfolios`
--
ALTER TABLE `portfolios`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `portfolio_features`
--
ALTER TABLE `portfolio_features`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `schedules`
--
ALTER TABLE `schedules`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `schemes`
--
ALTER TABLE `schemes`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=170;

--
-- AUTO_INCREMENT for table `set_tunes`
--
ALTER TABLE `set_tunes`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `socials`
--
ALTER TABLE `socials`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `subscribers`
--
ALTER TABLE `subscribers`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `templates`
--
ALTER TABLE `templates`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `testimonials`
--
ALTER TABLE `testimonials`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `themes`
--
ALTER TABLE `themes`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `tickets`
--
ALTER TABLE `tickets`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_kycs`
--
ALTER TABLE `user_kycs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_minings`
--
ALTER TABLE `user_minings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_navigations`
--
ALTER TABLE `user_navigations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `user_wallets`
--
ALTER TABLE `user_wallets`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `withdrawal_schedules`
--
ALTER TABLE `withdrawal_schedules`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `withdraw_accounts`
--
ALTER TABLE `withdraw_accounts`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `withdraw_methods`
--
ALTER TABLE `withdraw_methods`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `messages`
--
ALTER TABLE `messages`
  ADD CONSTRAINT `messages_ticket_id_foreign` FOREIGN KEY (`ticket_id`) REFERENCES `tickets` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `messages_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `miners`
--
ALTER TABLE `miners`
  ADD CONSTRAINT `miners_ibfk_1` FOREIGN KEY (`coin_id`) REFERENCES `coins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
