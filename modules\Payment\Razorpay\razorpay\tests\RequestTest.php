<?php

namespace Razorpay\Tests;

class RequestTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * Create customer
     */
    public function testadd_header()
    {
        $data = $this->api->customer->create(['name' => 'Razorpay User 38', 'email' => '<EMAIL>', 'fail_existing' => '0']);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue(in_array('customer', $data->toArray()));
    }

    /**
     * Edit customer
     */
    public function test_edit_customer()
    {
        $data = $this->api->customer->fetch($this->customerId)->edit(['name' => 'Razorpay User 21', 'contact' => '9123456780']);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue(in_array($this->customerId, $data->toArray()));
    }

    /**
     * Fetch customer All
     */
    public function test_fetch_all()
    {
        $data = $this->api->customer->all();

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue(is_numeric($data->count()));

        $this->assertTrue(is_array($data['items']));
    }

    /**
     * Fetch a customer
     */
    public function test_fetch_customer()
    {
        $data = $this->api->customer->fetch($this->customerId);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue(in_array($this->customerId, $data->toArray()));
    }
}
