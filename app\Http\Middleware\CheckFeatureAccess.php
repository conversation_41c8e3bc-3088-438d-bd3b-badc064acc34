<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckFeatureAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ?string $permissionKey = null, ?string $kycKey = null): Response
    {
        if ($permissionKey && ! setting($permissionKey, 'permission') && ! $request->user()->hasRole('admin')) {
            notify()->error(__('This feature is currently unavailable'));

            return $this->redirectTo();
        } elseif ($kycKey && ! setting($kycKey, 'kyc') && ! $request->user()->kyc) {
            notify()->error(__('Please verify your KYC.'));

            return $this->redirectTo();
        }

        return $next($request);
    }

    private function redirectTo()
    {
        return to_route('user.dashboard');
    }
}
