<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserMining extends Model
{
    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function scheme()
    {
        return $this->belongsTo(Scheme::class);
    }

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }

    public function childTransactions()
    {
        return $this->hasMany(Transaction::class, 'user_mining_id');
    }
}
