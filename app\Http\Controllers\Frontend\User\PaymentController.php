<?php

namespace App\Http\Controllers\Frontend\User;

use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Http\Controllers\Controller;
use App\Models\LevelReferral;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserWallet;
use App\Services\CurrencyService;
use App\Traits\NotifyTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;

class PaymentController extends Controller
{
    use NotifyTrait;

    public function index()
    {
        $user = Auth::user();
        $wallets = [];

        if (setting('multiple_currency', 'permission')) {
            $wallets = UserWallet::query()
                ->with([
                    'currency',
                ])
                ->where('user_id', $user->id)
                ->get();
        }

        return view('frontend::user.payment.index', ['wallets' => $wallets]);
    }

    public function history(Request $request)
    {
        $user = Auth::user();
        $transactions = Transaction::query()
            ->with('userWallet.currency', 'fromUser')
            ->where('user_id', $user->id)
            ->where('type', TxnType::PlanPurchase)
            ->when($request->tnx_id, fn ($q) => $q->whereLike('tnx', '%'.$request->tnx_id.'%'))
            ->latest()
            ->paginate($request->integer('perPage', 10))
            ->withQueryString();

        return view('frontend::user.payment.history', ['transactions' => $transactions]);
    }

    public function now(Request $request)
    {
        $request->validate([
            'merchant_number' => 'required',
            'wallet_id' => 'required',
            'amount' => 'required|numeric',
        ]);

        // Get data
        $user = Auth::user();
        $wallet = UserWallet::where('user_id', $user->id)->where('id', $request->wallet_id)->first();

        // Charge calculation
        $charge = 0;
        $userCharge = setting('user_make_payment_charge', 'make_payment');
        $userChargeType = setting('user_make_payment_charge_type', 'make_payment');
        $chargeCurrency = setting('site_currency', 'global');

        // User charge
        if ($userChargeType === 'percentage') {
            $charge = ($userCharge * $request->amount) / 100;
        } else {
            $charge = $chargeCurrency == $wallet->currency->code ? $userCharge : $userCharge * $wallet->currency->conversion_rate;
        }

        // Merchant charge
        $merchantChargeAmount = 0;
        $merchantCharge = setting('merchant_make_payment_charge', 'make_payment');
        $merchantChargeType = setting('merchant_make_payment_charge_type', 'make_payment');
        $siteCurrency = setting('site_currency', 'global');
        if ($merchantChargeType === 'percentage') {
            $merchantChargeAmount += ($merchantCharge * $request->amount) / 100;
        } else {
            $merchantChargeAmount += $chargeCurrency == data_get($wallet, 'currency.code', setting('site_currency', 'global')) ? $merchantCharge : $merchantCharge * $wallet->currency->conversion_rate;
        }

        $totalAmount = $request->amount + $charge;
        $merchant = User::has('merchant')->where('account_number', $request->merchant_number)->first();

        // Check merchant
        if (! $merchant) {
            notify()->error(__('Merchant not found'));

            return back();
        }

        // Check min and max amount
        $minAmount = CurrencyService::getConvertedAmount(setting('payment_minimum', 'make_payment'), data_get($wallet, 'currency.code', $siteCurrency), true);
        $maxAmount = CurrencyService::getConvertedAmount(setting('payment_maximum', 'make_payment'), data_get($wallet, 'currency.code', $siteCurrency), true);

        if ($request->amount < $minAmount || $request->amount > $maxAmount) {
            notify()->error(__('Please enter the amount within the range :min to :max', [
                'min' => $minAmount,
                'max' => $maxAmount.' '.data_get($wallet, 'currency.code', $siteCurrency),
            ]));

            return back();
        }

        // Check balance
        if (($request->get('wallet_id') === 'default' && $user->balance < $totalAmount) || ($request->get('wallet_id') !== 'default' && $wallet?->balance < $totalAmount)) {
            notify()->error(__('Insufficient funds!'));

            return back();
        }

        try {
            DB::beginTransaction();

            // Deducting total amount from user
            if ($request->get('wallet_id') === 'default') {
                $user->decrement('balance', $totalAmount);
                $merchant->increment('balance', $request->amount - $merchantChargeAmount);
            } else {
                $wallet->decrement('balance', $totalAmount);
                $merchantWallet = UserWallet::firstOrCreate([
                    'user_id' => $merchant->id,
                    'currency_id' => $wallet->currency_id,
                ], [
                    'balance' => 0,
                ]);

                $merchantWallet->balance += $request->amount - $merchantChargeAmount;
                $merchantWallet->save();
            }

            // Create transaction for merchant
            Transaction::create([
                'user_id' => $merchant->id,
                'description' => 'Payment Received',
                'from_user_id' => $user->id,
                'type' => TxnType::PlanPurchase,
                'amount' => $request->amount - $merchantChargeAmount,
                'wallet_type' => $request->get('wallet_id'),
                'charge' => $merchantChargeAmount,
                'final_amount' => $request->amount + $merchantChargeAmount,
                'method' => 'User',
                'status' => TxnStatus::Success,
            ]);

            // Create transaction for user
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'description' => 'Payment Sent',
                'from_user_id' => $merchant->id,
                'type' => TxnType::PlanPurchase,
                'amount' => $request->amount,
                'wallet_type' => $request->get('wallet_id'),
                'charge' => $charge,
                'final_amount' => $totalAmount,
                'method' => 'User',
                'status' => TxnStatus::Success,
            ]);

            // Referral Bonus
            if (setting('payment', 'referral_level')) {
                $level = LevelReferral::where('type', 'payment')->max('the_order') + 1;
                creditCurrencyWiseReferralBonus($user, 'payment', $transaction->amount, $level, 1, null, $wallet);
            }

            DB::commit();

            $shortcodes = [
                '[[withdraw_rejected]]' => $merchant->full_name,
                '[[amount]]' => formatAmount($transaction->amount, $transaction->currency),
                '[[charge]]' => formatAmount($transaction->charge, $transaction->currency),
                '[[total_amount]]' => formatAmount($transaction->final_amount, $transaction->currency),
                '[[wallet]]' => data_get($transaction->userWallet, 'currency.code', setting('site_currency', 'global')),
                '[[gateway]]' => $transaction->method,
                '[[payment_at]]' => $transaction->created_at,
                '[[payment_id]]' => $transaction->tnx,
                '[[user_name]]' => $user->full_name,
                '[[user_account_no]]' => $user->account_number,
                '[[site_title]]' => setting('site_title', 'global'),
            ];

            $this->sendNotify($merchant->email, 'merchant_payment', 'Merchant', $shortcodes, $merchant->phone, $merchant->id, route('merchant.transactions'));

            notify()->success(__('Payment has been sent successfully!'));

            return redirect()->to(URL::temporarySignedRoute('user.payment.success', now()->addMinutes(5), ['txn' => $transaction->tnx]));
        } catch (\Throwable $throwable) {
            DB::rollBack();

            notify()->error(__('Sorry! Something went wrong.'));

            return redirect()->back();
        }
    }

    public function success($txn)
    {
        $tran = Transaction::where('type', TxnType::PlanPurchase)->tnx($txn);

        return view('frontend::user.payment.success', ['tran' => $tran]);
    }
}
