<?php

namespace App\Http\Controllers\Auth;

use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Facades\Txn\Txn;
use App\Http\Controllers\Controller;
use App\Models\LoginActivities;
use App\Models\Transaction;
use App\Models\User;
use App\Traits\NotifyTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules;

class RegisteredUserController extends Controller
{
    use NotifyTrait;

    public function create()
    {
        if (! setting('account_creation', 'permission')) {
            notify()->error(__('Registration is disabled'));

            return to_route('home');
        }

        $page = getPageData('register');

        $data = json_decode($page?->data, true);

        $location = getLocation();

        return view('frontend::auth.register', ['data' => $data, 'location' => $location]);
    }

    public function store(Request $request)
    {
        $usernameRequired = $this->isFieldRequired('username');
        $phoneRequired = $this->isFieldRequired('phone');
        $countryRequired = $this->isFieldRequired('country');
        $referralCodeRequired = $this->isFieldRequired('referral_code');
        $genderRequired = $this->isFieldRequired('gender');

        $validator = Validator::make($request->all(), [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'username' => [Rule::requiredIf($usernameRequired), 'string', 'max:255', 'unique:users,username'],
            'phone' => [Rule::requiredIf($phoneRequired), 'string', 'max:255', 'unique:users,phone'],
            'country' => [Rule::requiredIf($countryRequired), 'string', 'max:255'],
            'invite' => [Rule::requiredIf($referralCodeRequired), 'string', 'exists:users,referral_code'],
            'gender' => [Rule::requiredIf($genderRequired), 'string', 'in:male,female,other', 'max:255'],
            'i_agree' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {

            $referralUser = User::where('referral_code', $request->invite)->first();

            $username = ! $request->filled('username') ? generateUniqueUsername(trim($request->first_name.' '.$request->last_name)) : $request->username;

            if (! $request->filled('country')) {
                $location = getLocation();
                $request->merge([
                    'country' => $location->name.':'.$location->dial_code,
                ]);
            }

            $dial_code = explode(':', $request->country);
            $phone = $dial_code[1].$request->phone;
            $country = $dial_code[0];

            // Create user account
            DB::beginTransaction();

            $user = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'username' => $username,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'country' => $country,
                'phone' => $phone,
                'gender' => $request->gender,
                'ref_id' => $referralUser?->id,
                'portfolio_id' => null,
                'portfolios' => json_encode([]),
            ]);

            Auth::login($user);

            $this->distributeSignUpBonus($user);

            if ($referralUser) {
                $this->processReferralBonus($referralUser, $user);
            }

            LoginActivities::add();

            notify()->success(__('Registration successful!'));

            DB::commit();
            session()->put('new_user', true);

            return to_route('user.dashboard');
        } catch (\Throwable $throwable) {
            throw $throwable;
            DB::rollBack();

            notify()->error(__('Sorry! Something went wrong.'));

            return redirect()->back();
        }
    }

    private function isFieldRequired($field)
    {
        return getPageSetting("{$field}_show") && getPageSetting("{$field}_validation");
    }

    private function processReferralBonus($referral, $user)
    {
        $referral->load('portfolio');
        $email_verification = setting('email_verification', 'permission') ? $referral->email_verified_at !== null : true;
        DB::beginTransaction();
        try {
            // Sign Up Referral Bonus
            if (setting('sign_up_referral', 'permission') && $email_verification) {

                $referralBonus = (float) setting('referral_bonus', 'fee');
                // User who was sharing link
                $provider = $referral;

                Transaction::create([
                    'user_id' => $provider->id,
                    'from_user_id' => $user->id,
                    'from_model' => 'User',
                    'wallet_type' => 'default',
                    'description' => 'Referral Bonus via '.$user->full_name,
                    'type' => TxnType::Referral,
                    'amount' => $referralBonus,
                    'charge' => 0,
                    'final_amount' => $referralBonus,
                    'method' => 'System',
                    'status' => TxnStatus::Success,
                ]);

                $refBoostedAmount = 0;
                if ($provider->portfolio && $provider->portfolio->features) {
                    $portfolioFeatures = $provider->portfolio->features;
                    if ($portfolioFeatures->boost_amount_referral_bonus > 0) {

                        if ($portfolioFeatures->boost_referral_bonus_type == 'percentage') {
                            $refBoostedAmount += ($referralBonus * $portfolioFeatures->boost_amount_referral_bonus / 100);
                        } else {
                            $refBoostedAmount += $portfolioFeatures->boost_amount_referral_bonus;
                        }

                        $referralBoostedTransaction = Transaction::create([
                            'user_id' => $provider->id,
                            'description' => 'Referral Bonus Boost',
                            'type' => TxnType::BoostedReferral,
                            'amount' => $refBoostedAmount,
                            'wallet_type' => 'default',
                            'charge' => 0,
                            'final_amount' => $refBoostedAmount,
                            'method' => 'System',
                            'status' => TxnStatus::Success,
                        ]);

                        $referralBonus += $refBoostedAmount;
                    }
                }

                $provider->increment('balance', $referralBonus);

                $shortcodes = [
                    '[[full_name]]' => $provider->full_name,
                    '[[referred_name]]' => $user->full_name,
                    '[[referred_account_no]]' => $user->account_number,
                    '[[joined_at]]' => $user->created_at,
                    '[[referral_link]]' => route('user.referral'),
                    '[[site_title]]' => setting('site_title', 'global'),
                ];

                $this->sendNotify($provider->email, 'user_referral_join', 'User', $shortcodes, $provider->phone, $provider->id, route('user.referral'));
            }
        } catch (\Throwable $th) {
            throw $th;
            DB::rollBack();
        }
        DB::commit();
    }

    private function distributeSignUpBonus($user)
    {
        if (setting('referral_signup_bonus', 'permission') && (float) setting('signup_bonus', 'fee') > 0) {
            $signupBonus = (float) setting('signup_bonus', 'fee');
            $user->increment('balance', $signupBonus);
            (new Txn)->new($signupBonus, 0, $signupBonus, 'default', 'system', 'Signup Bonus', TxnType::SignupBonus, TxnStatus::Success, null, null, $user->id);
            session()->flash('sign_up_bonus', setting('currency_symbol', 'global').$signupBonus);
        }
    }
}
