<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserWallet extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'coin_id',
        'balance',
    ];

    public function coin()
    {
        return $this->belongsTo(Coin::class, 'coin_id', 'id')
            ->withDefault([
                'name' => setting('site_currency'),
                'symbol' => setting('currency_symbol'),
                'code' => setting('site_currency'),
            ]);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
