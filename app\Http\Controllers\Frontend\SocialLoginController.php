<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use App\Models\LoginActivities;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;

class SocialLoginController extends Controller
{
    public function redirect($provider)
    {
        abort_if($provider != 'google' && $provider != 'facebook', 404);

        return Socialite::driver($provider)->redirect();
    }

    public function callback($provider)
    {
        abort_if($provider != 'google' && $provider != 'facebook', 404);

        try {
            $socialiteUser = Socialite::driver($provider)->user();
        } catch (\Exception $exception) {
            return redirect()->route('login');
        }

        $socialiteUserId = $socialiteUser->getId();
        $socialiteUserName = $socialiteUser->getName();
        $socialiteUseremail = $socialiteUser->getEmail();
        $socialiteUserimage = $socialiteUser->getAvatar();

        $user = User::where([
            'provider' => $provider,
            'provider_id' => $socialiteUserId,
        ])->first();

        if (! $user) {
            $first_name = explode(' ', $socialiteUserName)[0];
            $last_name = explode(' ', $socialiteUserName)[1] ?? $first_name;
            $username = \Illuminate\Support\Str::slug($first_name).'_'.Str::random(5);

            $user = User::create([
                'first_name' => $first_name,
                'last_name' => $last_name,
                'username' => $username,
                'email' => $socialiteUseremail,
                'password' => bcrypt($socialiteUserId),
                'avatar' => $socialiteUserimage,
                'provider' => $provider,
                'provider_id' => $socialiteUserId,
                'country' => 'United States',
                'phone' => '**********',
                'license_type' => 'exclusive',
            ]);
        }

        Auth::login($user);

        LoginActivities::add();

        return redirect()->intended(route('user.dashboard', absolute: false));
    }

    public function register(Request $request)
    {
        $provider = session('provider');
        $socialiteUserId = session('socialiteUserId');
        $socialiteUserName = session('socialiteUserName');
        $socialiteUseremail = session('socialiteUseremail');
        $socialiteUserimage = session('socialiteUserimage');

        // Checking email exists or not
        $is_exists_email = User::where('email', $socialiteUseremail)->exists();
        $email = $is_exists_email ? $socialiteUseremail.'_'.uniqid() : $socialiteUseremail;

        // Create user account
        $user = User::create([
            'name' => $socialiteUserName,
            'email' => $email,
            'username' => Str::slug($socialiteUserName).'_'.Str::random(5),
            'image' => $socialiteUserimage,
            'provider' => $provider,
            'provider_id' => $socialiteUserId,
            'role' => $request->user,
            'email_verified_at' => now(),
        ]);

        $admins = Admin::all();
        foreach ($admins as $admin) {
            $admin->notify(new NewUserRegisteredNotification($admin, $user));
        }

        Auth::guard('user')->login($user);

        $this->forgetSocialSessions();

        return redirect()->route('user.dashboard');
    }

    protected function forgetSocialSessions()
    {
        session()->forget('provider');
        session()->forget('socialiteUserId');
        session()->forget('socialiteUserName');
        session()->forget('socialiteUseremail');
        session()->forget('socialiteUserimage');
        session()->forget('socialiteUserimage');
    }
}
