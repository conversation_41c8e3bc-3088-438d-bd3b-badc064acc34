<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LandingPage extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function scopeCurrentTheme($query)
    {
        return $query->where('theme', site_theme());
    }

    /**
     * Scope a query to only include currentLang
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCurrentLang($query)
    {
        return $query->where('locale', app()->getLocale());
    }

    public function content()
    {
        return $this->hasMany(LandingContent::class, 'type', 'code');
    }
}
