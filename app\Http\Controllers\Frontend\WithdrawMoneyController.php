<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserWallet;
use App\Models\WithdrawAccount;
use App\Models\WithdrawalSchedule;
use App\Traits\NotifyTrait;
use App\Traits\Payment;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class WithdrawMoneyController extends Controller
{
    use NotifyTrait;
    use Payment;

    public function withdrawMoneyHistory(Request $request)
    {
        $user = Auth::user();
        $transactions = Transaction::with('wallet.coin')
            ->where('user_id', $user->id)
            ->when($request->filled('txn'), function ($query) use ($request) {
                $query->where('tnx', 'like', '%'.$request->input('txn').'%');
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->input('status'));
            })
            ->when($request->filled('wallet'), function ($query) use ($request) {
                $query->whereRelation('wallet', 'coin_id', $request->input('wallet'));
            })
            ->when($request->filled('date'), function ($query) use ($request) {
                if (str($request->input('date'))->contains('to')) {
                    $dates = explode(' to ', $request->input('date'));
                    $dates = array_map(function ($date) {
                        return date('Y-m-d', strtotime($date));
                    }, $dates);
                    $query->whereBetween(DB::raw('DATE(created_at)'), $dates);
                } else {
                    $query->whereDate('created_at', $request->input('date'));
                }
            })
            ->whereIn('type', [TxnType::Withdraw, TxnType::WithdrawAuto])
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('frontend::user.withdraw.history', ['transactions' => $transactions]);
    }

    public function withdrawMoney()
    {
        $user = Auth::user();

        if (! setting('user_withdraw', 'permission') || ! $user->withdraw_status) {
            notify()->error(__('Withdraw currently unavailable!'));

            return back();
        } elseif (! setting('kyc_withdraw') && ! $user->kyc) {
            notify()->error(__('Please verify your KYC.'));

            return to_route('user.dashboard');
        }

        $withdrawAccounts = WithdrawAccount::query()
            ->where('user_id', $user->id)
            ->with(['method', 'wallet'])
            ->get();

        $wallets = [];

        if (setting('multiple_currency', 'permission')) {
            $wallets = UserWallet::query()
                ->with('coin')
                ->where('user_id', $user->id)
                ->get();
        }

        return view('frontend::user.withdraw.withdraw_money', ['withdrawAccounts' => $withdrawAccounts, 'wallets' => $wallets]);
    }

    public function withdrawMoneyNow(Request $request)
    {

        $user = User::find(Auth::user()->id);

        if (! setting('user_withdraw', 'permission') || ! $user->withdraw_status) {
            notify()->error(__('Withdraw currently unavailable!'));

            return back();
        } elseif (! setting('kyc_withdraw') && ! $user->kyc) {
            notify()->error(__('Please verify your KYC.'));

            return to_route('user.dashboard');
        }

        $withdrawOffDays = WithdrawalSchedule::where('status', false)->pluck('name')->toArray();

        $date = Carbon::now();

        $today = $date->format('l');

        if (in_array($today, $withdrawOffDays)) {
            notify()->error(__('Today is the off day of withdraw'));

            return back();
        }

        $validator = Validator::make($request->all(), [
            'amount' => ['required', 'numeric'],
            'withdraw_account' => 'required|exists:withdraw_accounts,id',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back()->withErrors($validator)->withInput();
        }

        $todayTransaction = Transaction::query()
            ->whereIn('type', [TxnType::Withdraw, TxnType::WithdrawAuto])
            ->where('user_id', $user->id)
            ->whereDate('created_at', Carbon::today())
            ->count();

        $dayLimit = (float) Setting('withdraw_day_limit', 'fee');

        if ($todayTransaction >= $dayLimit) {
            notify()->error(__('Today Withdraw limit has been reached'));

            return back();
        }

        $amount = (float) $request->amount;

        $withdrawAccount = WithdrawAccount::with('method', 'wallet')->find($request->withdraw_account);

        $withdrawMethod = $withdrawAccount->method;

        if ($amount < $withdrawMethod->min_withdraw || $amount > $withdrawMethod->max_withdraw) {
            $message = __('Please ensure the withdrawal amount is between :min to :max', [
                'min' => formatAmount($withdrawMethod->min_withdraw, $withdrawAccount->wallet->coin, true),
                'max' => formatAmount($withdrawMethod->max_withdraw, $withdrawAccount->wallet->coin, true),
            ]);
            notify()->error($message);

            return back();
        }

        $charge = $withdrawMethod->charge_type == 'percentage' ? (($withdrawMethod->charge / 100) * $amount) : $withdrawMethod->charge;

        $totalAmount = $amount + (float) $charge;

        $userWallet = $withdrawAccount->user_wallet_id == 0 ? 'default' : $withdrawAccount->user_wallet_id;

        if ($userWallet == 'default') {
            if ($user->balance < $totalAmount) {
                notify()->error(__('Insufficient Balance'));

                return back();
            }
        } else {

            $user_wallet = UserWallet::find($userWallet);

            if ($user_wallet->balance < $totalAmount) {
                notify()->error(__('Insufficient Balance'));

                return back();
            }
        }

        try {
            DB::beginTransaction();

            if ($userWallet == 'default') {
                $user->balance -= $totalAmount;
                $user->save();
            } else {
                $user_wallet = UserWallet::find($userWallet);
                $user_wallet->balance -= $totalAmount;
                $user_wallet->save();
            }

            $payAmount = $amount * $withdrawMethod->rate;

            $type = $withdrawMethod->type == 'auto' ? TxnType::WithdrawAuto : TxnType::Withdraw;

            $txnInfo = Transaction::create([
                'user_id' => $user->id,
                'amount' => $request->amount,
                'charge' => $charge,
                'final_amount' => $totalAmount,
                'wallet_type' => $userWallet,
                'description' => 'Withdraw With '.$withdrawAccount->method_name,
                'type' => $type,
                'status' => TxnStatus::Pending,
                'pay_amount' => $payAmount,
                'pay_currency' => $withdrawMethod->currency,
                'method' => $withdrawMethod->name,
                'manual_field_data' => json_decode($withdrawAccount->credentials, true),
            ]);

            DB::commit();

            $shortcodes = [
                '[[amount]]' => formatAmount($txnInfo->amount, $txnInfo->currency),
                '[[charge]]' => formatAmount($txnInfo->charge, $txnInfo->currency),
                '[[wallet]]' => data_get($txnInfo->wallet, 'coin.code', setting('site_currency', 'global')),
                '[[gateway]]' => $txnInfo->method,
                '[[request_at]]' => $txnInfo->created_at,
                '[[total_amount]]' => formatAmount($txnInfo->final_amount, $txnInfo->currency),
                '[[request_link]]' => route('admin.withdraw.pending'),
                '[[site_title]]' => setting('site_title', 'global'),
                '[[currency]]' => data_get($txnInfo->wallet, 'coin.code', setting('site_currency', 'global')),
            ];

            $this->sendNotify($user->email, 'admin_withdraw_request', 'Admin', $shortcodes, $user->phone, $user->id, route('admin.withdraw.pending'));

            $notify = [
                'card-header' => 'Withdraw Money',
                'title' => formatAmount($txnInfo->amount, $txnInfo->currency, true).' Withdraw Request Successful',
                'p' => 'The Withdraw Request has been successfully sent',
                'tnx_id' => $txnInfo->tnx,
                'amount' => formatAmount($txnInfo->amount, $txnInfo->currency, true),
                'charge' => formatAmount($txnInfo->charge, $txnInfo->currency, true),
                'type' => $txnInfo->type,
                'final_amount' => formatAmount($txnInfo->final_amount, $txnInfo->currency, true),
                'action' => route('user.withdrawMoney.index'),
                'status' => TxnStatus::Pending,
            ];

            return view('frontend::user.withdraw.success', ['notify' => $notify]);
        } catch (\Throwable $throwable) {
            DB::rollBack();

            notify()->error(__('Sorry! Something went wrong.'));

            return back();
        }
    }
}
