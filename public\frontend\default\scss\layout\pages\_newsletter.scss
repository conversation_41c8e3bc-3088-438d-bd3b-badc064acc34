@use '../../utils' as *;

/*----------------------------------------*/
/* Newsletter styles
/*----------------------------------------*/
.td-newsletter-section {
    background: #6556FF;


    @include dark-theme {
        background: linear-gradient(90deg, rgba(71, 118, 230, 0.30) 0%, rgba(142, 84, 233, 0.30) 100%);
    }
}

.newsletter-thumb {
    max-width: 504px;
    margin: 0 auto;
    position: relative;

    @media #{$xs,$sm} {
        max-width: 404px;
    }

    &::before {
        position: absolute;
        content: "";
        border-radius: 298px;
        background: #C5FFE2;
        filter: blur(100px);
        width: 298px;
        height: 298px;
        z-index: -1;
        top: 50%;
        inset-inline-start: 50%;
        transform: translate(-50%, -50%);

        @include rtl {
            inset-inline-start: auto;
            inset-inline-end: 50%;
        }
    }
}

.newsletter-contents {
    max-width: 500px;
    margin: 0 auto;

    .newsletter-form {
        position: relative;

        .input-clip {
            position: relative;
            padding: 1px;
            display: block;

            &::before {
                position: absolute;
                content: "";
                inset-inline-start: 0;
                top: 0;
                transition: all 0.3s;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, rgba($white, $alpha: 0.30), rgba($white, $alpha: 0.30) 100%);
                clip-path: polygon(0.512% 0.962%, 99.488% 0.962%, 99.488% 0.962%, 99.551% 0.999%, 99.61% 1.109%, 99.665% 1.284%, 99.715% 1.518%, 99.76% 1.806%, 99.798% 2.143%, 99.829% 2.521%, 99.853% 2.934%, 99.867% 3.378%, 99.872% 3.846%, 99.872% 75.331%, 99.872% 75.331%, 99.871% 75.531%, 99.868% 75.728%, 99.864% 75.923%, 99.858% 76.115%, 99.85% 76.303%, 99.84% 76.486%, 99.829% 76.663%, 99.816% 76.835%, 99.801% 77%, 99.785% 77.158%, 99.76% 77.37%, 96.99% 98.193%, 96.99% 98.193%, 96.968% 98.348%, 96.944% 98.489%, 96.919% 98.614%, 96.893% 98.724%, 96.866% 98.819%, 96.838% 98.897%, 96.809% 98.958%, 96.78% 99.002%, 96.75% 99.029%, 96.719% 99.038%, 0.512% 99.038%, 0.512% 99.038%, 0.449% 99.001%, 0.39% 98.891%, 0.335% 98.716%, 0.285% 98.482%, 0.24% 98.194%, 0.202% 97.857%, 0.171% 97.479%, 0.147% 97.066%, 0.133% 96.622%, 0.128% 96.154%, 0.128% 3.846%, 0.128% 3.846%, 0.132% 3.407%, 0.145% 2.988%, 0.166% 2.596%, 0.193% 2.233%, 0.228% 1.907%, 0.267% 1.62%, 0.313% 1.379%, 0.362% 1.188%, 0.416% 1.052%, 0.472% 0.977%, 0.512% 0.962%);

                @include dark-theme {
                    background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
                }
            }

            .clip-inner {
                position: relative;
                z-index: 3;
                width: 100%;
                clip-path: polygon(0.512% 0.962%, 99.488% 0.962%, 99.488% 0.962%, 99.551% 0.999%, 99.61% 1.109%, 99.665% 1.284%, 99.715% 1.518%, 99.76% 1.806%, 99.798% 2.143%, 99.829% 2.521%, 99.853% 2.934%, 99.867% 3.378%, 99.872% 3.846%, 99.872% 75.331%, 99.872% 75.331%, 99.871% 75.531%, 99.868% 75.728%, 99.864% 75.923%, 99.858% 76.115%, 99.85% 76.303%, 99.84% 76.486%, 99.829% 76.663%, 99.816% 76.835%, 99.801% 77%, 99.785% 77.158%, 99.76% 77.37%, 96.99% 98.193%, 96.99% 98.193%, 96.968% 98.348%, 96.944% 98.489%, 96.919% 98.614%, 96.893% 98.724%, 96.866% 98.819%, 96.838% 98.897%, 96.809% 98.958%, 96.78% 99.002%, 96.75% 99.029%, 96.719% 99.038%, 0.512% 99.038%, 0.512% 99.038%, 0.449% 99.001%, 0.39% 98.891%, 0.335% 98.716%, 0.285% 98.482%, 0.24% 98.194%, 0.202% 97.857%, 0.171% 97.479%, 0.147% 97.066%, 0.133% 96.622%, 0.128% 96.154%, 0.128% 3.846%, 0.128% 3.846%, 0.132% 3.407%, 0.145% 2.988%, 0.166% 2.596%, 0.193% 2.233%, 0.228% 1.907%, 0.267% 1.62%, 0.313% 1.379%, 0.362% 1.188%, 0.416% 1.052%, 0.472% 0.977%, 0.512% 0.962%);
                background: #3D3587;

                &::before {
                    position: absolute;
                    top: 0;
                    inset-inline-start: 0;
                    content: "";
                    z-index: -1;
                    width: 100%;
                    height: 100%;
                    clip-path: polygon(0.512% 0.962%, 99.488% 0.962%, 99.488% 0.962%, 99.551% 0.999%, 99.61% 1.109%, 99.665% 1.284%, 99.715% 1.518%, 99.76% 1.806%, 99.798% 2.143%, 99.829% 2.521%, 99.853% 2.934%, 99.867% 3.378%, 99.872% 3.846%, 99.872% 75.331%, 99.872% 75.331%, 99.871% 75.531%, 99.868% 75.728%, 99.864% 75.923%, 99.858% 76.115%, 99.85% 76.303%, 99.84% 76.486%, 99.829% 76.663%, 99.816% 76.835%, 99.801% 77%, 99.785% 77.158%, 99.76% 77.37%, 96.99% 98.193%, 96.99% 98.193%, 96.968% 98.348%, 96.944% 98.489%, 96.919% 98.614%, 96.893% 98.724%, 96.866% 98.819%, 96.838% 98.897%, 96.809% 98.958%, 96.78% 99.002%, 96.75% 99.029%, 96.719% 99.038%, 0.512% 99.038%, 0.512% 99.038%, 0.449% 99.001%, 0.39% 98.891%, 0.335% 98.716%, 0.285% 98.482%, 0.24% 98.194%, 0.202% 97.857%, 0.171% 97.479%, 0.147% 97.066%, 0.133% 96.622%, 0.128% 96.154%, 0.128% 3.846%, 0.128% 3.846%, 0.132% 3.407%, 0.145% 2.988%, 0.166% 2.596%, 0.193% 2.233%, 0.228% 1.907%, 0.267% 1.62%, 0.313% 1.379%, 0.362% 1.188%, 0.416% 1.052%, 0.472% 0.977%, 0.512% 0.962%);
                }

                input {
                    background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) -1.65%, rgba(142, 84, 233, 0.3) 43.73%);
                    border: 0;
                    height: 52px;
                    font-size: 14px;
                    color: rgba($white, $alpha: 0.60);
                    padding-inline-end: 140px;

                    @include td-placeholder {
                        color: rgba($white, $alpha: 0.60);
                    }
                }
            }
        }

        .td-btn {
            position: absolute;
            top: 50%;
            inset-inline-end: 6px;
            z-index: 11;
            transform: translateY(-50%);
            background: linear-gradient(90deg, #4776E6 36.36%, #8E54E9 136.36%);
        }
    }
}

.newsletter-shapes {
    .sky {
        position: relative;
        width: 100%;
        height: 200px;
        position: absolute;
        top: 0;
    }

    .cloud {
        position: absolute;
        animation: float linear infinite;
        opacity: 0.1;
    }

    .cloud1 {
        height: 60px;
        top: 30px;
        left: -80px;
        animation-duration: 60s;
    }

    .cloud1::before {
        height: 40px;
        left: 40px;
        top: 10px;
    }

    .cloud1::after {
        height: 30px;
        left: 20px;
        top: 30px;
    }

    .cloud2 {
        height: 80px;
        top: 60px;
        left: -100px;
        animation-duration: 80s;
    }

    .cloud3 {
        height: 70px;
        top: 100px;
        left: -120px;
        animation-duration: 100s;
    }

    .cloud4 {
        width: 65px;
        height: 65px;
        top: 150px;
        left: -150px;
        animation-duration: 90s;
    }

    @keyframes float {
        0% {
            transform: translateX(0);
        }

        100% {
            transform: translateX(120vw);
        }
    }

}