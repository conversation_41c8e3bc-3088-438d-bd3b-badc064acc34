<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\TxnType;
use App\Http\Controllers\Controller;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MiningController extends Controller
{
    public function index()
    {
        return view('frontend::user.mining.index');
    }

    public function miningHistory(Request $request)
    {
        $user = Auth::user();
        $transactions = Transaction::with('wallet.coin', 'userMining.scheme.miner.coin')
        ->whereHas('userMining')
            ->where('user_id', $user->id)
            ->when($request->filled('txn'), function ($query) use ($request) {
                $query->where('tnx', 'like', '%'.$request->input('txn').'%');
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->whereRelation('userMining', 'status', $request->input('status'));
            })
            ->when($request->filled('wallet'), function ($query) use ($request) {
                $query->whereHas('userMining.scheme.miner', function ($query) use ($request) {
                    $query->where('coin_id', $request->input('wallet'));
                });
            })
            ->whereIn('type', [TxnType::PlanPurchase->value, TxnType::PlanPurchaseManual->value])
            ->when($request->filled('date'), function ($query) use ($request) {
                if (str($request->input('date'))->contains('to')) {
                    $dates = explode(' to ', $request->input('date'));
                    $dates = array_map(function ($date) {
                        return date('Y-m-d', strtotime($date));
                    }, $dates);
                    $query->whereBetween(DB::raw('DATE(created_at)'), $dates);
                } else {
                    $query->whereDate('created_at', $request->input('date'));
                }
            })
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('frontend::user.mining.history', ['transactions' => $transactions]);
    }

    public function details(Request $request, Transaction $transaction)
    {
        abort_if($transaction->user_id != Auth::id(), 404);

        $currentWalletBalance = $transaction->user->wallets()->where('coin_id', $transaction->scheme->miner->coin->id)->first()?->balance ?? 0;

        $liveCalData['currentWalletBalance'] = $currentWalletBalance;
        if ($transaction->userMining()->exists()) {
            $transaction->load('userMining');

            $returnAmountRange = null;

            if ($transaction->scheme->return_amount_type == 'fixed') {
                $returnAmountRange = [$transaction->scheme->return_amount_value, $transaction->scheme->return_amount_value];
            } else {
                $returnAmountRange = [$transaction->scheme->return_min_amount, $transaction->scheme->return_max_amount];
            }

            $liveCalData['earningPerPeriod'] = $returnAmountRange;
            $liveCalData['returnDuration'] = $transaction->scheme->return_period_hours * 60 * 60;

            $lastMiningTime = $transaction->userMining->last_mining_time ?? $transaction->userMining->created_at;
            $liveCalData['alreadyPassedTime'] = now()->parse($lastMiningTime)->diffInSeconds();
            $liveCalData['remainingTime'] = now()->parse($transaction->userMining->next_mining_time ?? now())->diffInSeconds();

            if ($transaction->scheme->return_amount_type == 'fixed') {
                $avgEarning = $transaction->scheme->return_amount_value;
            } else {
                $avgEarning = ($transaction->scheme->return_min_amount + $transaction->scheme->return_max_amount) / 2;
            }

            $liveCalData['earningPerSecond'] = $avgEarning / $liveCalData['returnDuration'];

            $liveCalData['alreadyMinedAmountInBackground'] =
            $liveCalData['earningPerSecond'] * $liveCalData['alreadyPassedTime'];

        }

        return view('frontend::user.mining.details', ['transaction' => $transaction, 'liveCalData' => $liveCalData]);
    }
}
