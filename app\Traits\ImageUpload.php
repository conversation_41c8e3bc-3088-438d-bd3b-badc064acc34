<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait ImageUpload
{
    public function imageUploadTrait($query, $old = null, $folderPath = 'images', $allowExt = ['jpeg', 'png', 'jpg', 'gif', 'svg', 'webp']): string
    {
        $ext = strtolower($query->getClientOriginalExtension());

        if ($query->getSize() > 5100000) {
            notify()->error(__('Max file size:5MB '));
        }

        if (! in_array($ext, $allowExt)) {
            notify()->error(__('Only allow : :extensions', ['extensions' => implode(',', $allowExt)]));
        }

        if ($old != null) {
            self::fileDelete($old);
        }

        $image_name = Str::random(20);
        $image_full_name = $image_name.'.'.$ext;
        $upload_path = 'global/uploads/'.$folderPath.'/';
        $destination = public_path($upload_path);
        $query->move($destination, $image_full_name);

        return $upload_path.$image_full_name;
    }

    protected function fileDelete($path)
    {
        $path = public_path($path);

        if (file_exists($path)) {
            unlink($path);
        }
    }

    private function deleteDirectory($dirPath)
    {
        if (! is_dir($dirPath)) {
            return;
        }

        $files = array_diff(scandir($dirPath), ['.', '..']);

        foreach ($files as $file) {
            $filePath = $dirPath.DIRECTORY_SEPARATOR.$file;
            if (is_dir($filePath)) {
                $this->deleteDirectory($filePath);
            } else {
                unlink($filePath);
            }
        }

        rmdir($dirPath);
    }
}
