@use '../../utils' as *;

/*----------------------------------------*/
/*  Header Styles
/*----------------------------------------*/

// Header transparent
.header-transparent {
  position: absolute;
  inset-inline-start: 0;
  width: 100%;
  z-index: 99;
}

// Active-sticky
.active-sticky {
  position: fixed !important;
  top: 0;
  z-index: 111;
  inset-inline-end: 0;
  inset-inline-start: 0;
  width: 100%;
  animation: sticky 0.3s;
  -webkit-animation: sticky 0.3s;
}

// Mode-switcher
.mode-switcher {
  button {
    position: relative;
    top: -3px;
  }
}

// Heder one styles
.header-style-one {

  .header-clip-path {
    position: relative;
    display: block;
    margin-top: 10px;
    padding: 1px;

    @media #{$xs,$sm,$md} {
      margin: 10px 0px 0;
    }

    .header-clip-path-inner {
      display: inline-flex;
      position: relative;
      width: 100%;
      z-index: 31;

      @media #{$xs,$sm,$md} {
        clip-path: none;
      }

      &::after {
        position: absolute;
        content: "";
        background: var(--td-white);
        clip-path: polygon(2.066% 0.568%, 99.338% 0.568%, 99.338% 0.568%, 99.344% 0.573%, 99.35% 0.587%, 99.356% 0.611%, 99.362% 0.644%, 99.367% 0.686%, 99.373% 0.737%, 99.378% 0.797%, 99.383% 0.864%, 99.388% 0.94%, 99.393% 1.024%, 99.399% 1.158%, 99.952% 14.202%, 99.952% 14.202%, 99.956% 14.309%, 99.959% 14.423%, 99.963% 14.542%, 99.966% 14.666%, 99.968% 14.794%, 99.97% 14.926%, 99.972% 15.062%, 99.973% 15.2%, 99.974% 15.341%, 99.974% 15.483%, 99.974% 65.78%, 99.974% 65.78%, 99.974% 65.931%, 99.973% 66.081%, 99.972% 66.228%, 99.97% 66.371%, 99.968% 66.51%, 99.965% 66.645%, 99.961% 66.774%, 99.958% 66.897%, 99.954% 67.013%, 99.949% 67.122%, 99.942% 67.263%, 98.081% 99.044%, 98.081% 99.044%, 98.076% 99.116%, 98.072% 99.181%, 98.067% 99.238%, 98.062% 99.289%, 98.057% 99.332%, 98.051% 99.368%, 98.046% 99.396%, 98.041% 99.416%, 98.035% 99.428%, 98.03% 99.432%, 0.11% 99.432%, 0.11% 99.432%, 0.096% 99.407%, 0.083% 99.337%, 0.071% 99.223%, 0.06% 99.071%, 0.05% 98.884%, 0.042% 98.666%, 0.035% 98.421%, 0.03% 98.152%, 0.027% 97.864%, 0.026% 97.561%, 0.026% 42.625%, 0.026% 42.625%, 0.026% 42.488%, 0.026% 42.354%, 0.028% 42.221%, 0.029% 42.091%, 0.031% 41.964%, 0.033% 41.84%, 0.036% 41.721%, 0.039% 41.606%, 0.043% 41.495%, 0.047% 41.391%, 0.053% 41.251%, 2.009% 1.066%, 2.009% 1.066%, 2.013% 0.985%, 2.017% 0.911%, 2.022% 0.844%, 2.027% 0.784%, 2.032% 0.731%, 2.037% 0.685%, 2.042% 0.646%, 2.047% 0.615%, 2.052% 0.592%, 2.058% 0.576%, 2.066% 0.568%);
        width: 100%;
        height: 100%;
        top: 0;
        inset-inline-start: 0;
        z-index: -1;

        @media #{$xs,$sm,$md} {
          clip-path: none;
        }

        @include dark-theme {
          background: var(--td-void);
        }
      }
    }

    &::before {
      position: absolute;
      inset-inline-start: 0;
      top: 0;
      transition: all .3s;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, #091B52 0%, #0094FF 100%);
      opacity: 0.6;
      content: "";
      clip-path: polygon(2.066% 0.568%, 99.338% 0.568%, 99.338% 0.568%, 99.344% 0.573%, 99.35% 0.587%, 99.356% 0.611%, 99.362% 0.644%, 99.367% 0.686%, 99.373% 0.737%, 99.378% 0.797%, 99.383% 0.864%, 99.388% 0.94%, 99.393% 1.024%, 99.399% 1.158%, 99.952% 14.202%, 99.952% 14.202%, 99.956% 14.309%, 99.959% 14.423%, 99.963% 14.542%, 99.966% 14.666%, 99.968% 14.794%, 99.97% 14.926%, 99.972% 15.062%, 99.973% 15.2%, 99.974% 15.341%, 99.974% 15.483%, 99.974% 65.78%, 99.974% 65.78%, 99.974% 65.931%, 99.973% 66.081%, 99.972% 66.228%, 99.97% 66.371%, 99.968% 66.51%, 99.965% 66.645%, 99.961% 66.774%, 99.958% 66.897%, 99.954% 67.013%, 99.949% 67.122%, 99.942% 67.263%, 98.081% 99.044%, 98.081% 99.044%, 98.076% 99.116%, 98.072% 99.181%, 98.067% 99.238%, 98.062% 99.289%, 98.057% 99.332%, 98.051% 99.368%, 98.046% 99.396%, 98.041% 99.416%, 98.035% 99.428%, 98.03% 99.432%, 0.11% 99.432%, 0.11% 99.432%, 0.096% 99.407%, 0.083% 99.337%, 0.071% 99.223%, 0.06% 99.071%, 0.05% 98.884%, 0.042% 98.666%, 0.035% 98.421%, 0.03% 98.152%, 0.027% 97.864%, 0.026% 97.561%, 0.026% 42.625%, 0.026% 42.625%, 0.026% 42.488%, 0.026% 42.354%, 0.028% 42.221%, 0.029% 42.091%, 0.031% 41.964%, 0.033% 41.84%, 0.036% 41.721%, 0.039% 41.606%, 0.043% 41.495%, 0.047% 41.391%, 0.053% 41.251%, 2.009% 1.066%, 2.009% 1.066%, 2.013% 0.985%, 2.017% 0.911%, 2.022% 0.844%, 2.027% 0.784%, 2.032% 0.731%, 2.037% 0.685%, 2.042% 0.646%, 2.047% 0.615%, 2.052% 0.592%, 2.058% 0.576%, 2.066% 0.568%);
      border-radius: 2px;

      @media #{$xs,$sm,$md} {
        clip-path: none;
      }
    }
  }

  .header-logo {
    a {
      display: block
    }

    .logo-white {
      display: none;
    }

    img {
      height: 30px;

      @media #{$xs} {
        height: 26px;
        object-fit: cover;
      }
    }
  }

  .header-inner {
    @include flexbox();
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    width: 100%;
    z-index: 4;

    @media #{$sm,$md} {
      padding: 14px 20px;
    }

    @media #{$xs} {
      padding: 16px 20px;
    }
  }

  .header-left {
    display: flex;
    align-items: center;
    column-gap: 80px;

    @media #{$lg} {
      column-gap: 16px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 14px;

    @media #{$xs} {
      gap: 6px;
    }

    .header-btns-wrap {
      display: flex;
      align-items: center;
      column-gap: 16px;

      @media #{$xxs} {
        column-gap: 8px;
      }

      .td-btn {
        height: 40px;
        min-width: 80px;
      }
    }

    .header-quick-actions {
      column-gap: 16px;

      @media #{$xxs} {
        column-gap: 8px;
      }
    }
  }
}

// Language css
.language-nav {
  background-color: transparent;
  position: relative;
  @include inline-flex();
  align-items: center;
  justify-content: center;

  .translate_wrapper {
    &.active {
      .more_lang {
        display: block;
        position: absolute;
        top: calc(100% + 32px);
        inset-inline-start: 0;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
        background: #171c35;
        width: 144px;
        z-index: 31;
        border-radius: 4px;
        border: 1px solid #D1D4DA;
        background: var(--td-white);

        @include dark-theme {
          background-color: #0a1229;
          border-color: #0B277A;
        }
      }
    }
  }

  .current_lang {
    cursor: pointer;
    overflow: hidden;
    margin-inline-end: 5px;

    .lang {
      .flag-icon {
        width: 24px;
        height: 24px;
        @include inline-flex();
        align-items: center;
        background-size: cover;
        border-radius: 40px;
      }

      svg * {
        stroke: rgba($heading, $alpha: 1);

        @include dark-theme {
          stroke: rgba($white, $alpha: 1);
        }
      }
    }
  }

  .lang.selected {
    display: none;
  }

  .lang {
    >span {
      &.lang-txt {
        @include inline-flex();
        margin-inline-end: 4px;
        font-size: 16px;
        font-weight: 600;
        color: #484848;
        text-transform: capitalize;

        @media #{$xxs} {
          font-size: 14px;
        }

        @include dark-theme {
          color: var(--td-white);
        }
      }
    }

    span {
      span {
        color: var(--td-text-secondary);
        margin-inline-start: 5px;
        transition: 0.3s;
      }
    }
  }

  .more_lang {
    transform: translateY(-20px);
    opacity: 0;
    cursor: pointer;
    display: none;
    transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
    z-index: 1;

    .lang {
      padding: 7px 10px;
      @include flexbox();
      transition: .3s;

      .lang-txt {
        font-size: 14px;
      }

      i {
        width: 24px;
        height: 24px;
        @include inline-flex();
        align-items: center;
        background-size: cover;
        border-radius: 40px;
      }

      &:hover {
        background: #E6EFFC;
        color: var(--td-heading);

        @include dark-theme {
          color: var(--td-white);
          background: #1A2237;
        }

        span {
          color: var(--td-heading);

          @include dark-theme {
            color: var(--td-white);
          }
        }
      }
    }
  }

  .more_lang.active {
    opacity: 1;
    transform: translateY(0px);
  }
}