@use "../utils" as *;

/*----------------------------------------*/
/* Alert Styles
/*----------------------------------------*/

.td-alert-box {
	background: var(--td-white);
	padding: rem(12) rem(20) rem(12) rem(16);
	z-index: 1;
	position: relative;
	transition: .3s;
	@include flexbox();
	column-gap: rem(12);
	align-items: center;
	width: 320px;
	border: 1px solid var(--td-border-primary);
	border-radius: 10px;

	@media #{$xxs} {
		padding: rem(10) rem(12) rem(10);
		width: 300px;
	}

	&.hidden {
		opacity: 0;
		transform: translateY(-50%, rem(20));
		pointer-events: none;
	}

	&.has-success {
		border-color: rgba($toastr-success, $alpha: 0.3);
		background: #E9F9ED;

		@include dark-theme {
			background: #061E27;
		}
	}

	&.has-warning {
		border-color: rgba($toastr-warning, $alpha: 0.3);
		background: #FEF2E5;

		@include dark-theme {

			background: #393026;
		}
	}

	&.has-danger {
		border-color: rgba($toastr-danger, $alpha: 0.3);
		background: #FCECEE;

		@include dark-theme {
			background-color: #191128;
		}
	}

	&.has-info {
		border-color: rgba($toastr-info, $alpha: 0.3);
		background: #EAF3FD;

		@include dark-theme {
			background-color: #0B1935;
		}
	}

	.alert-content {
		@include flexbox();
		align-items: center;
		column-gap: rem(12);
		flex-grow: 1;

		@media #{$xs} {
			column-gap: rem(12);
		}

		.alert-title {
			font-size: 18px;
			font-weight: 500;
			font-family: var(--td-ff-body);

			@media #{$xs} {
				font-size: rem(14);
			}
		}

		.alert-message {
			font-size: 16px;
			position: relative;
			color: var(--td-heading);
			font-weight: 500;
		}
	}

	.alert-icon {
		flex: 0 0 auto;

		svg {
			width: 24px;
			height: 24px;
		}
	}

	.close-btn {
		padding: 5px;
		position: absolute;
		inset-inline-end: -12px;
		top: -12px;
		display: flex;
		width: 24px;
		height: 24px;
		justify-content: center;
		align-items: center;
		gap: 10px;
		background-color: #FFE4E8;
		border-radius: 50%;

		@include dark-theme {
			background-color: var(--td-white);
		}
	}
}

.alert-show-status {
	position: fixed;
	top: rem(16);
	inset-inline-end: rem(16);
	z-index: 999;
}

/*----------------------------------------*/
/* Identity Alert Styles
/*----------------------------------------*/
.identity-alert {
	padding: rem(20);
	border: 2px dashed;
	border-radius: rem(12);
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px 12px;
	flex-wrap: wrap;

	&.fill-success {
		border-radius: 16px;
		border: 1px dashed rgba(0, 204, 0, 0.20);
		background: rgba(0, 204, 0, 0.19);
	}

	&.fill-info {
		border-radius: 16px;
		border: 1px dashed rgba(65, 177, 252, 0.20);
		background: rgba(65, 177, 252, 0.07);
	}

	.left-contents {
		display: flex;
		align-items: center;
	}

	.right-actions {
		display: flex;
		align-items: center;
		gap: 12px 12px;
		flex-wrap: wrap;
	}

	@media #{$xs} {
		padding: rem(16);
	}

	.icon {
		height: rem(50);
		width: rem(50);
		text-align: center;
		border-radius: rem(30);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-inline-end: rem(15);
		font-size: rem(20);
		flex: 0 0 auto;

		svg {
			height: rem(22);
		}
	}

	.contents {
		.title {
			font-size: rem(16);
			font-weight: 500;
			margin-bottom: rem(5);
		}

		.content {
			p {
				display: inline-block;
				font-size: rem(14);
				margin-bottom: 0;

				@include dark-theme {
					color: rgba($white, $alpha: 0.7);
				}
			}

			.underline-btn {
				font-weight: 500;
				position: relative;
				font-size: rem(14);
				color: var(--td-heading);

				&::after {
					content: "";
					position: absolute;
					height: rem(1);
					transition: .3s;
					inset-inline-start: auto;
					bottom: -2px;
					background: currentColor;
					width: 100%;
					inset-inline-end: 0;
				}

				&:hover {
					color: var(--td-heading);

					&::after {
						width: 0%;
						inset-inline-start: 0;
						inset-inline-end: auto;
					}
				}
			}
		}
	}

	&.danger {
		border: 2px dashed rgba(243, 65, 65, 0.4);
		background: rgba(243, 65, 65, 0.1);

		.icon {
			background: #FFE5E5;
			color: var(--td-white);
		}
	}

	&.warning {
		background: rgba(255, 170, 0, 0.05);
		border-color: rgba(255, 170, 0, 0.3);

		.icon {
			background: #FFF6E5;
			color: #0C0C14
		}
	}

	&.success {
		border: 2px dashed rgba(0, 204, 0, 0.3);
		background: rgba(0, 204, 0, 0.05);

		.icon {
			background: #E5FFE5;
		}
	}

	&.info {
		background: rgba(65, 177, 252, 0.07);
		border: 2px dashed rgba(65, 177, 252, 0.2);

		.icon {
			background: #E5EBFF;
		}
	}
}