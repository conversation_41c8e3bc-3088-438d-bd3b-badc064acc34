<?php

/**
 * Exception for 400 Bad Request responses
 */

namespace WpOrg\Requests\Exception\Http;

use WpOrg\Requests\Exception\Http;

/**
 * Exception for 400 Bad Request responses
 */
final class Status400 extends Http
{
    /**
     * HTTP status code
     *
     * @var int
     */
    protected $code = 400;

    /**
     * Reason phrase
     *
     * @var string
     */
    protected $reason = 'Bad Request';
}
