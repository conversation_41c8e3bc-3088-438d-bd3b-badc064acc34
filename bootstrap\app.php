<?php

use App\Http\Controllers\Frontend\PageController;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        then: function () {
            // Admin Routes
            Route::prefix(setting('site_admin_prefix', 'global'))
                ->name('admin.')
                ->middleware(['web', 'auth:admin'])
                ->group(base_path('routes/admin.php'));

            // Payment Routes
            Route::middleware(['web'])
                ->group(base_path('routes/payment.php'));

            // Dyanmic Page
            Route::middleware('web')
                ->get('/{page}', PageController::class)
                ->name('page');
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\Localization::class,
            'install_check',
            'trans',
            \App\Http\Middleware\XSS::class,
            \App\Http\Middleware\IsMaintenance::class,
        ]);

        $middleware->preventRequestsDuringMaintenance(['/admin*']);

        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'auth' => \App\Http\Middleware\Authenticate::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            '2fa' => \App\Http\Middleware\TwoFaCheck::class,
            'check_feature' => \App\Http\Middleware\CheckFeatureAccess::class,
            'check_deactivation' => \App\Http\Middleware\CheckDeactivation::class,
        ]);

        $middleware->validateCsrfTokens(except: [
            '*gateway/coingate/callback',
            '*ipn*',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
