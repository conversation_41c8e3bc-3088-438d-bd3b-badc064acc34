@use '../../utils' as *;

/*----------------------------------------*/
/* Mobile menu css
/*----------------------------------------*/
.mobile-menu {
	margin-bottom: 30px;

	ul {
		list-style: none;

		li {
			position: relative;

			&>a {
				padding: 14px 0;
				font-size: 15px;
				font-weight: 600;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #484848;
				font-family: var(--td-ff-heading);
				background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
				-webkit-background-clip: text;
				background-clip: text;

				@include dark-theme {
					color: var(--td-white);
				}

				i {
					width: 24px;
					height: 24px;
					display: inline-flex;
					align-items: center;
					justify-content: center;
					background-color: #ddd;
				}
			}

			ul {
				padding-inline-start: 5%;

				@media #{$xxs} {
					padding-inline-start: 3%;
				}

			}

			&:not(:last-child) {
				&>a {
					border-bottom: 1px solid rgba($heading, $alpha: 0.3);

					@include dark-theme {
						border-color: rgba($white, $alpha: 0.1);
					}
				}
			}

			&.active {
				&>a {
					-webkit-text-fill-color: transparent;
				}

				&>.tp-menu-close {
					color: var(--td-white);
					background: var(--td-black);
					border-color: var(--td-black);

					i {
						-webkit-transform: rotate(90deg);
						-moz-transform: rotate(90deg);
						-ms-transform: rotate(90deg);
						-o-transform: rotate(90deg);
						transform: rotate(90deg);
					}
				}
			}

			.td-dp-menu {
				display: none;
				padding-inline-start: 20px;
			}
		}
	}

	.td-mega-menu {
		padding: 0;
		padding-top: 30px;
		box-shadow: none;
		transform: inherit;
	}
}

.td-mega-menu {
	position: absolute;
	top: 100%;
	inset-inline-start: 0;
	inset-inline-end: 0;
	opacity: 0;
	width: 100%;
	z-index: 99;
	margin: 0 auto;
	background: var(--td-white);
	visibility: hidden;
	transform-origin: top;
	transition: 0.4s;
	transition-duration: 0.1s;
	padding: 30px 30px 10px 30px;
	transform: perspective(300px) rotateX(-18deg);
	box-shadow: 0px 10px 30px 0px rgba(25, 25, 26, 0.1);
}