<?php

namespace Razorpay\Tests;

class EmandateTest extends TestCase
{
    /**
     * Specify unique customer id, invoice id & token id
     * for example cust_IEfAt3ruD4OEzo, inv_IF37M4q6SdOpjT & token_IF1ThOcFC9J7pU
     */
    private $customerId = 'cust_IEfAt3ruD4OEzo';

    private $invoiceId = 'inv_JM5rC3ddYKVWgy';

    private $tokenId = 'token_IF1ThOcFC9J7pU';

    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * Create customer
     */
    public function test_create_customer_emandate()
    {
        $data = $this->api->customer->create(['name' => 'Razorpay User 71', 'email' => '<EMAIL>', 'contact' => **********, 'fail_existing' => '0']);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue(in_array('customer', $data->toArray()));
    }

    /**
     * Create Order
     */
    public function test_create_order_emandate()
    {
        $data = $this->api->order->create(['amount' => 100, 'currency' => 'INR', 'method' => 'emandate', 'customer_id' => $this->customerId, 'receipt' => 'Receipt No. '.time(), 'notes' => ['notes_key_1' => 'Beam me up Scotty', 'notes_key_2' => 'Engage'], 'token' => ['auth_type' => 'netbanking', 'max_amount' => 9999900, 'expire_at' => **********, 'notes' => ['notes_key_1' => 'Tea, Earl Grey, Hot', 'notes_key_2' => 'Tea, Earl Grey… decaf.'], 'bank_account' => ['beneficiary_name' => 'Gaurav Kumar', 'account_number' => '****************', 'account_type' => 'savings', 'ifsc_code' => 'HDFC0000001']]]);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertArrayHasKey('id', $data->toArray());
    }

    /**
     * Create registration link
     */
    public function test_create_subscription_registration_emandate()
    {
        $data = $this->api->subscription->createSubscriptionRegistration(['customer' => ['name' => 'Gaurav Kumar', 'email' => '<EMAIL>', 'contact' => '**********'], 'type' => 'link', 'amount' => 100, 'currency' => 'INR', 'description' => 'Registration Link for Gaurav Kumar', 'subscription_registration' => ['method' => 'card', 'max_amount' => '500', 'expire_at' => strtotime('+1 month')], 'receipt' => 'Receipt No. '.time(), 'email_notify' => 1, 'sms_notify' => 1, 'expire_by' => strtotime('+1 month'), 'notes' => ['note_key 1' => 'Beam me up Scotty', 'note_key 2' => 'Tea. Earl Gray. Hot.']]);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue(in_array('customer', $data->toArray()));
    }

    /**
     * Send/Resend notifications
     */
    public function test_send_notification()
    {
        $data = $this->api->invoice->fetch($this->invoiceId)->notifyBy('sms');

        $this->assertTrue(is_array($data));

        $this->assertArrayHasKey('success', $data);

    }

    /**
     * Fetch token by payment ID
     */
    public function test_fetch_token_by_payment_id()
    {
        $payment = $this->api->payment->all();

        $data = $this->api->payment->fetch($payment['items'][0]['id']);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertArrayHasKey('id', $data->toArray());
    }

    /**
     * Fetch tokens by customer id
     */
    public function test_fetch_token_by_customer_id()
    {
        $data = $this->api->customer->fetch($this->customerId)->tokens()->all();

        $this->assertTrue(is_array($data->toArray()));
    }
}
