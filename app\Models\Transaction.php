<?php

namespace App\Models;

use App\Enums\TxnStatus;
use App\Enums\TxnType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Transaction extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = ['day'];

    protected $searchable = [
        'amount',
        'tnx',
        'type',
        'method',
        'description',
        'status',
        'created_at',
    ];

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($transaction) {
            $transaction->tnx = 'TRX'.strtoupper(Str::random(10));
        });
    }

    public function scopeStatus($query, $status)
    {
        if ($status && $status != 'all') {
            return $query->where('status', $status);
        }

        return $query;
    }

    /**
     * Scope a query to only include currency
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCurrency($query, $currency)
    {
        if ($currency == 'default') {
            return $query->where('wallet_type', $currency);
        } else {
            return $query->whereHas('wallet', function ($query) use ($currency) {
                $query->where('coin_id', $currency);
            });
        }
    }

    public function wallet()
    {
        return $this->belongsTo(UserWallet::class, 'wallet_type');
    }

    public function scopeProfit($query)
    {
        return $query->whereIn('type', [TxnType::Referral]);
    }

    public function scopeSearch($query, $search)
    {
        if ($search) {
            return $query->where(function ($query) use ($search) {
                $query->whereHas('user', function ($query) use ($search) {
                    $query->where('username', 'like', '%'.$search.'%');
                })->orWhere('tnx', 'like', '%'.$search.'%')->orWhere('description', 'like', '%'.$search.'%');
            });
        }

        return $query;
    }

    public function scopeType($query, $type)
    {
        if ($type && $type != 'all') {
            return $query->where('type', $type);
        }

        return $query;
    }

    public function scopePending($query)
    {
        return $query->where('status', TxnStatus::Pending->value);
    }

    public function scopeRejected($query)
    {
        return $query->where('status', TxnStatus::Failed->value);
    }

    protected function createdAt(): Attribute
    {
        return Attribute::make(get: function () {
            return $this->attributes['created_at'] ? Carbon::parse($this->attributes['created_at'])->format('d M Y, h:i A') : '';
        });
    }

    protected function day(): Attribute
    {
        return Attribute::make(get: function () {
            return $this->attributes['created_at'] ? Carbon::parse($this->attributes['created_at'])->format('d M') : '';
        });
    }

    public function scopeTnx($query, $tnx)
    {
        return $query->where('tnx', $tnx)->first();
    }

    public function referral()
    {
        return $this->referrals()->where('type', '=', $this->target_type);
    }

    public function referrals()
    {
        return $this->hasMany(Referral::class, 'referral_target_id', 'target_id')->where('type', '=', $this->target_type);
    }

    public function user()
    {
        return $this->belongsTo(User::class)->withDefault();
    }

    public function scheme()
    {
        return $this->belongsTo(Scheme::class, 'scheme_id');
    }

    public function fromUser()
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    public function totalWithdraw()
    {
        return $this->where('status', TxnStatus::Success)->where(function ($query) {
            $query->where('type', TxnType::Withdraw)
                ->orWhere('type', TxnType::WithdrawAuto);
        });
    }

    protected function method(): Attribute
    {
        return new Attribute(
            get: fn ($value) => ucwords($value),
        );
    }

    /**
     * Get the type
     *
     * @param  string  $value
     * @return string
     */
    public function getTypeAttribute($value)
    {
        return $this->attributes['type']?->value ?? $this->attributes['type'];
    }

    /**
     * Get the type as text
     *
     * @param  string  $value
     * @return string
     */
    public function getTypeTextAttribute($value)
    {
        return str($this->attributes['type']?->value ?? $this->attributes['type'])->headline();
    }

    protected function casts(): array
    {
        return [
            // 'type' => TxnType::class,
            'status' => TxnStatus::class,
            'manual_field_data' => 'json',
        ];
    }

    public function userMining()
    {
        return $this->belongsTo(UserMining::class, 'user_mining_id');
    }

    public function gateway(){
        return $this->belongsTo(Gateway::class, 'method','gateway_code');
    }
}
