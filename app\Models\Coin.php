<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coin extends Model
{
    use HasFactory;

    protected $guarded = [];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'conversion_rate' => 'double',
    ];

    public function miners()
    {
        return $this->hasMany(Miner::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
