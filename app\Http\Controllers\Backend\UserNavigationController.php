<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\UserNavigation;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\DB;

class UserNavigationController extends Controller implements HasMiddleware
{
    public static function middleware()
    {
        return [
            new Middleware('permission:user-navigation-manage'),
        ];
    }

    public function index()
    {
        $navigations = UserNavigation::orderBy('position')->get();

        return view('backend.user_navigations.index', ['navigations' => $navigations]);
    }

    public function update(Request $request)
    {
        try {
            UserNavigation::find($request->id)->update([
                'name' => $request->name,
            ]);

            $status = 'success';
            $message = __('Navigation updated successfully!');
        } catch (\Exception $exception) {
            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }

    public function positionUpdate(Request $request)
    {
        DB::beginTransaction();

        try {
            $ids = $request->except('_token');
            $visible = $request->visible;

            $navigations = new UserNavigation;
            $i = 1;

            foreach ($ids as $id) {
                $navigation = $navigations->find((int) $id);

                $navigation->update([
                    'position' => $i,
                ]);

                $i++;
            }

            DB::commit();

            $status = 'success';
            $message = __('Navigation Position Updated Successfully');
        } catch (\Exception $exception) {
            DB::rollBack();

            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }
}
