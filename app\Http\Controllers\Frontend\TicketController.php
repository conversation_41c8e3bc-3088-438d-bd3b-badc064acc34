<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Ticket;
use App\Models\User;
use App\Traits\ImageUpload;
use App\Traits\NotifyTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class TicketController extends Controller
{
    use ImageUpload;
    use NotifyTrait;

    public function tickets()
    {
        $user = Auth::user();
        $tickets = Ticket::where('user_id', $user->id)->latest()->paginate();

        return view('frontend::user.ticket.index', ['tickets' => $tickets, 'user' => $user]);
    }

    public function create()
    {
        return view('frontend::user.ticket.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back()->withInput();
        }

        $user = User::find(Auth::user()->id);

        $attachments = [];

        if ($request->hasFile('attach')) {
            foreach ($request->file('attach') as $file) {
                $attachments[] = self::imageUploadTrait($file);
            }
        }

        $data = [
            'uuid' => 'SUPT'.rand(100000, 999999),
            'title' => $request->input('title'),
            'message' => nl2br($request->input('message')),
            'attachments' => $attachments,
        ];

        $ticket = $user->tickets()->create($data);

        $shortcodes = [
            '[[title]]' => $data['title'],
            '[[message]]' => $data['message'],
            '[[reply_link]]' => route('admin.ticket.show', $ticket->uuid),
            '[[site_title]]' => setting('site_title', 'global'),
        ];

        $this->sendNotify(setting('support_email', 'global'), 'admin_ticket_reply', 'Admin', $shortcodes, $user->phone, $user->id, route('admin.ticket.show', $ticket->uuid));

        notify()->success(__('Your ticket has been created successfully'));

        return redirect()->route('user.ticket.show', $ticket->uuid);
    }

    public function show($uuid)
    {
        $ticket = Ticket::uuid($uuid);
        $user = Auth::user();

        return view('frontend::user.ticket.show', ['ticket' => $ticket, 'user' => $user]);
    }

    public function reply(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back();
        }

        $user = Auth::user();

        $input = $request->all();

        $attachments = [];

        foreach ($request->file('attach') ?? [] as $file) {
            $attachments[] = self::imageUploadTrait($file);
        }

        $data = [
            'user_id' => $user->id,
            'message' => nl2br($input['message']),
            'attach' => $attachments,
        ];

        $ticket = Ticket::uuid($input['uuid']);

        if ($ticket->isClosed()) {
            $ticket->reopen();
        }

        $ticket->messages()->create($data);

        $shortcodes = [
            '[[title]]' => $ticket->title,
            '[[message]]' => $data['message'],
            '[[reply_link]]' => route('admin.ticket.show', $ticket->uuid),
            '[[site_title]]' => setting('site_title', 'global'),
        ];

        $this->sendNotify(setting('support_email', 'global'), 'admin_ticket_reply', 'Admin', $shortcodes, $user->phone, $user->id, route('admin.ticket.show', $ticket->uuid));

        notify()->success(__('Your Ticket Reply successfully'));

        return redirect()->route('user.ticket.show', $ticket->uuid);
    }

    public function closeNow($uuid)
    {
        Ticket::uuid($uuid)->close();
        notify()->success(__('Ticket closed successfully'));

        return redirect()->route('user.tickets');
    }

    public function reopenNow($uuid)
    {
        Ticket::uuid($uuid)->reopen();
        notify()->success(__('Ticket reopened successfully'));

        return back();
    }
}
