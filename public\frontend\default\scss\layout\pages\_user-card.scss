@use '../../utils' as *;

/*----------------------------------------*/
/*  User card styles 
/*----------------------------------------*/
.user-balance-card {
    padding: 16px 30px;
    position: relative;
    border-radius: 16px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.05) -31.74%, rgba(9, 70, 255, 0.03) 88.89%);
    border: 1px solid rgba(34, 34, 35, 0.16);
    z-index: 2;
    overflow: hidden;

    @include dark-theme {
        background: linear-gradient(135deg, #0f1e44, #0d1a3a);
        border: 1px solid rgba($heading, $alpha: 0.16);
    }

    @include dark-theme {
        border-color: #1c2b56;
    }

    @media #{$xxs} {
        padding: 20px 20px;
    }

    .main-balance {
        margin-bottom: 60px;

        @media #{$xs} {
            margin-bottom: rem(30);
        }

        .balance-value {
            font-weight: 700;
            display: flex;
            align-items: center;
            font-size: 24px;
            gap: 4px;
        }
    }

    .profit-balance {
        .balance-value {
            display: flex;
            align-items: center;
            gap: 4px;
            font-weight: 700;

            h5 {
                font-size: 16px;
            }
        }
    }

    .balance-label {
        font-size: 16px;
        margin-bottom: 4px;
        display: block;

        @include dark-theme {
            color: rgba($white, $alpha: 0.7);
        }
    }

    .icon-btn {
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        color: rgba(154, 157, 167, 1);
        padding: 5px;
        font-size: 20px;
    }

    .level-badge {
        position: absolute;
        top: 16px;
        inset-inline-end: 16px;
        padding: 6px 16px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 30px;
        background: linear-gradient(0deg, rgba(6, 16, 49, 0.20) 0%, rgba(62, 197, 255, 0.20) 100%);
        backdrop-filter: blur(15px);
        display: inline-flex;
        align-items: center;
        gap: 8px;
        z-index: 1;

        @media #{$xxs} {
            position: initial;
            margin-bottom: 15px;
        }

        img {
            width: 18px;
        }

        &::before {
            position: absolute;
            content: "";
            border-radius: 30px;
            inset: 0;
            padding: 2px;
            background: linear-gradient(0deg, rgba(62, 197, 255, 0.05) 0%, rgba(62, 197, 255, 0.5) 100%);
            -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            z-index: -1;
        }
    }

    .card-coin {
        position: absolute;
        inset-inline-end: 0;
        bottom: -60px;
        height: 100%;
        width: 50%;
        background-repeat: no-repeat;
        background-position: bottom right;
        opacity: 80%;
        z-index: -1;
        background-size: contain;
        @media #{$xs,$sm,$md,$lg,$xl} {
            display: none;
        }
    }

    .card-wave {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-size: cover;
        opacity: 10%;
        z-index: -2;

        @include dark-theme {
            opacity: 100%;
        }
    }

    .login-status {
        .login-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .login-time {
            font-size: 12px;
            margin-top: 3px;
            margin-bottom: 0;
            color: rgba($heading, $alpha: 0.6);

            @include dark-theme {
                color: var(--td-text-primary);
            }
        }

        .login-info {
            margin-top: 5px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px 10px;

            .item {
                display: flex;
                align-items: center;
                gap: 2px;

                .icon {
                    display: inline-flex;
                    align-items: center;
                    font-size: 16px;
                    position: relative;
                }

                .text {
                    font-size: 12px;
                    color: rgba($heading, $alpha: 0.6);

                    @include dark-theme {
                        color: var(--td-text-primary);
                    }
                }
            }
        }
    }
}

// referral card
.user-referral-card {
    padding: 30px 30px;
    overflow: hidden;
    position: relative;
    border-radius: 16px;
    border: 1px solid rgba($heading, $alpha: 0.16);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.10) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
    backdrop-filter: blur(10px);

    @include dark-theme {
        border-color: #0B277A;
    }

    @media #{$xs} {
        padding: 20px 20px 20px;
    }

    .referral-contents {
        .title {
            font-size: 20px;
            margin-bottom: 6px;

            @media #{$xs,$lg} {
                font-size: 18px;
            }
        }

        .description {
            font-size: 14px;
        }
    }

    .referral-input {
        margin-top: 25px;
        position: relative;

        .input {
            position: relative;
            margin-bottom: 5px;

            input {
                background: rgba($white, $alpha: 0.4);
                border: 1px solid rgba($heading, $alpha: 0.16);
                border-radius: 0px;
                padding-inline-end: 128px;
                border-radius: 8px;

                @include dark-theme {
                    color: #9A9DA7;
                }

                @include dark-theme {
                    background-color: #141D3B;
                }

                @include dark-theme {
                    border-color: rgba($white, $alpha: 0.1);
                }

                &:focus {
                    border-color: var(--td-primary);
                }
            }

            .td-btn {
                position: absolute;
                inset-inline-end: 5px;
                top: 50%;
                transform: translateY(-50%);
                height: 36px;
                font-size: 14px;
                padding: 0px 16px;
                gap: 6px;
                border-radius: 6px;
            }
        }

        .description {
            font-size: 16px;
            background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
}