<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\LevelReferral;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class UserReferralController extends Controller
{
    public function userReferral()
    {
        $user = Auth::user();

        $referralUsers = User::query()
            ->where('ref_id', $user->id)
            ->paginate(15);

        $totalRefUser = $referralUsers->total();

        $setting = Setting::where('name', 'referral_rules')->first();

        $rules = json_decode($setting->val ?? '') ?? [];

        $totalReferralProfit = $user->totalReferralProfit();

        $title = 'Referral Program';

        return view('frontend::user.referral.index', ['referralUsers' => $referralUsers, 'totalRefUser' => $totalRefUser, 'title' => $title, 'rules' => $rules, 'totalReferralProfit' => $totalReferralProfit, 'currencySymbol' => setting('currency_symbol')]);
    }

    public function userReferralList()
    {
        $user = Auth::user();

        $referralUsers = User::query()
            ->where('ref_id', $user->id)
            ->paginate(15);
        $title = 'Referred List';

        return view('frontend::user.referral.referred_list', ['referrals' => $referralUsers, 'title' => $title]);
    }

    public function referralTree()
    {
        $level = LevelReferral::max('the_order');
        $user = Auth::user();
        $title = 'Referral Tree';

        return view('frontend::user.referral.referral_tree', ['level' => $level, 'user' => $user, 'title' => $title]);
    }
}
