@use "../utils" as *;

/*----------------------------------------*/
/* Section Title  
/*----------------------------------------*/
.section-title-wrapper {
    .highlight {
        background: linear-gradient(90deg, #4776E6 56.79%, #8E54E9 97.46%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .highlight-primary {
        background: linear-gradient(90deg, var(--td-gradient-1) 0%, var(--td-gradient-2) 14.2%, var(--td-gradient-3) 56.81%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .section-title {
        font-size: 48px;

        @media #{$xxl} {
            font-size: 48px;
        }

        @media #{$xl} {
            font-size: 44px;
        }

        @media #{$lg} {
            font-size: 36px;
        }

        @media #{$md} {
            font-size: 34px;
        }

        @media #{$sm} {
            font-size: 32px;
        }

        @media #{$xs} {
            font-size: 28px;
        }

        &.w-break {
            width: 65%;

            @media #{$xs,$sm,$md} {
                width: 100%;
            }
        }
    }

    &.is-white {
        .section-title {
            color: var(--td-white);
        }

        .description {
            color: rgba($white, $alpha: 0.8);
        }
    }
}

.heading-chip-box {
    position: relative;
    width: 100%;
    height: 100%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 50px 50px;
    z-index: 2;
    background-color: rgba($white, $alpha: 0.5);

    @include dark-theme {
        background-color: transparent;
    }

    .clip-border-bg {
        position: absolute;
        top: 0;
        inset-inline-start: 0;
        height: 100%;
        width: 100%;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        z-index: -1;
    }
}