<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Theme;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Validator;
use ZipArchive;

class ThemeController extends Controller
{
    public function siteTheme()
    {
        $themes = Theme::where('type', 'site')->get();

        return view('backend.theme.site', ['themes' => $themes]);
    }

    public function dynamicLanding()
    {
        $landingThemes = Theme::where('type', 'landing')->get();

        return view('backend.theme.dynamic_landing', ['landingThemes' => $landingThemes]);
    }

    public function statusUpdate(Request $request)
    {
        $theme = Theme::find($request->id);

        $status = $theme->type == 'site' ? 1 : $request->status;

        if ($status) {
            $query = Theme::where('type', $theme->type)->where('status', true);
            $oldStatus = $query->pluck('id')->toArray();
            $query->update([
                'status' => 0,
            ]);
        }

        $theme->update([
            'status' => $status,
        ]);

        Cache::forget('landingSections');
        Cache::forget('pages');

        if ($theme->type == 'site') {
            notify()->success(__('Site Theme Status Updated Successfully'));

            return back();
        }

        return response()->json([
            'old_status' => $oldStatus ?? [],
            'message' => __('Landing Theme Status Updated Successfully'),
        ]);
    }

    public function dynamicLandingUpdate(Request $request)
    {
        $input = $request->all();

        $zipThemeFile = $request->theme_file;
        $themeFileName = $zipThemeFile->getClientOriginalName();
        $themeFileName = pathinfo($themeFileName, PATHINFO_FILENAME);

        $input = array_merge($input, [
            'name' => $themeFileName,
        ]);

        $validator = Validator::make($input, [
            'theme_file' => 'required|file|mimes:zip|max:30048',
            'name' => 'required|unique:themes',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back();
        }

        DB::beginTransaction();

        try {
            $zip = new ZipArchive;
            if ($zip->open($zipThemeFile) !== true) {
                // Handle zip file opening failure
                notify()->error(__('Failed to open the zip file'));

                return back();
            }

            $indexHtmlExists = false;
            for ($i = 0; $i < $zip->numFiles; $i++) {
                $filename = $zip->getNameIndex($i);
                if ($filename === 'index.html') {
                    $indexHtmlExists = true;
                    break;
                }
            }

            if (! $indexHtmlExists) {
                // Handle index.html not found
                notify()->error(__('The zip file does not contain index.html'));

                return back();
            }

            $zip->extractTo('./assets/landing_theme/'.$themeFileName);
            $zip->close();
            $themeHtml = file_get_contents(sprintf('./assets/landing_theme/%s/index.html', $themeFileName));
            file_put_contents(sprintf('./resources/views/landing_theme/%s.blade.php', $themeFileName), $themeHtml);
            @unlink(sprintf('assets/landing_theme/%s/index.html', $themeFileName));
            Theme::create([
                'name' => $themeFileName,
                'type' => 'landing',
                'status' => false,
            ]);

            DB::commit();

            $status = 'success';
            $message = __('Landing Theme Uploaded Successfully');
        } catch (\Exception $exception) {
            DB::rollBack();

            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }

    public function dynamicLandingStatusUpdate(Request $request)
    {
        DB::beginTransaction();

        try {
            $id = $request->id;
            $status = $request->status;

            $theme = Theme::find($id);

            if ($status) {
                $query = Theme::where('type', 'landing')->where('status', true);
                $oldStatus = $query->pluck('id')->toArray();
                $query->update([
                    'status' => 0,
                ]);
            }

            $theme->update([
                'status' => $status,
            ]);

            DB::commit();

            $status = 'success';
            $message = __('Landing Theme Status Updated Successfully');
        } catch (\Exception $exception) {
            DB::rollBack();

            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }

    public function dynamicLandingDelete($id)
    {
        DB::beginTransaction();

        try {
            $theme = Theme::find($id);
            File::deleteDirectory('assets/landing_theme/'.$theme->name);
            if (file_exists(resource_path(sprintf('views/landing_theme/%s.blade.php', $theme->name)))) {
                unlink(resource_path(sprintf('views/landing_theme/%s.blade.php', $theme->name)));
            }

            $theme->delete();

            DB::commit();

            $status = 'success';
            $message = __('Landing Theme Deleted Successfully');
        } catch (\Exception $exception) {
            DB::rollBack();

            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }
}
