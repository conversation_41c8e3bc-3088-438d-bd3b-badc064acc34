<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LandingContent extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function scopeCurrentTheme($query)
    {
        return $query->where('theme', site_theme());
    }

    /**
     * Scope a query to only include language
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeLang($query)
    {
        return $query->where('locale', app()->getLocale());
    }
}
