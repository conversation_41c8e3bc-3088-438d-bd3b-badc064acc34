<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\UserWallet;
use App\Models\WithdrawAccount;
use App\Models\WithdrawMethod;
use App\Traits\ImageUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class WithdrawAccountController extends Controller
{
    use ImageUpload;

    public function index(Request $request)
    {
        $user = Auth::user();
        $withdrawAccounts = WithdrawAccount::query()
            ->where('user_id', $user->id)
            ->when($request->keyword, fn ($q) => $q->where('method_name', 'like', '%'.$request->keyword.'%'))
            ->latest()
            ->paginate($request->perPage ?? 10);

        return view('frontend::user.withdraw.account.index', ['withdrawAccounts' => $withdrawAccounts]);
    }

    public function create()
    {
        $user = Auth::user();
        $withdrawMethods = WithdrawMethod::query()
            ->where('status', true)
            ->get();

        $wallets = UserWallet::whereBelongsTo($user)
            ->with('coin')
            ->get();

        return view('frontend::user.withdraw.account.create', ['withdrawMethods' => $withdrawMethods, 'wallets' => $wallets]);
    }

    public function withdrawMethod($id)
    {
        $withdrawMethod = WithdrawMethod::query()
            ->where('status', true)
            ->findOrFail($id);

        $html = view('frontend::user.withdraw.account.method_details', ['withdrawMethod' => $withdrawMethod])->render();

        return response()->json([
            'html' => $html,
        ]);
    }

    public function store(Request $request)
    {
        $user = Auth::user();

        $isMultiWalletEnabled = setting('multiple_currency', 'permission');

        $validator = Validator::make($request->all(), [
            'withdraw_method_id' => 'required|exists:withdraw_methods,id',
            'method_name' => 'required',
            'credentials' => 'required',
            'wallet_id' => Rule::requiredIf($isMultiWalletEnabled),
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back();
        }

        $credentials = $request->credentials;

        foreach ($credentials as $key => $value) {

            if (isset($value['value']) && is_file($value['value'])) {
                $credentials[$key]['value'] = self::imageUploadTrait($value['value']);
            }
        }

        try {

            DB::beginTransaction();

            $withdrawAccounts = new WithdrawAccount;
            $withdrawAccounts->user_id = $user->id;
            $withdrawAccounts->user_wallet_id = $request->wallet_id === 'default' ? 0 : $request->wallet_id;
            $withdrawAccounts->withdraw_method_id = $request->withdraw_method_id;
            $withdrawAccounts->method_name = $request->method_name;
            $withdrawAccounts->credentials = json_encode($credentials);
            $withdrawAccounts->save();

            DB::commit();

            notify()->success(__('Withdraw account created successfully'));

            return redirect()->route('user.withdraw.account.index');
        } catch (\Throwable $throwable) {
            DB::rollback();

            notify()->error(__('Sorry! Something went wrong. Please try again'));

            return redirect()->back();
        }
    }

    public function edit($id)
    {

        $withdrawMethods = WithdrawMethod::query()
            ->where('status', true)
            ->get();

        $withdrawAccounts = WithdrawAccount::query()
            ->where('user_id', Auth::user()->id)
            ->findOrFail(decrypt($id));

        return view('frontend::user.withdraw.account.edit', ['withdrawAccounts' => $withdrawAccounts, 'withdrawMethods' => $withdrawMethods]);
    }

    public function update(Request $request, $id)
    {
        $user = Auth::user();

        if (! setting('kyc_withdraw') && ! $user->kyc) {
            notify()->error(__('Please verify your KYC.'));

            return redirect()->back();
        }

        $validator = Validator::make($request->all(), [
            'method_name' => 'required',
            'credentials' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back();
        }

        $credentials = $request->credentials;

        $withdrawAccounts = WithdrawAccount::query()
            ->where('user_id', $user->id)
            ->findOrFail($id);

        $oldCredentials = json_decode($withdrawAccounts->credentials, true);

        foreach ($credentials as $key => $value) {

            if (! isset($value['value'])) {
                $credentials[$key]['value'] = data_get($oldCredentials[$key], 'value');
            }

            if (isset($value['value']) && is_file($value['value'])) {
                $credentials[$key]['value'] = self::imageUploadTrait($value['value'], data_get($oldCredentials[$key], 'value'));
            }
        }

        try {
            DB::beginTransaction();

            $withdrawAccounts->method_name = $request->method_name;
            $withdrawAccounts->credentials = json_encode($credentials);
            $withdrawAccounts->save();

            DB::commit();

            notify()->success(__('Withdraw account updated successfully'));

            return redirect()->route('user.withdraw.account.index');
        } catch (\Throwable $throwable) {
            DB::rollback();
            notify()->error(__('Sorry! Something went wrong. Please try again'));

            return redirect()->back();
        }
    }

    public function delete($id)
    {

        try {
            DB::beginTransaction();

            $withdrawAccounts = WithdrawAccount::query()
                ->where('user_id', Auth::user()->id)
                ->findOrFail(decrypt($id));

            $oldCredentials = json_decode($withdrawAccounts->credentials, true);

            foreach ($oldCredentials as $value) {
                if (isset($value['value']) && is_file($value['value'])) {
                    self::fileDelete($value['value']);
                }
            }

            $withdrawAccounts->delete();

            DB::commit();

            notify()->success(__('Withdraw account deleted successfully'));

            return redirect()->back();
        } catch (\Throwable $throwable) {
            DB::rollback();
            notify()->error(__('Sorry! Something went wrong. Please try again'));

            return redirect()->back();
        }
    }

    public function getMethods(Request $request)
    {
        $options = collect([
            '<option value="" selected disabled>Select Method</option>',
        ]);

        $currency = $request->get('currency') === 'default' ? setting('site_currency', 'global') : UserWallet::query()->findOrFail($request->get('currency'))?->coin?->code;

        WithdrawMethod::query()
            ->where('status', true)
            ->where('currency', $currency)
            ->each(function ($method) use ($options) {
                $url = route('user.withdraw.account.details', $method->id);
                $options->push(sprintf("<option value='%s' data-routeurl='%s'>%s</option>", $method->id, $url, $method->name));
            });

        return response()->json([
            'options' => $options->toArray(),
        ]);
    }
}
