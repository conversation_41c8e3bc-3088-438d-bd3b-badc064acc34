@use "../utils" as *;

/*----------------------------------------*/
/* FAQ styles
/*----------------------------------------*/
.td-faq-section {
    background-color: #F5F5F5;

    @include dark-theme {
        background-color: var(--td-void);
    }
}

.td-faq-style {
    .accordion {
        .accordion-button {
            padding: 0;
            font-size: rem(18);
            background: transparent;
            box-shadow: none;
            color: var(--td-heading);
            font-weight: 700;
            font-family: var(--td-ff-heading);

            @media #{$xs,$sm,$lg} {
                font-size: rem(16);
            }

            @media #{$xxs} {
                font-size: rem(14);
            }

            &:not(.collapsed) {
                border-radius: 0;

                span {
                    color: var(--td-primary);
                }

                .accordion-body {
                    background: var(--td-white);
                }

                &::after {
                    background-image: url(../images/icons/multiplication.svg);
                }
            }

            &::after {
                position: absolute;
                inset-inline-end: 20px;
                content: "";
                font-family: var(--td-ff-fontawesome);
                font-size: 1.125rem;
                font-weight: 400;
                text-align: center;
                top: 50%;
                transform: translateY(-50%);
                background-image: url(../images/icons/addition.svg);
                filter: invert(1) brightness(1.2);

                @include dark-theme {
                    filter: brightness(0) invert(1);
                }
            }

            span {
                padding-inline-end: rem(10);
                display: inline-block;
                transition: none;
            }
        }

        .accordion-body {
            background: transparent;
            border-radius: rem(0);
            padding: 0px 30px 30px 30px;

            @media #{$xxs} {
                padding-inline-start: rem(20);
                padding-inline-end: rem(20);
            }

            .description {
                color: var(--td-text-primary);

                strong {
                    color: var(--td-heading);
                }
            }

            .accordion-body-list {
                margin-top: rem(14);

                ul {
                    li {
                        &:not(:last-child) {
                            margin-bottom: rem(7);
                        }
                    }
                }
            }
        }

        .accordion-item {

            position: relative;
            padding: 1px;
            display: block;
            background-color: transparent;
            border: 0;

            .clip-path {
                position: relative;
                z-index: 3;
                background: var(--td-white);
                clip-path: polygon(99.536% 0.685%, 99.536% 0.685%, 99.598% 0.73%, 99.658% 0.86%, 99.713% 1.067%, 99.764% 1.346%, 99.809% 1.688%, 99.848% 2.087%, 99.879% 2.536%, 99.903% 3.027%, 99.918% 3.554%, 99.923% 4.11%, 99.923% 80.814%, 99.923% 80.814%, 99.921% 81.115%, 99.917% 81.411%, 99.909% 81.7%, 99.899% 81.982%, 99.887% 82.255%, 99.871% 82.517%, 99.854% 82.766%, 99.833% 83.002%, 99.811% 83.222%, 99.786% 83.425%, 99.768% 83.551%, 97.504% 98.627%, 97.504% 98.627%, 97.483% 98.755%, 97.462% 98.87%, 97.44% 98.973%, 97.417% 99.062%, 97.394% 99.139%, 97.37% 99.202%, 97.346% 99.251%, 97.321% 99.286%, 97.296% 99.308%, 97.271% 99.315%, 0.464% 99.315%, 0.464% 99.315%, 0.402% 99.27%, 0.342% 99.14%, 0.287% 98.933%, 0.236% 98.654%, 0.191% 98.312%, 0.152% 97.913%, 0.121% 97.464%, 0.097% 96.973%, 0.082% 96.446%, 0.077% 95.89%, 0.077% 4.11%, 0.077% 4.11%, 0.082% 3.554%, 0.097% 3.027%, 0.121% 2.536%, 0.152% 2.087%, 0.191% 1.688%, 0.236% 1.346%, 0.287% 1.067%, 0.342% 0.86%, 0.402% 0.73%, 0.464% 0.685%, 99.536% 0.685%);
                width: 100%;

                @include dark-theme {
                    background: #04060a;
                }

                &::before {
                    position: absolute;
                    top: 0;
                    inset-inline-start: 0;
                    content: "";
                    background: linear-gradient(90deg, rgba(71, 118, 230, 0.10) 68.94%, rgba(142, 84, 233, 0.10) 100%);
                    z-index: -1;
                    width: 100%;
                    height: 100%;

                    @include dark-theme {
                        background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
                    }
                }
            }

            &::before {
                position: absolute;
                inset-inline-start: 0;
                top: 0;
                transition: all .3s;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);
                opacity: 0.4;
                content: "";
                clip-path: polygon(99.536% 0.685%, 99.536% 0.685%, 99.598% 0.73%, 99.658% 0.86%, 99.713% 1.067%, 99.764% 1.346%, 99.809% 1.688%, 99.848% 2.087%, 99.879% 2.536%, 99.903% 3.027%, 99.918% 3.554%, 99.923% 4.11%, 99.923% 80.814%, 99.923% 80.814%, 99.921% 81.115%, 99.917% 81.411%, 99.909% 81.7%, 99.899% 81.982%, 99.887% 82.255%, 99.871% 82.517%, 99.854% 82.766%, 99.833% 83.002%, 99.811% 83.222%, 99.786% 83.425%, 99.768% 83.551%, 97.504% 98.627%, 97.504% 98.627%, 97.483% 98.755%, 97.462% 98.87%, 97.44% 98.973%, 97.417% 99.062%, 97.394% 99.139%, 97.37% 99.202%, 97.346% 99.251%, 97.321% 99.286%, 97.296% 99.308%, 97.271% 99.315%, 0.464% 99.315%, 0.464% 99.315%, 0.402% 99.27%, 0.342% 99.14%, 0.287% 98.933%, 0.236% 98.654%, 0.191% 98.312%, 0.152% 97.913%, 0.121% 97.464%, 0.097% 96.973%, 0.082% 96.446%, 0.077% 95.89%, 0.077% 4.11%, 0.077% 4.11%, 0.082% 3.554%, 0.097% 3.027%, 0.121% 2.536%, 0.152% 2.087%, 0.191% 1.688%, 0.236% 1.346%, 0.287% 1.067%, 0.342% 0.86%, 0.402% 0.73%, 0.464% 0.685%, 99.536% 0.685%);
                border-radius: 2px;
            }

            &:not(:last-of-type) {
                margin-bottom: rem(20);
            }

            &:not(:first-of-type) {
                border-top: 0;
            }


            &:last-of-type {
                &>.accordion-collapse {
                    border-bottom-right-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }

            &:first-of-type {
                .accordion-button {
                    border-top-left-radius: 0;
                    border-top-right-radius: 0;
                }
            }

            &.accordion-active {
                box-shadow: none;
                border: 0;

                .accordion-button {
                    color: var(--td-heading);
                }

                &:not(:first-of-type) {
                    border-top: var(--bs-accordion-border-width) solid rgba($white, $alpha: 0.16);
                }

                .faq-shape-bg {
                    opacity: 1;
                }
            }
        }

        .accordion-header {
            button {
                padding: rem(20) rem(40) rem(20) rem(20);
                line-height: 1.5;

                @media #{$xs} {
                    padding: rem(18) rem(35) rem(18) rem(18);
                }
            }
        }
    }
}

.faq-thumb {
    max-width: 532px;
    margin-inline-start: auto;
}

.faq-shapes {
    .shape-one {
        position: absolute;
        top: 0;
        inset-inline-end: 0;
        z-index: -1;

        img {
            @media #{$md,$lg,$xl} {
                width: 200px;
            }
        }
    }
}

.td-faq-section-two {
    background: #F5F5F5;
}