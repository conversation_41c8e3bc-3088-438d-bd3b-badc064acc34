name: Static analysis (Psalm)

on: [push, pull_request]

jobs:
  psalm:
    name: Psalm
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php-versions: ['8.0']
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: '0'

      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          tools: composer:v2

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache composer dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Validate composer.json and composer.lock
        run: composer validate

      - name: Install Composer dependencies
        run: composer install --no-progress --optimize-autoloader

      - name: Run psalm
        run: composer psalm
