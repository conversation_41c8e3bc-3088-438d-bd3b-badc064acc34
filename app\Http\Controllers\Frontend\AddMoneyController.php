<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Facades\Txn\Txn;
use App\Http\Controllers\Controller;
use App\Models\DepositMethod;
use App\Models\Transaction;
use App\Models\UserWallet;
use App\Traits\ImageUpload;
use App\Traits\NotifyTrait;
use App\Traits\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AddMoneyController extends Controller
{
    use ImageUpload;
    use NotifyTrait;
    use Payment;

    public function addMoneyHistory(Request $request)
    {
        $user = Auth::user();
        $transactions = Transaction::with('wallet.coin')
            ->where('user_id', $user->id)
            ->when($request->filled('txn'), function ($query) use ($request) {
                $query->where('tnx', 'like', '%'.$request->input('txn').'%');
            })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->input('status'));
            })
            ->when($request->filled('wallet'), function ($query) use ($request) {
                $query->whereRelation('wallet', 'coin_id', $request->input('wallet'));
            })
            ->when($request->filled('date'), function ($query) use ($request) {
                if (str($request->input('date'))->contains('to')) {
                    $dates = explode(' to ', $request->input('date'));
                    $dates = array_map(function ($date) {
                        return date('Y-m-d', strtotime($date));
                    }, $dates);
                    $query->whereBetween(DB::raw('DATE(created_at)'), $dates);
                } else {
                    $query->whereDate('created_at', $request->input('date'));
                }
            })
            ->whereIn('type', [TxnType::Deposit, TxnType::ManualDeposit])
            ->latest()
            ->paginate()
            ->withQueryString();

        return view('frontend::user.add_money.history', ['transactions' => $transactions]);
    }

    public function gatewayInfo(Request $request, $id)
    {
        $gatewaysInfo = DepositMethod::query()
            ->where('status', true)
            ->findOrFail($id);

        $html = view('frontend::user.add_money.review_details', ['gatewaysInfo' => $gatewaysInfo])->render();
        if ($request->filled('amount')) {
            [$payAmount, $charge, $finalAmount] = $gatewaysInfo->getChargeAmount($request->amount ?? 0);
            return response()->json([
                'payAmount' => $payAmount,
                'charge' => $charge,
                'finalAmount' => $finalAmount,
                'html' => $html,
                'gateway' => $gatewaysInfo,
            ]);
        }

        return response()->json($html);
    }

    public function addMoney()
    {

        $user = Auth::user();

        if (! setting('user_deposit', 'permission') || ! $user->deposit_status) {
            notify()->error(__('Deposit currently unavailable'));

            return to_route('user.dashboard');
        } elseif (! setting('kyc_deposit') && ! $user->kyc) {
            notify()->error(__('Please verify your KYC.'));

            return to_route('user.dashboard');
        }

        $gateways = DepositMethod::query()
            ->where('status', 1)
            ->get();

        $wallets = [];

        $wallets = UserWallet::query()
            ->with([
                'coin',
            ])
            ->whereBelongsTo($user)
            ->get();

        return view('frontend::user.add_money.add_money', ['gateways' => $gateways, 'wallets' => $wallets]);
    }

    public function addMoneyNow(Request $request)
    {
        $user = Auth::user();

        if (! setting('user_deposit', 'permission') || ! $user->deposit_status) {
            notify()->error(__('Deposit currently unavailable!'));

            return redirect()->back();
        } elseif (! setting('kyc_deposit') && ! $user->kyc) {
            notify()->error(__('Please verify your KYC.'));

            return redirect()->back();
        }

        $validator = Validator::make($request->all(), [
            'payment_gateway' => 'required',
            'amount' => 'required',
            'user_wallet' => setting('multiple_currency') ? 'required' : 'nullable',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back()->withErrors($validator)->withInput();
        }

        $gatewayInfo = DepositMethod::findOrFail($request->payment_gateway);

        if (! $gatewayInfo) {
            notify()->error(__('Gateway does not exist!'));

            return redirect()->back();
        }

        $amount = $request->amount;
        $wallet = UserWallet::find($request->user_wallet);

        if ($amount < $gatewayInfo->minimum_deposit || $amount > $gatewayInfo->maximum_deposit) {
            $currencySymbol = setting('currency_symbol', 'global');
            $message = __('Please Deposit the Amount within the range :symbol:min to :symbol:max', [
                'symbol' => data_get($wallet, 'currency.symbol', $currencySymbol),
                'min' => $gatewayInfo->minimum_deposit,
                'max' => $gatewayInfo->maximum_deposit,
            ]);

            notify()->error($message);

            return redirect()->back();
        }

        try {

            [$payAmount, $charge, $finalAmount] = $gatewayInfo->getChargeAmount($amount);
            $depositType = TxnType::Deposit;

            if ($request->manual_data !== null) {
                $depositType = TxnType::ManualDeposit;
                $manualData = $request->manual_data;

                foreach ($manualData as $key => $value) {
                    if (is_file($value)) {
                        $manualData[$key] = self::imageUploadTrait($value);
                    }
                }

                $shortcodes = [
                    '[[amount]]' => $request->amount,
                    '[[charge]]' => $charge,
                    '[[wallet]]' => data_get($wallet, 'coin.name', 'Default'),
                    '[[currency]]' => data_get($wallet, 'coin.code', setting('site_currency', 'global')),
                    '[[gateway]]' => $gatewayInfo->name,
                    '[[request_at]]' => date('d M, Y h:i A'),
                    '[[total_amount]]' => $finalAmount,
                    '[[request_link]]' => route('admin.deposit.manual.pending'),
                    '[[site_title]]' => setting('site_title', 'global'),
                ];

                $this->sendNotify($user->email, 'admin_manual_deposit', 'Admin', $shortcodes, $user->phone, $user->id, route('admin.deposit.manual.pending'));
            }

            DB::beginTransaction();

            $userWallet = data_get($wallet, 'id', 'default');

            $txnInfo = (new Txn)->new($request->amount, $charge, $finalAmount, $userWallet, $gatewayInfo->gateway_code, 'Deposit With '.$gatewayInfo->name, $depositType, TxnStatus::Pending, $gatewayInfo->currency, $payAmount, $user->id, null, 'User', $manualData ?? []);

            DB::commit();

            return self::depositAutoGateway($gatewayInfo->gateway_code, $txnInfo);
        } catch (\Throwable $throwable) {
            DB::rollBack();

            throw $throwable;
            notify()->error(__('Sorry! Something went wrong. Please try again.'));

            return redirect()->back();
        }
    }
}
