<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Kyc;
use App\Models\UserKyc;
use App\Traits\ImageUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use PragmaRX\Google2FALaravel\Support\Authenticator;

class SettingController extends Controller
{
    use ImageUpload;

    public function index(Request $request, $type = 'personal')
    {
        $data = match ($type) {
            'personal' => $this->fetchPersonalInfo(),
            'password' => [],
            'security' => [],
            'kyc' => $this->fetchKycInfo(),
            default => $this->fetchPersonalInfo(),
        };

        $user = Auth::user();

        return view('frontend::user.settings.index', ['data' => $data, 'user' => $user]);
    }

    public function update(Request $request)
    {
        $type = $request->type ?? 'personal';
        $user = Auth::user();

        match ($type) {
            'personal' => $data = $this->updatePersonalInfo($request, $user),
            'password' => $data = $this->updatePasswordInfo($request, $user),
            'kyc' => $data = $this->fetchKycInfo(),
            default => $data = $this->updatePersonalInfo($request, $user),
        };

        notify()->{$data['status']}($data['message']);

        return back();
    }

    public function generateQrcode(Request $request)
    {
        $user = Auth::user();

        $google2fa = app('pragmarx.google2fa');

        $google2fa_secret = $google2fa->generateSecretKey();

        $user->update([
            'google2fa_secret' => $google2fa_secret,
        ]);

        return back();
    }

    public function twofaAction(Request $request)
    {
        $user = Auth::user();
        if ($request->status == 'disable') {

            if (Hash::check(request('one_time_password'), $user->password)) {
                $user->update([
                    'two_fa' => 0,
                ]);

                notify()->success(__('2FA disabled successfully'));

                return redirect()->back();
            }

            notify()->warning(__('Your password is wrong!'));

            return redirect()->back();
        } elseif ($request->status == 'enable') {
            session([
                config('google2fa.session_var') => [
                    'auth_passed' => false,
                ],
            ]);

            $authenticator = app(Authenticator::class)->boot($request);
            if ($authenticator->isAuthenticated()) {

                $user->update([
                    'two_fa' => 1,
                ]);

                notify()->success(__('2FA enabled successfully'));

                return redirect()->back();
            }

            notify()->warning(__('One time key is wrong!'));

            return redirect()->back();
        }
    }

    protected function updatePersonalInfo($request, $user)
    {

        $request->validate([
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'country' => 'required|string',
            'phone' => 'required|string',
        ]);

        $user->update([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'gender' => $request->gender,
            'date_of_birth' => $request->date_of_birth,
            'country' => $request->country,
            'phone' => $request->phone,
            'city' => $request->city,
            'zip_code' => $request->zip,
            'address' => $request->address,
            'avatar' => $request->hasFile('avatar') ? self::imageUploadTrait($request->file('avatar'), $user->avatar) : $user->avatar,
        ]);

        return [
            'status' => 'success',
            'message' => __('Personal information updated successfully'),
        ];
    }

    protected function updatePasswordInfo($request, $user)
    {
        $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
            'new_password_confirmation' => 'required|string',
        ]);

        if (! Hash::check($request->current_password, $user->password)) {
            return [
                'status' => 'error',
                'message' => __('Current password is incorrect'),
            ];
        }

        $user->update([
            'password' => bcrypt($request->new_password),
        ]);

        return [
            'status' => 'success',
            'message' => __('Password updated successfully'),
        ];
    }

    protected function fetchPersonalInfo()
    {
        return [
            'user' => Auth::user(),
        ];
    }

    protected function fetchKycInfo()
    {
        $user = Auth::user();

        $user_kycs = UserKyc::with('kyc')->where('user_id', $user->id)->latest()->get();

        $userKycIds = UserKyc::whereIn('status', ['pending', 'approved'])->where('user_id', $user->id)->where('is_valid', true)->pluck('kyc_id');

        $kycs = Kyc::where('status', true)->whereNotIn('id', $userKycIds)->get();

        return [
            'kycs' => $kycs,
            'user_kycs' => $user_kycs,
        ];
    }
}
