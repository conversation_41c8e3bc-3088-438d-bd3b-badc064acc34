<?php

namespace App\Http\Controllers;

use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Enums\UserMiningStatus;
use App\Facades\Txn\Txn;
use App\Models\CronJob;
use App\Models\CronJobLog;
use App\Models\Portfolio;
use App\Models\Transaction;
use App\Models\User;
use App\Traits\NotifyTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Remotelywork\Installer\Repository\App;

class CronJobController extends Controller
{
    use NotifyTrait;

    public function runCronJobs()
    {

        $action_id = request('run_action');

        // Get running cron jobs
        if (is_null($action_id)) {
            $jobs = CronJob::where('status', 'running')
                ->where('next_run_at', '<', now())
                ->get();
        } else {
            $jobs = CronJob::whereKey($action_id)->get();
        }

        foreach ($jobs as $job) {

            $error = null;

            $log = new CronJobLog;
            $log->cron_job_id = $job->id;
            $log->started_at = now();

            try {

                if ($job->type == 'system') {
                    $this->{$job->reserved_method}();
                } else {
                    Http::withOptions([
                        'verify' => false,
                    ])->get($job->url);
                }
            } catch (\Throwable $th) {
                $error = $th->getMessage();
            }

            $log->ended_at = now();
            $log->error = $error;
            $log->save();

            $job->update([
                'last_run_at' => now(),
                'next_run_at' => now()->addSeconds($job->schedule),
            ]);
        }

        if ($action_id !== null) {
            notify()->success(__('Cron running successfully!'), 'Success');

            return back();
        }
    }

    public function userInactive()
    {
        if (! setting('inactive_account_disabled', 'inactive_user') == 1) {
            return false;
        }

        try {

            DB::beginTransaction();
            $this->startCron();

            User::whereDoesntHave('activities', function ($query) {
                $query->where('created_at', '>', now()->subDays(30));
            })->where('status', 1)->chunk(500, function ($inactiveUsers) {
                foreach ($inactiveUsers as $user) {
                    $user->update(['status' => 0]);
                }
            });

            DB::commit();

            return '........Inactive users disabled successfully.';
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    // using mining return

    public function userMiningReturn()
    {
        try {
            DB::beginTransaction();
            $this->startCron();

            User::where('status', true)->chunk(500, function ($users) {
                foreach ($users as $user) {
                    $userMining = $user->minings()->where('status', UserMiningStatus::Ongoing)->get();

                    if ($userMining->count() > 0) {
                        foreach ($userMining as $mining) {

                            $schema = $mining->scheme;
                            if (! $schema) {
                                continue;
                            }
                            $miningReturnOffDays = is_array($schema->holidays) ? $schema->holidays : [];

                            $date = now();
                            $today = $date->format('l');

                            $todayDate = $date->format('Y-m-d');

                            if ($miningReturnOffDays == null || (! in_array($today, $miningReturnOffDays) && ! in_array($todayDate, $miningReturnOffDays))) {
                                if (! $mining->next_mining_time || now()->parse($mining->next_mining_time)->isPast()) {
                                    $miningReturn = 0;
                                    if ($schema->return_amount_type == 'fixed') {
                                        $miningReturn = $schema->return_amount_value;
                                    } else {
                                        $miningReturn = rand($schema->return_min_amount, $schema->return_max_amount);
                                    }

                                    $incrementedAmount = $miningReturn;

                                    Transaction::create([
                                        'user_id' => $user->id,
                                        'description' => 'Mining Return of #'.$mining->transaction->tnx,
                                        'type' => TxnType::MiningReturn,
                                        'amount' => $miningReturn,
                                        'wallet_type' => $user->wallets()->where('coin_id', $schema->miner->coin_id)->first()->id,
                                        'charge' => 0,
                                        'final_amount' => $miningReturn,
                                        'method' => 'System',
                                        'status' => TxnStatus::Success,
                                        'user_mining_id' => $mining->id,
                                    ]);

                                    $mining->update([
                                        'total_mined_amount' => $mining->total_mined_amount + $miningReturn,
                                        'last_mining_time' => now(),
                                        'next_mining_time' => now()->addHours($schema->return_period_hours),
                                        'mining_count' => $mining->mining_count + 1,
                                    ]);
                                    if ($mining->user->portfolio) {
                                        $portfolioFeatures = $mining->user->portfolio->features;
                                        if ($portfolioFeatures->boost_amount_return_amount > 0) {

                                            if ($portfolioFeatures->boost_return_amount_type == 'percentage') {
                                                $boostedReturnAmount = ($miningReturn * $portfolioFeatures->boost_amount_return_amount / 100);
                                            } else {
                                                $boostedReturnAmount = $portfolioFeatures->boost_amount_return_amount;
                                            }
                                            Transaction::create([
                                                'user_id' => $user->id,
                                                'description' => 'Mining Return Boost of #'.$mining->transaction->tnx,
                                                'type' => TxnType::BoostedMiningReturn,
                                                'amount' => $boostedReturnAmount,
                                                'wallet_type' => $user->wallets()->where('coin_id', $schema->miner->coin_id)->first()->id,
                                                'charge' => 0,
                                                'final_amount' => $boostedReturnAmount,
                                                'method' => 'System',
                                                'status' => TxnStatus::Success,
                                                'user_mining_id' => $mining->id,
                                            ]);

                                            $incrementedAmount += $boostedReturnAmount;
                                        }
                                    }

                                    $user->wallets()->where('coin_id', $schema->miner->coin_id)->increment('balance', $incrementedAmount);

                                    if ($schema->max_mining_amount && $schema->max_mining_amount > 0 && $mining->total_mined_amount >= $schema->max_mining_amount) {
                                        $mining->update([
                                            'status' => UserMiningStatus::Completed,
                                        ]);
                                    }
                                    if ($mining->return_type == 'period' && $schema->return_period_max_number && $mining->mining_count > 0 && $mining->mining_count >= $schema->return_period_max_number) {
                                        $mining->update([
                                            'status' => UserMiningStatus::Completed,
                                        ]);
                                    }

                                    // maintenance fee
                                    if ($schema->maintenance_fee_amount > 0) {
                                        $maintenanceFee = 0;
                                        if ($schema->maintenance_fee_type == 'percentage') {
                                            $maintenanceFee = ($miningReturn * $schema->maintenance_fee_amount / 100);
                                        } else {
                                            $maintenanceFee = $schema->maintenance_fee_amount;
                                        }

                                        Transaction::create([
                                            'user_id' => $user->id,
                                            'description' => 'Maintenance Fee of #'.$mining->transaction->tnx,
                                            'type' => TxnType::MaintenanceFee,
                                            'amount' => $maintenanceFee,
                                            'wallet_type' => $user->wallets()->where('coin_id', $schema->miner->coin_id)->first()->id,
                                            'charge' => 0,
                                            'final_amount' => $maintenanceFee,
                                            'method' => 'System',
                                            'status' => TxnStatus::Success,
                                            'user_mining_id' => $mining->id,
                                        ]);

                                        // check portfolio
                                        if ($user->portfolio) {
                                            $portfolio = $user->portfolio;
                                            if ($portfolio->discount_amount_maintenance > 0) {

                                                if ($portfolio->discount_maintenance_type == 'percentage') {
                                                    $boostedMaintenanceFee = ($maintenanceFee * $portfolio->discount_amount_maintenance / 100);
                                                } else {
                                                    $boostedMaintenanceFee = $portfolio->discount_amount_maintenance;
                                                }

                                                Transaction::create([
                                                    'user_id' => $user->id,
                                                    'description' => 'Maintenance Fee Discount of #'.$mining->transaction->tnx,
                                                    'type' => TxnType::DiscountMaintenanceFee,
                                                    'amount' => $boostedMaintenanceFee,
                                                    'wallet_type' => $user->wallets()->where('coin_id', $schema->miner->coin_id)->first()->id,
                                                    'charge' => 0,
                                                    'final_amount' => $boostedMaintenanceFee,
                                                    'method' => 'System',
                                                    'status' => TxnStatus::Success,
                                                    'user_mining_id' => $mining->id,
                                                ]);

                                                $maintenanceFee -= $boostedMaintenanceFee;
                                            }
                                        }

                                        $user->wallets()->where('coin_id', $schema->miner->coin_id)->decrement('balance', $maintenanceFee);
                                    }
                                }
                            }

                        }
                    }
                }
            });

            DB::commit();

            return '........User mining return job completed successfully.';
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function userPortfolio()
    {

        try {

            DB::beginTransaction();
            $this->startCron();

            // Get all active portfolios
            $portfolios = Portfolio::where('status', true)->get();

            // Run portfolio processing
            User::where('status', true)->chunk(500, function ($users) use ($portfolios) {
                foreach ($users as $user) {

                    $totalTransactions = $user->transaction->where('status', TxnStatus::Success)->sum('amount');
                    // Get eligible portfolio
                    $eligiblePortfolios = $portfolios->reject(function ($rank) use ($user, $totalTransactions) {

                        return is_array(json_decode($user->portfolios)) &&
                            in_array($rank->id, json_decode($user->portfolios)) ||
                            $rank->minimum_transactions > $totalTransactions;
                    });

                    if ($eligiblePortfolios !== null) {

                        // Get eligible portfolios minimum transactions amount
                        $maxPortfolioTransctionsAmount = $eligiblePortfolios->max('minimum_transactions');

                        // Get highest portfolio by max transactions amount
                        $highestPortfolio = $eligiblePortfolios->where('minimum_transactions', $maxPortfolioTransctionsAmount)->first();
                        // Get none portfolio
                        $nonePortfolio = $eligiblePortfolios->where('minimum_transactions', 0)->first();

                        // Distribute portfolio badge and bonus to users
                        foreach ($eligiblePortfolios as $portfolio) {

                            if ($portfolio->bonus > 0) {
                                $user->balance += $portfolio->bonus;
                                $user->save();
                                app(Txn::class)->new($portfolio->bonus, 0, $portfolio->bonus, 'default', 'System', "'".$portfolio->portfolio_name."' Portfolio Bonus", TxnType::PortfolioBonus, TxnStatus::Success, null, null, $user->id);
                            }

                            // Shortcodes
                            $shortcodes = [
                                '[[portfolio_name]]' => $portfolio->portfolio_name,
                                '[[full_name]]' => $user->full_name,
                            ];

                            $userPortfolios = $user->portfolios != null ? array_merge(json_decode($user->portfolios), [$portfolio->id]) : [$portfolio->id];

                            if ($portfolio->id === $highestPortfolio->id) {

                                if ($nonePortfolio != null && ! in_array($nonePortfolio->id, $userPortfolios)) {
                                    $userPortfolios = array_merge($userPortfolios, [$nonePortfolio->id]);
                                }

                                $user->update([
                                    'portfolio_id' => $portfolio->id,
                                    'portfolios' => json_encode($userPortfolios),
                                ]);

                                // $this->mailNotify($user->email, 'portfolio_achieve', $shortcodes);
                                // $this->pushNotify('portfolio_achieve', $shortcodes, route('user.portfolio'), $user->id);
                            }
                        }
                    }
                }
            });

            DB::commit();

            return '......User portfolio job completed successfully!';
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    protected function startCron()
    {
        if (! App::initApp()) {
            return false;
        }
    }
}
