@use '../../utils' as *;

/*----------------------------------------*/
/* Dashboard styles 
/*----------------------------------------*/

// Dashboard header styles
.app-page-header {
    max-width: 100vw;
    position: fixed;
    top: 0;
    z-index: 33;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    margin-inline-start: 290px;
    width: calc(100% - 290px);
    background-color: var(--td-void);

    @media #{$xl,$xxl} {
        margin-inline-start: 260px;
        width: calc(100% - 260px);
    }

    @media #{$xs} {
        margin-inline-start: 0;
        width: 100%;
    }

    &.close_icon {
        margin-inline-start: 80px;
        width: calc(100% - 80px);
    }

    &.dashboard-sticky {
        position: fixed;
        animation: sticky 0.3s;
        -webkit-animation: sticky 0.3s;
        top: 0;
        width: -webkit-fill-available;
        background: #131314;
    }
}

.app-dashboard-header {
    @include flexbox();
    justify-content: space-between;
    align-items: center;
    gap: 16px 30px;
    padding: 6px 20px;
    height: 70px;
    border-bottom: 1px solid rgba($heading, $alpha: 0.16);
    background-color: var(--td-white);

    @include dark-theme {
        background-color: transparent;
        border-color: rgba($white, $alpha: 0.1);
    }

    @media #{$xs} {
        padding: 15px 15px;
        gap: 16px 16px;
    }

    .left-contents {
        .user-welcome-info {
            display: flex;
            align-items: center;
            gap: 12px;

            .welcome-text {
                font-size: 18px;
                font-family: var(--td-ff-body);

                @media #{$xs} {
                    display: none;
                }
            }
        }
    }

    .right-contents {
        .header-quick-actions {
            display: flex;
            align-items: center;
            gap: 10px;

            .language-nav {
                .translate_wrapper.active {
                    .more_lang {
                        inset-inline-start: auto;
                        inset-inline-end: 0;
                        top: 35px;
                    }
                }
            }

            .lang {
                &>span {
                    &.lang-txt {
                        font-size: 14px;
                        font-weight: 500;
                    }
                }
            }

        }

        .header-btns-wrap {
            position: relative;
            padding-inline-end: 16px;
            margin-inline-end: 16px;

            &::before {
                position: absolute;
                content: "";
                inset-inline-end: 0;
                top: -14px;
                height: 70px;
                width: 1px;
                background-color: rgba($white, $alpha: 0.1);
            }
        }

        .others-actions {
            display: flex;
            gap: 12px;
        }
    }
}

// Dashboard calculation styles
.page-wrapper {
    &.compact-wrapper {
        .app-page-header {
            @media #{$xs,$sm,$md,$lg} {
                margin-inline-start: 0;
                width: 100%;
            }
        }

        .app-page-body-wrapper {
            div {
                &.app-sidebar-wrapper {
                    &.close_icon {
                        &~.app-page-body {
                            margin-inline-start: 80px;
                            -webkit-transition: 0.3s;
                            transition: 0.3s;
                        }
                    }
                }
            }
        }
    }
}

.app-page-body {
    min-height: calc(100vh - 70px);
    margin-top: 70px;
    margin-inline-start: 290px;
    padding: 20px 20px 20px;
    position: relative;
    -webkit-transition: 0.3s;
    transition: 0.3s;

    @media #{$xl,$xxl} {
        margin-inline-start: 260px;
    }

    @media #{$xs} {
        min-height: calc(100vh - 70px);
        margin-top: 70px;
    }
}

.app-page-body-wrapper {
    .app-page-body {
        @media #{$xs,$sm,$md,$lg} {
            margin-inline-start: 0 !important;
        }
    }
}

.bg-overlay {
    &.active {
        height: 100vh;
        width: 100vw;
        background-color: rgba(0, 0, 0, .2);
        position: fixed;
        z-index: 8;
        top: 0;
    }
}