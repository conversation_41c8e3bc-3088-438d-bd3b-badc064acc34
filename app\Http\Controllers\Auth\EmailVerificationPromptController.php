<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class EmailVerificationPromptController extends Controller
{
    public function __invoke(Request $request)
    {
        if (! setting('email_verification', 'permission') || $request->user()->hasVerifiedEmail()) {
            return redirect()->intended(route('user.dashboard', absolute: false));
        }

        if ($request->session()->get('new_user')) {
            $request->session()->forget('new_user');
            try {
                $request->user()->sendEmailVerificationNotification();
            } catch (\Exception $exception) {
                notify()->error(__('Failed to send verification email. Please try again.'));
            }
        }

        return view('frontend::auth.verify-email');
    }
}
