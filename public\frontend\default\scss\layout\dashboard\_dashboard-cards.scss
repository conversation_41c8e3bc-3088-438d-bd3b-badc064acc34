@use '../../utils' as *;

/*----------------------------------------*/
/* Dashboard default card styles
/*----------------------------------------*/
.default-card {
    background: #091628;
    border-radius: 24px;

    .card-heading {
        padding: 18px 30px 18px;
        border-bottom: 1px solid rgba($white, $alpha: 0.1);

        @media #{$xs} {
            padding: 18px 20px 18px;
        }
    }

    .card-inner {
        padding: 30px 30px 30px;

        @media #{$xs} {
            padding: 20px 20px 20px;
        }
    }
}

.card-heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;

    .title {
        font-size: 20px;

        @media #{$xs,$lg,$xl} {
            font-size: 18px;
        }

        @media #{$xxs} {
            font-size: 16px;
        }
    }

    .link {
        font-weight: 600;
        color: var(--td-white);
    }
}

// verify status
.verify-status {
    border-radius: 100px;
    border: 1px solid rgba($white, $alpha: 0.1);
    background: var(--td-gradient-2);
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    gap: 8px;

    .icon {
        flex: 0 0 auto;
        color: var(--td-white);
        font-size: 20px;
        display: flex;
        align-items: center;
        justify-self: center;
    }

    .contents {
        p {
            font-size: 16px;
            font-weight: 500;
            color: var(--td-white);
        }
    }
}