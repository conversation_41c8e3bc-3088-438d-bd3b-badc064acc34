<?php

namespace Razorpay\Tests;

class PlanTest extends TestCase
{
    /**
     * Specify unique plan id
     * for example plan_IEeswu4zFBRGwi
     */
    private $planId = 'plan_IEeswu4zFBRGwi';

    protected function setUp(): void
    {
        parent::setUp();
    }

    /**
     * Create Plan
     */
    public function test_create_plan()
    {
        $data = $this->api->plan->create(['period' => 'weekly', 'interval' => 1, 'item' => ['name' => 'Test Weekly 1 plan', 'description' => 'Description for the weekly 1 plan', 'amount' => 600, 'currency' => 'INR'], 'notes' => ['key1' => 'value3', 'key2' => 'value2']]);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue(in_array('plan', $data->toArray()));
    }

    /**
     * Fetch all plans
     *
     * @covers \Razorpay\Api\Collection::count
     */
    public function test_fetch_all_plans()
    {
        $data = $this->api->plan->all();

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue($data->count() >= 0);

        $this->assertTrue(is_array($data['items']));
    }

    /**
     * Fetch particular plan
     */
    public function test_fetch_plan()
    {
        $data = $this->api->plan->fetch($this->planId);

        $this->assertTrue(is_array($data->toArray()));

        $this->assertTrue(in_array('plan', $data->toArray()));
    }
}
