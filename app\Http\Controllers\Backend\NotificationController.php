<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\SetTune;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class NotificationController extends Controller implements HasMiddleware
{
    public static function middleware()
    {
        return [
            new Middleware('permission:push-notification-template', ['only' => ['template', 'editTemplate', 'updateTemplate']]),
        ];
    }

    public function latestNotification()
    {
        return true;
    }

    public function setTune()
    {
        $set_tunes = SetTune::all();

        return view('backend.setting.notification_tune.index', ['set_tunes' => $set_tunes]);
    }

    public function all()
    {
        $notifications = Notification::where('for', 'admin')->latest()->paginate(10);

        return view('backend.notification.index', ['notifications' => $notifications]);
    }

    public function status($id)
    {
        try {
            $set_tune = SetTune::find($id);

            if ($set_tune->status == 0) {
                $set_tune->status = 1;
                $set_tune->save();

                SetTune::whereNot('id', $id)->update(['status' => false]);

                notify()->success(__('Settings has been saved'));

                return back();
            }

            $set_tune->status = 0;
            $set_tune->save();

            SetTune::where('id', SetTune::first()->id)->update(['status' => true]);

            $status = 'success';
            $message = __('Settings has been saved');
        } catch (\Exception $exception) {
            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();
        }

        notify()->$status($message, $status);

        return back();
    }

    public function readNotification($id)
    {
        if ($id == 0) {
            Notification::where('for', 'admin')->update(['read' => 1]);

            return back();
        }

        $notification = Notification::find($id);
        if ($notification->read == 0) {
            $notification->read = 1;
            $notification->save();
        }

        return redirect()->to($notification->action_url);
    }
}
