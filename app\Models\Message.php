<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\User;

class Message extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected function casts(): array
    {
        return [
            'attach' => 'json',
        ];
    }

    public function user(): BelongsTo
    {
        $model = $this->model == 'admin' ? Admin::class : User::class;

        return $this->belongsTo($model)->withDefault();
    }
}
