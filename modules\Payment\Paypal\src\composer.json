{"name": "srmklive/paypal", "type": "library", "description": "Laravel plugin For Processing Payments Through Paypal Express Checkout. Can Be Used Independently With Other Applications.", "keywords": ["http", "rest", "web service", "paypal", "laravel paypal"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Srmklive\\PayPal\\": "src/"}}, "autoload-dev": {"psr-4": {"Srmklive\\PayPal\\Tests\\": "tests/"}}, "require": {"php": ">=7.2|^8.0", "ext-curl": "*", "guzzlehttp/guzzle": "~7.0", "illuminate/support": "~6.0|~7.0|~8.0|~9.0|^10.0", "nesbot/carbon": "~2.0"}, "require-dev": {"phpunit/phpunit": "^8.0|^9.0|^10.0", "symfony/var-dumper": "~5.0"}, "config": {"sort-packages": true}, "extra": {"laravel": {"providers": ["Srmklive\\PayPal\\Providers\\PayPalServiceProvider"], "aliases": {"PayPal": "Srmklive\\PayPal\\Facades\\PayPal"}}}, "minimum-stability": "dev", "prefer-stable": true}