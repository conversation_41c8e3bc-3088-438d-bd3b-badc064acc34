<?php

namespace App\Http\Controllers\Backend;

use App\Enums\KYCStatus;
use App\Http\Controllers\Controller;
use App\Models\Kyc;
use App\Models\User;
use App\Models\UserKyc;
use App\Traits\NotifyTrait;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\Validator;

class KycController extends Controller implements HasMiddleware
{
    use NotifyTrait;

    public static function middleware()
    {
        return [
            new Middleware('permission:kyc-form-manage', ['only' => ['create', 'store', 'show', 'edit', 'update', 'destroy']]),
            new Middleware('permission:kyc-list', ['only' => ['KycPending', 'kycAll', 'KycRejected']]),
            new Middleware('permission:kyc-action', ['only' => ['depositAction', 'actionNow']]),
        ];
    }

    public function index()
    {
        $kycs = Kyc::all();

        return view('backend.kyc.index', ['kycs' => $kycs]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|unique:kycs,name',
            'status' => 'required',
            'fields' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back();
        }

        try {
            $data = [
                'name' => $request->name,
                'status' => $request->status,
                'fields' => json_encode($request->fields),
            ];

            $kyc = Kyc::create($data);

            if ($request->boolean('unverified_confirmation')) {
                $this->markAsUnverified();
            }

            $status = 'success';
            $message = $kyc->name.' '.__('KYC added successfully!');

            notify()->$status($message, $status);

            return redirect()->route('admin.kyc-form.index');
        } catch (\Exception $exception) {
            $status = 'error';
            $message = __('Sorry! Something went wrong.');

            notify()->$status($message, $status);

            return back();
        }
    }

    protected function markAsUnverified()
    {
        User::where('kyc', 1)->update([
            'kyc' => 0,
        ]);
    }

    public function create()
    {
        return view('backend.kyc.create');
    }

    public function show(Kyc $kyc)
    {
        return view('backend.kyc.edit', ['kyc' => $kyc]);
    }

    public function edit($id)
    {
        $kyc = Kyc::find($id);

        return view('backend.kyc.edit', ['kyc' => $kyc]);
    }

    public function destroy($id)
    {
        try {
            Kyc::find($id)->delete();

            $status = 'success';
            $message = __('KYC deleted successfully!');
        } catch (\Exception $exception) {
            $status = 'error';
            $message = __('Sorry! Something went wrong.');
        }

        notify()->error($message, $status);

        return redirect()->route('admin.kyc-form.index');
    }

    public function KycPending(Request $request)
    {
        $perPage = $request->perPage ?? 15;
        $search = $request->search ?? null;
        $status = $request->status ?? null;

        $kycs = User::where('kyc', KYCStatus::Pending->value)
            ->search($search)
            ->when(in_array(request('sort_field'), ['updated_at', 'username', 'status']), function ($query) {
                $query->orderBy(request('sort_field'), request('sort_dir'));
            })
            ->status($status)
            ->latest('updated_at')
            ->paginate($perPage);

        return view('backend.kyc.pending', ['kycs' => $kycs]);
    }

    public function KycRejected(Request $request)
    {
        $perPage = $request->perPage ?? 15;
        $search = $request->search ?? null;
        $status = $request->status ?? null;

        $kycs = User::where('kyc', KYCStatus::Failed->value)
            ->search($search)
            ->status($status)
            ->when(in_array(request('sort_field'), ['updated_at', 'username', 'status']), function ($query) {
                $query->orderBy(request('sort_field'), request('sort_dir'));
            })
            ->latest('updated_at')
            ->paginate($perPage);

        return view('backend.kyc.rejected', ['kycs' => $kycs]);
    }

    public function kycData($id)
    {
        $user = User::find($id);
        $kycs = UserKyc::where('user_id', $user->id)->where('status', '!=', 'pending')->latest()->get();
        $waiting_kycs = UserKyc::where('user_id', $user->id)->where('status', 'pending')->get();

        $kycStatus = $user->kyc;

        return view('backend.kyc.include.__kyc_data', ['kycs' => $kycs, 'id' => $id, 'waiting_kycs' => $waiting_kycs, 'kycStatus' => $kycStatus])->render();
    }

    public function actionNow(Request $request)
    {
        try {
            $userKyc = UserKyc::find($request->integer('id'));

            $kycCount = Kyc::where('status', true)->count();
            $approvedKyc = UserKyc::where('user_id', $userKyc->user_id)->where('status', 'approved')->where('is_valid', true);

            $userKyc->message = $request->get('message');
            $userKyc->status = $request->status;
            $userKyc->is_valid = $request->status == 'approved';
            $userKyc->save();

            $user = User::find($userKyc->user_id);
            $pendingKycs = UserKyc::where('user_id', $userKyc->user_id)->where('status', 'pending')->where('is_valid', true)->count();

            $userKycStatus = KYCStatus::Pending;

            if ($kycCount == $approvedKyc->count() && $pendingKycs == 0) {
                $userKycStatus = KYCStatus::Verified;
            } elseif ($pendingKycs == 0) {
                $userKycStatus = KYCStatus::Failed;
            }

            $user->update([
                'kyc' => $userKycStatus,
            ]);

            $shortcodes = [
                '[[full_name]]' => $user->full_name,
                '[[status]]' => $request->status,
                '[[kyc_status_link]]' => route('user.setting.index', ['type' => 'kyc']),
                '[[site_title]]' => setting('site_title', 'global'),
            ];

            if ($kycCount == $approvedKyc->count() || $pendingKycs == 0) {
                $this->sendNotify($user->email, 'kyc_action', 'User', $shortcodes, $user->phone, $user->id, route('user.setting.index', ['type' => 'kyc']));
            }

            $status = 'success';
            $message = __('KYC Updated Successfully');

            notify()->$status($message, $status);

            return redirect()->route('admin.kyc.all');
        } catch (\Exception $exception) {
            $status = 'warning';
            $message = __('something is wrong: ').$exception->getMessage();

            notify()->$status($message, $status);

            return back();
        }
    }

    public function update(Request $request, $id)
    {

        $input = $request->all();
        $validator = Validator::make($input, [
            'name' => 'required|unique:kycs,name,'.$id,
            'status' => 'required',
            'fields' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return redirect()->back();
        }

        $data = [
            'name' => $input['name'],
            'status' => $input['status'],
            'fields' => json_encode($input['fields']),
        ];

        $kyc = Kyc::find($id);
        $kyc->update($data);

        notify()->success($kyc->name.' '.__(' KYC Updated'));

        return redirect()->route('admin.kyc-form.index');
    }

    public function kycAll(Request $request)
    {
        $perPage = $request->perPage ?? 15;
        $search = $request->search ?? null;
        $status = $request->status ?? 'all';

        $kycs = User::query()
            ->has('kycs')
            ->when(in_array(request('sort_field'), ['updated_at', 'username', 'status']), function ($query) {
                $query->orderBy(request('sort_field'), request('sort_dir'));
            })
            ->search($search)
            ->status($status)
            ->latest('updated_at')
            ->paginate($perPage);

        return view('backend.kyc.all', ['kycs' => $kycs]);
    }
}
