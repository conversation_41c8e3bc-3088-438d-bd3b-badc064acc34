<?php

namespace App\Http\Controllers\Backend;

use App\Enums\CoinStatus;
use App\Enums\CurrencyType;
use App\Http\Controllers\Controller;
use App\Models\Coin;
use App\Traits\ImageUpload;
use Illuminate\Http\Request;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CoinController extends Controller implements HasMiddleware
{
    use ImageUpload;

    public static function middleware()
    {
        new Middleware('permission:currency-manage', ['only' => ['index']]);
        new Middleware('permission:currency-create', ['only' => ['create', 'store']]);
        new Middleware('permission:currency-edit', ['only' => ['edit', 'update']]);
        new Middleware('permission:currency-delete', ['only' => ['delete']]);
    }

    public function index()
    {
        $currencies = Coin::latest()->paginate(10);

        return view('backend.coin.index', ['currencies' => $currencies]);
    }

    public function create()
    {
        return view('backend.coin.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'code' => 'required|uppercase|unique:coins,code',
            'symbol' => 'required|unique:coins,symbol',
            'icon' => 'required|mimes:png,jpg,jpeg,gif,webp',
            'status' => 'required',
            'conversion_rate' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back()->withErrors($validator)->withInput();
        }

        try {

            DB::beginTransaction();

            $currency = [
                'name' => $request->name,
                'code' => $request->code,
                'symbol' => $request->symbol,
                'type' => CurrencyType::Crypto,
                'status' => $request->status === 'active' ? CoinStatus::Active : CoinStatus::Inactive,
                'conversion_rate' => $request->conversion_rate,
            ];

            if ($request->hasFile('icon')) {
                $currency['icon'] = self::imageUploadTrait($request->icon, folderPath: 'currency');
            }

            Coin::create($currency);

            DB::commit();

            notify()->success(__('Coin created successfully!'));

            return redirect()->route('admin.coin.index');
        } catch (\Throwable $throwable) {
            DB::rollBack();
            notify()->error(__('Sorry! Something went wrong. Please try again.'));

            return back();
        }
    }

    public function edit($id)
    {
        $currencyInfo = Coin::findOrFail($id);

        return view('backend.coin.edit', ['currencyInfo' => $currencyInfo]);
    }

    public function update(Request $request, $id)
    {

        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'code' => 'required|uppercase|unique:coins,code,'.$id,
            'symbol' => 'required|unique:coins,symbol,'.$id,
            'icon' => 'nullable|mimes:png,jpg,jpeg,gif,webp',
            'status' => 'required',
            'conversion_rate' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back()->withErrors($validator)->withInput();
        }

        try {

            DB::beginTransaction();

            $currency = Coin::findOrFail($id);
            $currency->name = $request->name;
            $currency->code = $request->code;
            $currency->symbol = $request->symbol;
            $currency->status = $request->status === 'active' ? CoinStatus::Active : CoinStatus::Inactive;
            $currency->conversion_rate = $request->conversion_rate;

            if ($request->hasFile('icon')) {

                if (file_exists($currency->icon)) {
                    self::fileDelete($currency->icon);
                }

                $image_url = self::imageUploadTrait($request->icon, folderPath: 'currency');
                $currency->icon = $image_url;
            }

            $currency->save();

            DB::commit();

            notify()->success(__('Coin updated successfully!'));

            return redirect()->route('admin.coin.index');
        } catch (\Throwable $throwable) {
            DB::rollBack();
            notify()->error(__('Sorry! Something went wrong.'));

            return back();
        }
    }

    public function delete($id)
    {
        $currency = Coin::withCount('miners')->findOrFail($id);

        if ($currency->miners_count > 0) {
            notify()->error(__('Coin is used in miner. You can not delete it!'));

            return back();
        }

        try {

            DB::beginTransaction();

            if ($currency->icon !== null) {
                self::fileDelete($currency->icon);
            }

            $currency->delete();

            DB::commit();

            notify()->success(__('Coin deleted successfully!'));

            return back();
        } catch (\Throwable $throwable) {
            DB::rollBack();

            throw $throwable;
            notify()->error(__('Sorry! Something went wrong. Please try again.'));

            return back();
        }
    }
}
