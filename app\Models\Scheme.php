<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Scheme extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $casts = [
        'features' => 'json',
        'holidays' => 'json',
        'is_featured' => 'boolean',
    ];

    public function schedule()
    {
        return $this->hasOne(Schedule::class, 'id', 'return_period');
    }

    public function miner()
    {
        return $this->belongsTo(Miner::class, 'miner_id');
    }

    /**
     * Scope a query to only include active
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function userMining()
    {
        return $this->hasMany(UserMining::class,'scheme_id');
    }
}
