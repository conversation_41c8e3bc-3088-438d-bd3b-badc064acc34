<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckDeactivation
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $guard = 'web'): Response
    {
        $user = Auth::guard($guard);
        if ($user->check() && $user->user()->status == 0) {
            $user->logout();

            return $this->redirectTo($request)->withErrors([
                'email' => __('Your account is disabled. Please contact our support').' '.setting('support_email', 'global'),
            ]);
        }

        return $next($request);
    }

    public function redirectTo($request)
    {
        return match (true) {
            str_contains($request->path(), 'merchant') => to_route('merchant.login'),
            str_contains($request->path(), 'agent') => to_route('agent.login'),
            default => to_route('login'),
        };
    }
}
