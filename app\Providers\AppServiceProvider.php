<?php

namespace App\Providers;

use App\Traits\NotifyTrait;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    use NotifyTrait;

    public function register(): void
    {
        Paginator::defaultView('frontend::user._include._pagination');
    }

    public function boot(): void
    {
        // Implicitly grant "Super Admin" role all permissions
        Gate::before(function ($user, $ability) {
            return $user->hasRole('Super-Admin') ? true : null;
        });

        // Notify
        $this->app->singleton('notify', function () {
            return new \App\Facades\Notification\Notify;
        });

        $this->configureVerifyEmail();
    }

    protected function configureVerifyEmail()
    {
        VerifyEmail::toMailUsing(function ($notifiable) {
            $guardRouteMap = [
                'merchant' => 'merchant.verification.verify',
                'agent' => 'agent.verification.verify',
                'user' => 'verification.verify',
            ];

            $userType = strtolower($notifiable->role);
            $routeName = $guardRouteMap[$userType] ?? 'verification.verify';

            $verifyUrl = URL::temporarySignedRoute(
                $routeName,
                Carbon::now()->addMinutes(config('auth.verification.expire', 60)),
                [
                    'id' => $notifiable->getKey(),
                    'hash' => sha1($notifiable->getEmailForVerification()),
                ]
            );

            $shortcodes = [
                '[[token]]' => $verifyUrl,
                '[[full_name]]' => $notifiable->full_name,
                '[[site_title]]' => setting('site_title', 'global'),
                '[[site_url]]' => route('home'),
            ];

            return $this->sendNotify(
                $notifiable->email,
                'email_verification',
                'User',
                $shortcodes,
                $notifiable->phone,
                $notifiable->id,
                $verifyUrl
            );
        });
    }
}
