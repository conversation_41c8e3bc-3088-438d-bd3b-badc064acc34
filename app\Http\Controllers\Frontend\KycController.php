<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\KYCStatus;
use App\Http\Controllers\Controller;
use App\Models\Kyc;
use App\Models\User;
use App\Models\UserKyc;
use App\Traits\ImageUpload;
use App\Traits\NotifyTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class KycController extends Controller
{
    use ImageUpload;
    use NotifyTrait;

    public function __construct()
    {
        abort_if(! setting('kyc_verification', 'permission'), 404);
    }

    public function kycDetails(Request $request)
    {
        $kyc = UserKyc::find($request->id);

        return response()->json([
            'html' => view('frontend::user.kyc.include.__kyc_details_modal', ['kyc' => $kyc])->render(),
        ]);
    }

    public function kycSubmission($id)
    {
        $kyc = Kyc::findOrFail(decrypt($id));

        return view('frontend::user.kyc.submission', ['kyc' => $kyc]);
    }

    public function kycData($id)
    {
        $fields = Kyc::find($id)->fields;

        return view('frontend::user.kyc.data', ['fields' => $fields])->render();
    }

    public function submit(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'kyc_id' => 'required',
            'kyc_credential' => 'required',
        ]);

        if ($validator->fails()) {
            notify()->error($validator->errors()->first());

            return back();
        }

        $kyc = Kyc::find(decrypt($request->kyc_id));

        $user = User::find(Auth::user()->id);

        $newKycs = $request->kyc_credential;

        foreach ($newKycs as $key => $value) {

            if (is_file($value)) {
                $newKycs[$key] = self::imageUploadTrait($value);
            }
        }

        UserKyc::create([
            'user_id' => $user->id,
            'kyc_id' => $kyc->id,
            'type' => $kyc->name,
            'data' => $newKycs,
            'is_valid' => true,
            'status' => 'pending',
        ]);

        $pendingCount = UserKyc::where('user_id', $user->id)->whereIn('status', ['pending', 'approved'])->where('is_valid', true)->count();
        $isPending = Kyc::where('status', true)->count() == $pendingCount;

        $user->update([
            'kyc' => KYCStatus::Pending,
        ]);

        if ($isPending) {
            $shortcodes = [
                '[[full_name]]' => $user->full_name,
                '[[kyc_type]]' => $kyc->name,
                '[[submitted_at]]' => now(),
                '[[kyc_status_link]]' => route('admin.kyc.pending'),
                '[[site_title]]' => setting('site_title', 'global'),
            ];

            $this->sendNotify(setting('site_email', 'global'), 'admin_kyc_request', 'Admin', $shortcodes, $user->phone, $user->id, route('admin.kyc.pending'));
        }

        notify()->success(__('Kyc has been submitted.'));

        return to_route('user.setting.index', ['type' => 'kyc']);
    }
}
