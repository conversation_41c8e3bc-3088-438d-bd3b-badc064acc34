<?php

namespace Razorpay\Tests;

require_once realpath(__DIR__.'/../vendor/autoload.php');

use Dotenv\Dotenv;
use Razorpay\Api\Api;

if (class_exists('Dotenv')) {
    $dotenv = Dotenv::createImmutable(__DIR__);
    $dotenv->load();
}

class PartnerTest extends TestCase
{
    protected $instance;

    protected static $account_id;

    protected static $stakeholder_id;

    protected static $product_id;

    protected static $webhook_id;

    protected function setUp(): void
    {
        $apiKey = getenv('RAZORPAY_API_KEY') ? getenv('RAZORPAY_PARTNER_API_KEY') : '';
        $apiSecret = getenv('RAZORPAY_API_SECRET') ? getenv('RAZORPAY_PARTNER_API_SECRET') : '';
        $this->instance = new Api($apiKey, $apiSecret);
    }

    public function test_create_account()
    {
        $account = $this->instance->account->create($this->accountAttributes());

        self::$account_id = $account['id'];

        $this->assertTrue(is_array($account->toArray()));
        $this->assertTrue(is_array((array) $account['legal_info']));
    }

    public function test_create_stakerholder()
    {
        $stakeholder = $this->instance->account->fetch(self::$account_id)->stakeholders()->create($this->stakeholderAttributes());

        $this->assertTrue(is_array($stakeholder->toArray()));

        self::$stakeholder_id = $stakeholder['id'];

        $this->assertTrue(is_array((array) $stakeholder['phone']));
    }

    public function test_fetch_stakerholder()
    {
        $stakeholder = $this->instance->account->fetch(self::$account_id)->stakeholders()->fetch(self::$stakeholder_id);

        $this->assertTrue(is_array($stakeholder->toArray()));

        $this->assertTrue(is_array((array) $stakeholder['phone']));
    }

    public function test_fetch_all_stakerholder()
    {
        $stakeholder = $this->instance->account->fetch(self::$account_id)->stakeholders()->all();

        $this->assertTrue(is_array($stakeholder->toArray()));

        $this->assertTrue(is_array((array) $stakeholder['items']));
    }

    public function test_edit_stakerholder()
    {
        $stakeholder = $this->instance->account->fetch(self::$account_id)->stakeholders()->edit(self::$stakeholder_id, $this->stakeholderAttributes());

        $this->assertTrue(is_array($stakeholder->toArray()));

        $this->assertTrue(is_array((array) $stakeholder['phone']));
    }

    public function test_product_configuration()
    {
        $product = $this->instance->account->fetch(self::$account_id)->products()->requestProductConfiguration([
            'product_name' => 'payment_gateway',
            'tnc_accepted' => true,
            'ip' => '***************',
        ]);
        self::$product_id = $product['id'];
        $this->assertTrue(is_array($product->toArray()));
        $this->assertTrue(is_array((array) $product['requirements']));
    }

    public function test_product_fetch()
    {
        $product = $this->instance->account->fetch(self::$account_id)->products()->fetch(self::$product_id);
        $this->assertTrue(is_array($product->toArray()));
        $this->assertTrue(is_array((array) $product['requirements']));
    }

    public function test_product_edit()
    {
        $product = $this->instance->account->fetch(self::$account_id)->products()->edit(self::$product_id, [
            'tnc_accepted' => true,
            'ip' => '***************',
        ]);

        $this->assertTrue(is_array($product->toArray()));
    }

    public function test_fetch_tnc()
    {
        $webhook = $this->instance->product->fetchTnc('payments');

        $this->assertTrue(is_array($webhook->toArray()));
    }

    public function test_webhook_create()
    {
        $webhook = $this->instance->account->fetch(self::$account_id)->webhooks()->create($this->webhookAttributes());
        self::$webhook_id = $webhook['id'];

        $this->assertTrue(is_array($webhook->toArray()));

        $this->assertTrue(is_array((array) $webhook['id']));
    }

    public function test_webhook_fetch()
    {
        $webhook = $this->instance->account->fetch(self::$account_id)->webhooks()->fetch(self::$webhook_id);

        $this->assertTrue(is_array($webhook->toArray()));

        $this->assertTrue(is_array((array) $webhook['id']));
    }

    public function test_fetch_all_webhook()
    {
        $webhook = $this->instance->account->fetch(self::$account_id)->webhooks()->all();

        $this->assertTrue(is_array($webhook->toArray()));

        $this->assertTrue(is_array((array) $webhook['items']));
    }

    public function test_edit_webhook()
    {
        $webhook = $this->instance->account->fetch(self::$account_id)->webhooks()->edit([
            'url' => 'https://www.linkedin.com',
            'events' => ['refund.created'],
        ], self::$webhook_id);

        $this->assertTrue(is_array($webhook->toArray()));

        $this->assertTrue(is_array((array) $webhook['events']));
    }

    public function test_delete_webhook()
    {
        $webhook = $this->instance->account->fetch(self::$account_id)->webhooks()->delete(self::$webhook_id);

        $this->assertTrue(is_array($webhook->toArray()));
    }

    public function test_delete_account()
    {
        $account = $this->instance->account->fetch(self::$account_id)->delete();

        $this->assertTrue(is_array($account->toArray()));

        $this->assertTrue(is_array((array) $account['legal_info']));
    }

    public function test_fetch_account()
    {
        $account = $this->instance->account->fetch(self::$account_id);

        $this->assertTrue(is_array($account->toArray()));

        $this->assertTrue(is_array((array) $account['legal_info']));
    }

    public function test_edit_account()
    {
        $request = ['customer_facing_business_name' => 'Ltd'];

        $account = $this->instance->account->fetch(self::$account_id)->edit($request);

        $this->assertTrue(is_array($account->toArray()));

        $this->assertTrue(is_array((array) $account['legal_info']));
    }

    public function accountAttributes()
    {
        return $arrayVar = [
            'email' => 'gauriagain'.time().'@example.org',
            'phone' => '**********',
            'legal_business_name' => 'Acme Corp',
            'business_type' => 'partnership',
            'customer_facing_business_name' => 'Example',
            'profile' => [
                'category' => 'healthcare',
                'subcategory' => 'clinic',
                'description' => 'Healthcare E-commerce platform',
                'addresses' => [
                    'operation' => [
                        'street1' => '507, Koramangala 6th block',
                        'street2' => 'Kormanagala',
                        'city' => 'Bengaluru',
                        'state' => 'Karnataka',
                        'postal_code' => 560047,
                        'country' => 'IN',
                    ],
                    'registered' => [
                        'street1' => '507, Koramangala 1st block',
                        'street2' => 'MG Road',
                        'city' => 'Bengaluru',
                        'state' => 'Karnataka',
                        'postal_code' => 560034,
                        'country' => 'IN',
                    ],
                ],
                'business_model' => 'Online Clothing ( men, women, ethnic, modern ) fashion and lifestyle, accessories, t-shirt, shirt, track pant, shoes.',
            ],
            'legal_info' => ['pan' => '**********', 'gst' => '18AABCU9603R1ZM'],
            'brand' => ['color' => 'FFFFFF'],
            'notes' => ['internal_ref_id' => '123123'],
            'contact_name' => 'Gaurav Kumar',
            'contact_info' => [
                'chargeback' => ['email' => '<EMAIL>'],
                'refund' => ['email' => '<EMAIL>'],
                'support' => [
                    'email' => '<EMAIL>',
                    'phone' => '9999999998',
                    'policy_url' => 'https://www.google.com',
                ],
            ],
            'apps' => [
                'websites' => ['https://www.example.org'],
                'android' => [['url' => 'playstore.example.org', 'name' => 'Example']],
                'ios' => [['url' => 'appstore.example.org', 'name' => 'Example']],
            ],
        ];
    }

    public function stakeholderAttributes()
    {
        return $arrayVar = [
            'percentage_ownership' => 10,
            'name' => 'Gaurav Kumar',
            'email' => 'gaurav'.time().'.<EMAIL>',
            'relationship' => ['director' => true, 'executive' => false],
            'phone' => ['primary' => '7474747474', 'secondary' => '7474747474'],
            'addresses' => [
                'residential' => [
                    'street' => '506, Koramangala 1st block',
                    'city' => 'Bengaluru',
                    'state' => 'Karnataka',
                    'postal_code' => '560034',
                    'country' => 'IN',
                ],
            ],
            'kyc' => ['pan' => '**********'],
            'notes' => ['random_key_by_partner' => 'random_value'],
        ];
    }

    public function webhookAttributes()
    {
        return $arrayVar = [
            'url' => 'https://google.com',
            'alert_email' => '<EMAIL>',
            'secret' => '12345',
            'events' => [
                'payment.authorized',
                'payment.failed',
                'payment.captured',
                'payment.dispute.created',
                'refund.failed',
                'refund.created',
            ],
        ];
    }
}
