@use '../utils' as *;

/*----------------------------------------*/
/* Basic pagination styles
/*----------------------------------------*/
.td-pagination {
	ul {
		@include flexbox();
		align-items: center;
		gap: 10px 10px;
		flex-wrap: wrap;

		@media #{$xs,$sm,$md} {
			justify-content: start;
		}

		li {
			list-style: none;

			.clip-path {
				position: relative;
				display: inline-block;
				padding: 1px;

				&::before,
				&::after {
					position: absolute;
					inset-inline-start: 0;
					top: 0;
					transition: all .3s;
					width: 100%;
					height: 100%;
					background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
					content: "";
					clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
					border-radius: 2px;
				}

				&::after {
					background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
					opacity: 0;
					visibility: hidden;
				}

				&:hover {
					@include dark-theme {
						&::after {
							opacity: 1;
							visibility: visible;
						}
					}

					&::after {
						opacity: 1;
						visibility: visible;
					}
				}
			}

			a {
				width: 40px;
				height: 40px;
				@include inline-flex();
				align-items: center;
				justify-content: center;
				position: relative;
				inset-inline-end: 0;
				top: 50%;
				font-size: 16px;
				line-height: 20px;
				font-weight: 600;
				position: relative;
				display: inline-flex;
				flex-wrap: wrap;
				z-index: 3;
				background: #f6f6f6;
				clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
				gap: 12px;
				padding: 6px;
				color: var(--td-text-primary);

				@include dark-theme {
					background: var(--td-liberty-blue);
				}

				&:after {
					position: absolute;
					content: "";
					height: 100%;
					width: 100%;
					top: 0;
					inset-inline-start: 0;
					background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
					color: var(--td-white);
					z-index: -1;
					@include border-radius(4px);
					opacity: 0;
				}

				i,
				iconify-icon {
					font-size: 20px;
				}
			}

			.current {
				&::before {
					opacity: 0;
				}

				a {
					border: 0;
					color: var(--td-white);

					&::after {
						opacity: 1;
					}
				}
			}

			&.disabled {

				i,
				iconify-icon {
					color: rgba($heading, $alpha: 0.1);

					@include dark-theme {
						color: rgba($white, $alpha: 0.1);
					}
				}

				.clip-path {
					a {
						color: rgba($white, $alpha: 0.5);
						cursor: default;
					}

					&:hover {
						&::after {
							display: none;
						}
					}
				}
			}
		}
	}
}

.td-pagination-two {
	ul {
		@include flexbox();
		align-items: center;
		gap: 10px 10px;
		flex-wrap: wrap;

		@media #{$xs,$sm,$md} {
			justify-content: start;
		}

		li {
			list-style: none;

			.clip-path {
				position: relative;
				display: inline-block;
				padding: 1px;

				&::before,
				&::after {
					position: absolute;
					inset-inline-start: 0;
					top: 0;
					transition: all .3s;
					width: 100%;
					height: 100%;
					background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
					content: "";
					clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
					border-radius: 2px;
				}

				&::after {
					background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
					opacity: 0;
					visibility: hidden;
				}

				&:hover {
					&::after {
						opacity: 1;
						visibility: visible;
					}
				}
			}

			a {
				width: 40px;
				height: 40px;
				@include inline-flex();
				align-items: center;
				justify-content: center;
				position: relative;
				inset-inline-end: 0;
				top: 50%;
				font-size: 16px;
				line-height: 20px;
				font-weight: 600;
				position: relative;
				display: inline-flex;
				flex-wrap: wrap;
				z-index: 3;
				background: #dde3fa;
				clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
				gap: 12px;
				padding: 6px;
				color: var(--td-text-primary);

				@include dark-theme {
					background: var(--td-liberty-blue);
				}

				&:after {
					position: absolute;
					content: "";
					height: 100%;
					width: 100%;
					top: 0;
					inset-inline-start: 0;
					background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) -10%, rgba(142, 84, 233, 0.3) 265%);
					color: var(--td-white);
					z-index: -1;
					@include border-radius(4px);
					opacity: 0;
				}

				i,
				iconify-icon {
					font-size: 20px;
				}
			}

			.current {
				&::before {
					opacity: 0;
				}

				a {
					border: 0;
					color: var(--td-white);

					&::after {
						opacity: 1;
					}
				}
			}

			&.disabled {

				i,
				iconify-icon {
					color: rgba($heading, $alpha: 0.1);

					@include dark-theme {
						color: rgba($white, $alpha: 0.1);
					}
				}

				.clip-path {
					a {
						color: rgba($white, $alpha: 0.5);
						cursor: default;
					}

					&:hover {
						&::after {
							display: none;
						}
					}
				}
			}
		}
	}
}