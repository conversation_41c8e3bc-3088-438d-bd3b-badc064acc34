name: TestsV3
on: [push, pull_request]
jobs:
  paypal:
    name: PHP ${{ matrix.php-versions }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        php-versions: ['7.2', '7.3', '7.4', '8.0', '8.1', '8.2']
    steps:
    - name: Checkout
      uses: actions/checkout@v2
    - name: Setup PHP with Composer and extensions
      with:
        php-version: ${{ matrix.php-versions }}
      uses: shivammathur/setup-php@v2
    - name: Get Composer cache directory
      id: composercache
      run: echo "::set-output name=dir::$(composer config cache-files-dir)"
    - name: Cache Composer dependencies
      uses: actions/cache@v2
      with:
        php-version: ${{ matrix.php-versions }}
        path: ${{ steps.composercache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-
    - name: Install Composer dependencies
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run: composer install --no-progress --prefer-dist --optimize-autoloader $(if [ "$PHP_VERSION" == "8.0" || "$PHP_VERSION" == "8.1" ]; then echo "--ignore-platform-reqs"; fi;)
    - name: Run tests with code coverage
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run: vendor/bin/phpunit $(if [ "$PHP_VERSION" == "7.2" ]; then echo "-c phpunit.xml.dist.php72"; fi;) $(if [ "$PHP_VERSION" == "7.3" ]; then echo "-c phpunit.xml.dist.php8"; fi;) $(if [ "$PHP_VERSION" == "7.4" ]; then echo "-c phpunit.xml.dist.php8"; fi;) $(if [ "$PHP_VERSION" == "8.0" ]; then echo "-c phpunit.xml.dist.php8"; fi;) --coverage-clover build/logs/clover.xml
    - name: Install PHP Coveralls library
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
      run:
        composer global require --dev php-coveralls/php-coveralls
    - name: Upload coverage results to Coveralls
      env:
        PHP_VERSION: ${{ matrix.php-versions }}
        COVERALLS_REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        COVERALLS_PARALLEL: true
        COVERALLS_FLAG_NAME: php-${{ matrix.php-versions }}
      run:
        php-coveralls -v
  coveralls-finish:
    needs: [paypal]
    runs-on: ubuntu-latest
    steps:
    - name: Coveralls Finished
      uses: coverallsapp/github-action@master
      with:
        github-token: ${{ secrets.github_token }}
        parallel-finished: true
